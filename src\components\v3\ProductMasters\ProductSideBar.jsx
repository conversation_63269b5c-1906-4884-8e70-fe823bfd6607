import {
  BarcodeOutlined,
  CalendarOutlined,
  CommentOutlined,
  DollarOutlined,
  DownloadOutlined,
  EyeOutlined,
  HddOutlined,
  IdcardOutlined,
  PercentageOutlined,
  ShoppingOutlined,
  TagOutlined,
  WarningOutlined,
} from '@ant-design/icons';
import { Button, Collapse, Table, Tag, Tooltip } from 'antd';
import { IndianRupee } from 'lucide-react';
import { useEffect, useState } from 'react';
import { toast } from 'react-toastify';
import {
  downloadMedia,
  getLocalDate,
  getLocalDateTime,
} from '../../../helperFunction';
import { useGetSelectedProductsMutation } from '../../../slices/productApiSlice';
import { useGetStoresQuery } from '../../../slices/storeApiSlice';
import RightSidebar from '../../global/components/RightSidebar';
import PreviewImgPdfFullscreen from '../../salesOrder/PreviewImgPdfFullscreen';

const NestedProductTable = ({ initialData }) => {
  const [expandedRows, setExpandedRows] = useState({});
  const [nestedData, setNestedData] = useState({});
  const [loading, setLoading] = useState({});
  const [getSelectedProducts] = useGetSelectedProductsMutation();

  // Transform data to avoid antd's automatic children handling
  const transformData = (data) => {
    return data?.map((item) => {
      const { children, ...rest } = item;
      return {
        ...rest,
        hasChildren: children?.length > 0,
        childrenIds: children,
        key: item._id || item.id,
      };
    });
  };

  const columns = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      render: (text) => <span className="text-sm text-gray-800">{text}</span>,
    },
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      render: (text) => <span className="text-sm text-gray-800">{text}</span>,
    },
    {
      title: 'UOM',
      dataIndex: 'uom',
      key: 'uom',
      render: (text) => <span className="text-sm text-gray-800">{text}</span>,
    },
    {
      title: 'Category',
      dataIndex: 'category',
      key: 'category',
      render: (text) => <span className="text-sm text-gray-800">{text}</span>,
    },
    {
      title: 'Date',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (text) => (
        <span className="text-sm text-gray-800">{getLocalDate(text)}</span>
      ),
    },
  ];

  const handleExpand = async (expanded, record) => {
    const rowKey = record.key;
    if (expanded && record.hasChildren) {
      setLoading((prev) => ({ ...prev, [rowKey]: true }));
      try {
        if (!nestedData[rowKey]) {
          const response = await getSelectedProducts({
            data: { ids: record?.childrenIds },
          });
          setNestedData((prev) => ({
            ...prev,
            [rowKey]: transformData(response.data),
          }));
        }
        setExpandedRows((prev) => ({
          ...prev,
          [rowKey]: true,
        }));
      } catch (error) {
        toast.error('Error fetching children:', error);
      } finally {
        setLoading((prev) => ({ ...prev, [rowKey]: false }));
      }
    } else {
      setExpandedRows((prev) => ({
        ...prev,
        [rowKey]: false,
      }));
    }
  };

  const expandedRowRender = (record) => {
    const childData = nestedData[record.key];
    if (loading[record.key]) {
      return <div className="p-4">Loading...</div>;
    }
    if (!childData) {
      return null;
    }
    return (
      <Table
        columns={columns}
        dataSource={childData}
        pagination={false}
        size="small"
        expandable={{
          expandedRowKeys: Object.keys(expandedRows).filter(
            (key) => expandedRows[key]
          ),
          onExpand: handleExpand,
          expandedRowRender: expandedRowRender,
          rowExpandable: (record) => record?.hasChildren,
        }}
      />
    );
  };

  return (
    <Table
      columns={columns}
      dataSource={transformData(initialData)}
      pagination={false}
      size="small"
      expandable={{
        expandedRowKeys: Object.keys(expandedRows).filter(
          (key) => expandedRows[key]
        ),
        onExpand: handleExpand,
        expandedRowRender: expandedRowRender,
        rowExpandable: (record) => record.hasChildren,
      }}
    />
  );
};

const ProductSideBar = ({ openSideBar, setOpenSideBar, data }) => {
  const { data: stores } = useGetStoresQuery();
  const [getSelectedProducts] = useGetSelectedProductsMutation();
  const [mediaToPreview, setMediaToPreview] = useState(null);
  const [previewMedia, setPreviewMedia] = useState(false);
  const [variantsData, setVariantsData] = useState([]);

  useEffect(() => {
    if (data?.children && data.children.length) {
      getSelectedProducts({ data: { ids: data?.children } }).then((data) =>
        setVariantsData(data?.data)
      );
    }
  }, [data, getSelectedProducts]);

  const matchedStores = (allStoresId) => {
    return stores?.stores?.items?.filter((store) =>
      allStoresId.includes(store._id)
    );
  };

  const renderDetailRow = (label, value, icon, valueClassName = '') => {
    if (label === 'Threshold' && data) {
      const othersThreshold =
        data?.othersThreshold?.map((threshold) => (
          <Tag color={threshold.color} key={threshold.tag} className="px-3">
            <span className="font-semibold">{threshold.value}</span>
            <span className="mx-1">•</span>
            <span>{threshold.tag}</span>
          </Tag>
        )) || [];
      return (
        <div className="flex justify-between py-2 border-b last:border-b-0">
          <span className="flex items-center gap-3 text-gray-600">
            {icon && <span className="text-xl">{icon}</span>}
            <span className="font-medium">{label}</span>
          </span>
          <div>
            <Tag color="blue" className="px-3">
              {data?.quantityThreshold}
            </Tag>
            {othersThreshold.length > 0 && othersThreshold}
          </div>
        </div>
      );
    }
    return (
      <div className="flex justify-between py-2 border-b last:border-b-0">
        <span className="flex items-center gap-3 text-gray-600">
          {icon && <span className="text-xl">{icon}</span>}
          <span className="font-medium">{label}</span>
        </span>
        <span className={`text-sm text-gray-800 ${valueClassName}`}>
          {value || '-'}
        </span>
      </div>
    );
  };

  return (
    <RightSidebar
      openSideBar={openSideBar}
      setOpenSideBar={setOpenSideBar}
      className="overflow-y-auto max-h-screen"
    >
      {previewMedia && (
        <PreviewImgPdfFullscreen
          media={mediaToPreview}
          showPreview={previewMedia}
          setShowPreview={setPreviewMedia}
        />
      )}
      <div className="mb-4">
        <h3 className="text-sm font-semibold text-gray-600 mb-2 uppercase">
          FG Product Details
        </h3>
        <div className="grid gap-6 border rounded-lg p-4">
          {renderDetailRow(
            'Product ID',
            data.id,
            <IdcardOutlined className="text-indigo-500" />
          )}
          {renderDetailRow(
            'Product Name',
            data.name,
            <ShoppingOutlined className="text-green-500" />
          )}
          {renderDetailRow(
            'Date',
            getLocalDateTime(data.createdAt),
            <CalendarOutlined className="text-blue-500" />
          )}
          {renderDetailRow(
            'UOM',
            data?.uom,
            <BarcodeOutlined className="text-pink-500" />
          )}
          {renderDetailRow(
            'Category',
            data?.category,
            <TagOutlined className="text-orange-500" />
          )}
          {renderDetailRow(
            'Valuation',
            data.valuation,
            <DollarOutlined className="text-yellow-500" />
          )}
          {renderDetailRow(
            'Remarks',
            data.remarks,
            <CommentOutlined className="text-gray-500" />
          )}
          {renderDetailRow(
            'Threshold',
            data.threshold,
            <WarningOutlined className="text-red-500" />
          )}
        </div>
      </div>
      <div className="mb-4">
        <h3 className="text-sm font-semibold text-gray-600 mb-2 uppercase">
          Item Details
        </h3>
        <div className="grid gap-6 border rounded-lg p-4">
          {renderDetailRow(
            'CGST',
            `${data?.itemDetails?.cgst || 0}%`,
            <PercentageOutlined className="text-green-500" />
          )}
          {renderDetailRow(
            'SGST',
            `${data?.itemDetails?.sgst || 0}%`,
            <PercentageOutlined className="text-green-500" />
          )}
          {renderDetailRow(
            'IGST',
            `${data?.itemDetails?.igst || 0}%`,
            <PercentageOutlined className="text-green-500" />
          )}
          {renderDetailRow(
            'HSN',
            data?.itemDetails?.hsn,
            <HddOutlined className="text-blue-500" />
          )}
          {renderDetailRow(
            'Rate',
            data?.itemDetails?.rate,
            <IndianRupee className="text-pink-500" />
          )}
        </div>
      </div>
      {data?.additionalUoms?.length > 0 && (
        <Collapse accordion className="mb-4">
          <Collapse.Panel
            header="Additional UOMS Details"
            key="1"
            className="text-sm font-semibold text-gray-600"
          >
            <div className="mb-4">
              <div className="grid gap-6 border rounded-lg p-4">
                <div className="flex justify-between mb-2">
                  <span className="text-xs font-semibold text-gray-500 uppercase">
                    Additional Unit
                  </span>
                  <span className="text-xs font-semibold text-gray-500 uppercase">
                    Conversion Value
                  </span>
                </div>
                <div className="space-y-2">
                  {data.additionalUoms.map((uom) => (
                    <div
                      key={uom._id}
                      className="flex justify-between py-2 border-b last:border-b-0"
                    >
                      <span className="text-sm text-gray-500">
                        {uom.additionalUnit}
                      </span>
                      <span className="text-sm text-gray-800">
                        {`${uom.conversionValue} ${uom.conversionUnit}`}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </Collapse.Panel>
        </Collapse>
      )}
      {data?.stores?.length > 0 && (
        <Collapse accordion className="mb-4">
          <Collapse.Panel
            header="Store Details"
            key="1"
            className="text-sm font-semibold text-gray-600"
          >
            <div className="mb-4">
              <div className="grid gap-6 border rounded-lg p-4">
                <div className="flex justify-between mb-2">
                  <span className="text-xs font-semibold text-gray-500 uppercase">
                    Store ID
                  </span>
                  <span className="text-xs font-semibold text-gray-500 uppercase">
                    Store Name
                  </span>
                </div>
                <div className="space-y-2">
                  {matchedStores(data?.stores || [])?.map((store) => (
                    <div
                      key={store._id}
                      className="flex justify-between py-2 border-b last:border-b-0"
                    >
                      <span className="text-sm text-gray-500">{store.id}</span>
                      <span className="text-sm text-gray-800">
                        {store.name}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </Collapse.Panel>
        </Collapse>
      )}
      <Collapse accordion className="mb-4">
        <Collapse.Panel
          header="Variants Details"
          key="1"
          className="text-sm font-semibold text-gray-600"
        >
          <NestedProductTable initialData={variantsData} />
        </Collapse.Panel>
      </Collapse>
      {data?.attachments?.length > 0 && (
        <Collapse accordion className="mb-4">
          <Collapse.Panel
            header="Attachments"
            key="1"
            className="text-sm font-semibold text-gray-600"
          >
            <div className="mb-4">
              <div className="grid gap-6 border rounded-lg p-4">
                <div className="space-y-2">
                  {data?.attachments?.map((item, idx) => (
                    <div
                      key={`${item.name}-${idx}`}
                      className="flex items-center justify-between py-2 border-b last:border-b-0"
                    >
                      <Tooltip title={item.name}>
                        <span className="text-sm text-gray-500 truncate max-w-[200px]">
                          {item.name.length > 30
                            ? `${item.name.slice(0, 30)}...`
                            : item.name}
                        </span>
                      </Tooltip>
                      <div className="flex space-x-2">
                        {(item.type?.includes('image') ||
                          item.type?.includes('pdf')) && (
                          <Button
                            type="text"
                            icon={<EyeOutlined />}
                            onClick={() => {
                              setMediaToPreview(item);
                              setPreviewMedia(true);
                            }}
                            className="text-blue-500 hover:text-blue-700"
                          >
                            Preview
                          </Button>
                        )}
                        <Button
                          type="text"
                          icon={<DownloadOutlined />}
                          onClick={() => downloadMedia(item)}
                          className="text-green-500 hover:text-green-700"
                        >
                          Download
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </Collapse.Panel>
        </Collapse>
      )}
    </RightSidebar>
  );
};

export default ProductSideBar;

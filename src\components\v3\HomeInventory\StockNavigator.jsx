import { EditOutlined } from '@ant-design/icons';
import { Button, Empty, Input } from 'antd';
import { useEffect, useState } from 'react';
import { useSearchParams } from 'react-router-dom';
import { toast } from 'react-toastify';
import { ReactComponent as Analytics } from '../../../assets/svgs/analytics.svg';
import {
  getPartVariantName,
  getProductVariantName,
  handleCanCreateStockOutOrder,
} from '../../../helperFunction';
import { useLazyQueryPoQuery } from '../../../slices/createPoApiSlice';
import { useGetProductByNameForMasterMutation } from '../../../slices/productApiSlice';
import { InfoTooltip } from '../../global/components/InfoTooltip';
import Modal from '../../global/components/Modal';
import Table from '../../global/components/Table';
import Tooltip from '../../global/components/ToolTip';
import ProductMasterSelector from '../../global/ProductMasterSelector';
import VariantSelector from '../../global/VariantSelector';

const StockNavigator = ({
  _ManualIcon,
  setShowManualEntry,
  handleAddBatch,
  setFirstBatch,
  formData,
  setFormData,
  setUsage,
  getStocks,
  isMobile,
  isTablet,
}) => {
  const [quantityModal, setQuantityModal] = useState(false);
  const [isProductMaster, setIsProductMaster] = useState(false);
  const [addedItems, setAddedItems] = useState([]);
  const [itemQuantities, setItemQuantities] = useState([]);
  const [showPopup, setShowPopup] = useState(false);
  const [selectType, setSelectType] = useState('na');
  const [selectedRows, setSelectedRows] = useState([]);
  const [bomsData, setBomsData] = useState([]);
  const [getPos, { data: poData }] = useLazyQueryPoQuery();
  const [showProducts, setShowProducts] = useState(false);
  const [products, setProducts] = useState();
  const [selectedWo, setSelectedWo] = useState(null);
  const [SearchParams] = useSearchParams();
  const [selectedItem, setSelectedItem] = useState(null);
  const [batchSearchTerm, setBatchSearchTerm] = useState('');

  const [getProductByNameMaster] = useGetProductByNameForMasterMutation();

  const handleRowSelection = (rowId, isSelected) => {
    setSelectedRows((prev) =>
      isSelected ? [...prev, rowId] : prev.filter((id) => id !== rowId)
    );
  };

  useEffect(() => {
    const allRowIds = [];
    addedItems?.forEach((item) => {
      item?.entries?.forEach((entry) => {
        allRowIds.push(entry?._id);
      });
    });
    setSelectedRows(allRowIds);
  }, [addedItems]);

  useEffect(() => {
    getPos({ page: 1, limit: 10, field_value: 'Assembly', field_name: 'type' });
  }, [getPos]);

  useEffect(() => {
    (async () => {
      const workOrderId = SearchParams.get('workorder');
      if (workOrderId) {
        const currentWorkOrder = await getPos({
          page: 1,
          limit: 10,
          field_value: workOrderId,
          field_name: '_id',
        }).unwrap();
        const data = currentWorkOrder?.results?.[0];

        const { resultProductList } = handleCanCreateStockOutOrder(data);
        const raw = data?.requestStore?.map((store) =>
          data?.rawMaterials?.[0]?.rawMaterials?.find(
            (r) => r?.value === store._id
          )
        );
        const rawM = raw?.filter((r) => r !== undefined);
        setSelectedWo(data);
        const e = new Event('submit', { bubbles: true, cancelable: true });
        for (let i = 0; i < resultProductList.length; i++) {
          const item = resultProductList[i];
          if (item?.part?._id) {
            await addStock(item?.part?._id, 'parts', item?.part?.name);
            handleChangeBatchQuantity(item?.units, item?.part?._id);
            await handleSubmitQuantity(e, bomItemQuantities, true);
          } else if (item?.partVariant?._id) {
            await addStock(
              item?.partVariant?._id,
              'partVariants',
              getPartVariantName(item?.partVariant)
            );
            handleChangeBatchQuantity(item?.units, item?.partVariant?._id);
            await handleSubmitQuantity(e, bomItemQuantities, true);
          } else if (item?.productVariant) {
            await addStock(
              item?.productVariant?._id,
              'productVariants',
              getProductVariantName(item?.productVariant)
            );
            handleChangeBatchQuantity(item?.units, item?.productVariant?._id);
            await handleSubmitQuantity(e, bomItemQuantities, true);
          }
          for (let i = 0; i < rawM.length; i++) {
            const part = rawM?.[i]?.part;
            const type = rawM?.[i]?.partType;
            const units = rawM?.[i]?.units;
            if (part && type === 'Part') {
              await addStock(part?._id, 'parts', part?.name);
              handleChangeBatchQuantity(units, part?._id);
              await handleSubmitQuantity(e, bomItemQuantities, true);
            }
          }
        }
        setQuantityModal(false);
        setIsProductMaster(false);
      }
    })();
  }, [SearchParams]); //eslint-disable-line

  useEffect(() => {
    setBomsData(
      poData?.results?.map((data) => {
        const bomWithOrderQuantity = {
          bom: data?.bom,
          orderQuantity: data?.orderQuantity,
          name: data?.name,
          id: data?.workOrderId,
          type: data?.type,
          _id: data?._id,
          orders: data?.orders,
          requestStore: data?.requestStore,
        };
        return bomWithOrderQuantity;
      })
    );
  }, [poData]);

  let bomAddedItems = [];
  const addStock = async (id, type, name) => {
    // checking if the item already exists in formdata or not
    const existsinFormdata = formData?.items?.find((formdataitem) => {
      const item =
        formdataitem?.part ||
        formdataitem?.product ||
        formdataitem?.subAssembly ||
        formData?.partVariant ||
        formData?.productVariant;
      return item?._id === id;
    });
    if (existsinFormdata) {
      return toast.warn(`${type} ${name} already added`);
    }
    // const exists = addedItems?.find((elem) => elem?.item?._id === id);
    // Check that any item exists in tempAdditem
    // const exists = tempAddItem?.find((elem) => elem?.item?._id === id);
    // // if (exists && selectType === 'bom') {
    // //   return;
    // // } else
    // if (exists) {
    //   toast.warn(`${type} ${name} already added`, {
    //     position: toast.POSITION.TOP_RIGHT,
    //   });
    //   return;
    // }
    const res = await getStocks({
      id,
      type,
      storeId: formData?.store,
    }).unwrap();

    setAddedItems(() => [
      {
        item: res?.item,
        entries:
          res?.entries?.map((e) => ({
            ...e,
            quantity: 0,
            passed: false,
          })) || [],
      },
    ]);
    bomAddedItems = [
      {
        item: res?.item,
        entries:
          res?.entries?.map((e) => ({
            ...e,
            quantity: 0,
            passed: false,
          })) || [],
      },
    ];
    setQuantityModal(true);
  };

  let bomItemQuantities;

  const handleChangeBatchQuantity = (quantity, itemId) => {
    setItemQuantities((prev) =>
      prev?.find((item) => item?.itemId === itemId)
        ? prev?.map((item) => {
            if (item?.itemId === itemId) return { ...item, quantity };
            return item;
          })
        : [...prev, { quantity, itemId }]
    );

    bomItemQuantities = [...itemQuantities];
    const index = bomItemQuantities.findIndex((item) => item.itemId === itemId);

    if (index !== -1) {
      const currentItem = bomItemQuantities[index];
      currentItem.quantity =
        selectType === 'bom' ? quantity + currentItem.quantity : quantity;
    } else {
      bomItemQuantities.push({ quantity, itemId });
    }
  };

  const _handleBomClick = async () => {
    setShowPopup(true);
    setSelectType('bom');
  };

  const [bomIdList, setBomIdList] = useState([]);

  const handleSubmitQuantity = async (e, newItemQuantities, isBom) => {
    e.preventDefault();
    if (selectedRows?.length === 0) {
      toast.error('Please select at least one row');
      return;
    }
    // setting addeditem to tempAdditem to fix the warning status in addStock() where checking existing items
    // setTempAddItem(addedItems);
    let updatedItems;
    for (let itemQuantity of itemQuantities.length > 0
      ? itemQuantities
      : newItemQuantities) {
      let temp =
        selectType === 'bom' || isBom
          ? bomAddedItems?.find((item) => {
              return item?.item?._id === itemQuantity?.itemId;
            })
          : addedItems?.find((item) => {
              return item?.item?._id === itemQuantity?.itemId;
            });
      let entries = temp?.entries?.filter((entry) =>
        selectedRows.includes(entry?._id)
      );
      let quantity = itemQuantity?.quantity;

      entries = entries?.map((entry) => {
        if (entry?.remainingQuantity < quantity) {
          quantity -= entry?.remainingQuantity;
          return { ...entry, quantity: entry?.remainingQuantity };
        }

        const copy = quantity;
        quantity = 0;
        return { ...entry, quantity: copy };
      });
      updatedItems =
        selectType === 'bom' || isBom
          ? bomAddedItems?.map((item) => {
              if (item?.item?._id === itemQuantity?.itemId) {
                return { ...item, entries };
              }
              return item;
            })
          : addedItems?.map((item) => {
              if (item?.item?._id === itemQuantity?.itemId) {
                return { ...item, entries };
              }
              return item;
            });

      setFirstBatch(updatedItems[0].entries[0]);
      if (handleAddBatch) handleAddBatch(updatedItems);
    }
    if (selectType !== 'bom') {
      setItemQuantities([]);
      // setAddedItems([]);
    }
    setQuantityModal(false);
    setIsProductMaster(false);
    setShowPopup(false);
    setSelectedItem(null);
    setBatchSearchTerm('');
  };

  // const [partWiseWorkOrderList, setPartWiseWorkOrderList] = useState([]);

  const handleBomSubmit = async (e) => {
    setShowPopup(false);
    const { resultProductList } = handleCanCreateStockOutOrder(
      selectedWo,
      bomIdList
    );

    for (let i = 0; i < resultProductList.length; i++) {
      const item = resultProductList[i];
      if (item?.part?.id) {
        await addStock(item?.part?._id, 'parts', item?.part?.name);
        handleChangeBatchQuantity(item?.units, item?.part?._id);
        await handleSubmitQuantity(e, bomItemQuantities);
      } else if (item?.products?.id) {
        await addStock(item?.product?._id, 'products', item?.product?.name);
        handleChangeBatchQuantity(item?.units, item?.products?._id);
        await handleSubmitQuantity(e, bomItemQuantities);
      }
    }
    setProducts();
  };

  useEffect(() => {
    if (formData?.items?.length > 0) {
      setFormData((prev) => {
        return {
          ...prev,
          items: prev.items.map((item) => {
            return {
              ...item,
              usage: 'CreatePo',
              usageId: selectedWo?._id,
            };
          }),
        };
      });
      setUsage('CreatePo');
    }
  }, [formData?.items?.length, selectedWo]); //eslint-disable-line

  return (
    <div className="flex flex-col gap-2">
      {quantityModal && (
        <Modal
          isMobile={isMobile}
          isTablet={isTablet}
          onCloseModal={() => {
            setQuantityModal(false);
            setIsProductMaster(false);
            setSelectedItem(null);
            setBatchSearchTerm('');
          }}
          pages={['Select Item', 'Add Quantity']}
          onNextClick={({ setStep }) => {
            if (!selectedItem?.value) {
              toast.error('Cannot stock in parent item');
              return;
            }
            setStep((prev) => prev + 1);
          }}
          onSubmit={handleSubmitQuantity}
        >
          {({ step }) => {
            return (
              <>
                {step === 0 ? (
                  <>
                    {isProductMaster ? (
                      <ProductMasterSelector
                        hideName
                        autoSelect
                        onSelect={async (val) => {
                          const res = await getProductByNameMaster({
                            data: { name: val },
                          }).unwrap();
                          if (!res) {
                            toast.error('Prodcut has not been stocked in yet');
                            setSelectedItem(null);
                            return;
                          }
                          if (!res?.stores?.includes(formData?.store)) {
                            toast.error(
                              'Selected prodcut does not exist in the selected store'
                            );
                            setSelectedItem(null);
                            return;
                          }
                          addStock(res?._id, `products`, res?.name);
                          toast.info('Item Selected');
                          setSelectedItem({
                            value: res?._id,
                            label: res?.name,
                          });
                        }}
                      />
                    ) : (
                      <VariantSelector
                        storeId={formData?.store}
                        checkQuantity
                        disableAddButton
                        onLastItemSelect={(e) => {
                          if (e) {
                            addStock(e?.value, `${e?.type}s`, e?.label);
                            toast.info('Item Selected');
                            setSelectedItem(e);
                          } else {
                            setSelectedItem(null);
                          }
                        }}
                      />
                    )}
                    <p className="mt-5">
                      Selected Item: {selectedItem?.label || '-'}
                    </p>
                  </>
                ) : addedItems?.every((item) => !item?.entries?.length) ? (
                  <div className="mt-5">
                    <Empty
                      image={Empty.PRESENTED_IMAGE_SIMPLE}
                      description={
                        <span className="text-gray-600">
                          No Quantity Available for the item{' '}
                          <b className="text-gray-800">
                            {addedItems?.[0]?.item?.name}
                          </b>{' '}
                          in the selected store
                        </span>
                      }
                    />
                  </div>
                ) : (
                  <div className="flex flex-col items-center">
                    <div className="w-full mb-4">
                      <Input
                        placeholder="Search by batch number..."
                        value={batchSearchTerm}
                        onChange={(e) => setBatchSearchTerm(e.target.value)}
                        allowClear
                      />
                    </div>
                    <Table className={'mt-5'}>
                      <Table.Head>
                        <Table.Row>
                          <Table.Th className={'text-center'}>
                            <input
                              type="checkbox"
                              checked={addedItems?.every((item) =>
                                item?.entries?.every((entry) =>
                                  selectedRows.includes(entry._id)
                                )
                              )}
                              onChange={(e) => {
                                if (e.target.checked) {
                                  // Add all row IDs to selectedRows
                                  const allRowIds = [];
                                  addedItems?.forEach((item) =>
                                    item?.entries?.forEach((entry) =>
                                      allRowIds.push(entry._id)
                                    )
                                  );
                                  setSelectedRows(allRowIds);
                                } else {
                                  // Deselect all rows
                                  setSelectedRows([]);
                                }
                              }}
                            />
                          </Table.Th>
                          <Table.Th className={'text-center'}>Name</Table.Th>
                          <Table.Th className={'text-center'}>FIFO</Table.Th>
                          <Table.Th className={'text-center'}>Type</Table.Th>
                          <Table.Th className={'text-center'}>
                            Batch No.
                          </Table.Th>
                          <Table.Th className={'text-center'}>Lot No.</Table.Th>
                          <Table.Th className={'text-center'}>
                            Quantity
                          </Table.Th>
                          <Table.Th className={'text-center'}>
                            StoreArea
                          </Table.Th>
                        </Table.Row>
                      </Table.Head>
                      <Table.Body>
                        {addedItems?.map((item) =>
                          item?.entries
                            ?.filter(
                              (entry) =>
                                !batchSearchTerm ||
                                (entry?.batchNo &&
                                  entry.batchNo
                                    .toLowerCase()
                                    .includes(batchSearchTerm.toLowerCase()))
                            )
                            ?.map((entry, i) => {
                              const itemName = item?.item?.name.includes('(')
                                ? item?.item?.name
                                : item?.item?.part
                                  ? getPartVariantName(item?.item)
                                  : item?.item?.product
                                    ? getProductVariantName(item?.item)
                                    : item?.item?.name;

                              return (
                                <Table.Row
                                  key={i}
                                  className="text-center border-b group relative"
                                >
                                  <Table.Td>
                                    <input
                                      type="checkbox"
                                      checked={selectedRows.includes(
                                        entry?._id
                                      )}
                                      onChange={(e) =>
                                        handleRowSelection(
                                          entry?._id,
                                          e.target.checked
                                        )
                                      }
                                    />
                                  </Table.Td>
                                  {i === 0 && (
                                    <Table.Td rowSpan={item?.entries?.length}>
                                      {itemName?.length <= 20 ? (
                                        <div>{itemName}</div>
                                      ) : (
                                        <Tooltip text={itemName}>
                                          <Table.Td>
                                            {itemName?.substring(0, 20) + '...'}
                                          </Table.Td>
                                        </Tooltip>
                                      )}
                                    </Table.Td>
                                  )}
                                  {item?.entries?.length === 0 ? (
                                    <Table.Td>Out of Stock</Table.Td>
                                  ) : (
                                    <>
                                      <Table.Td>
                                        <p>{i + 1}</p>
                                      </Table.Td>
                                      <Table.Td>
                                        {entry?.itemType === 'semiFinishedGoods'
                                          ? `Semi Finished Goods(${entry?.process?.name || ''})`
                                          : entry?.itemType === 'finishedGoods'
                                            ? 'Finished Goods'
                                            : entry?.itemType}
                                      </Table.Td>
                                      <Table.Td>
                                        <p>{entry?.batchNo || '-'}</p>
                                      </Table.Td>
                                      <Table.Td>
                                        <p>{entry?.lotNo || '-'}</p>
                                      </Table.Td>
                                      <Table.Td>
                                        <p>{entry?.remainingQuantity || '-'}</p>
                                      </Table.Td>
                                      <Table.Td>
                                        <p>{entry?.storeArea || '-'}</p>
                                      </Table.Td>
                                    </>
                                  )}
                                </Table.Row>
                              );
                            })
                        )}
                      </Table.Body>
                    </Table>
                    <div className="w-full flex justify-center mt-4 mb-4">
                      {addedItems?.map((item) => (
                        <div
                          className="flex flex-col items-start"
                          key={item?.item?._id}
                        >
                          {/* <p>{item?.item?.name}:</p> */}
                          <Input
                            type="number"
                            placeholder="Enter Quantity"
                            onChange={(e) =>
                              handleChangeBatchQuantity(
                                parseFloat(e.target.value),
                                item?.item?._id
                              )
                            }
                          />
                        </div>
                      ))}
                    </div>
                    {/* <Button type="submit">Send to Cart -{'>'}</Button> */}
                  </div>
                )}
              </>
            );
          }}
        </Modal>
      )}

      <div className="flex gap-x-4 flex-wrap items-center md:gap-y-0 gap-y-2">
        <div className="flex gap-x-2 items-center">
          <Button
            size="small"
            type="primary"
            onClick={() => {
              setIsProductMaster(false);
              setQuantityModal(true);
            }}
          >
            + Add Items
          </Button>

          <InfoTooltip position="right" id="stockNavSearch">
            Effortlessly search for items and compile a cart list. Item batches
            are conveniently listed based on the valuation method set for each
            part, streamlining your inventory management process.
          </InfoTooltip>
        </div>
        <div className="flex gap-x-2 items-center">
          <Button
            size="small"
            type="primary"
            onClick={() => {
              setIsProductMaster(true);
              setQuantityModal(true);
            }}
          >
            + Add FG with Master
          </Button>

          <InfoTooltip position="right" id="stockNavSearch">
            Effortlessly search for items and compile a cart list. Item batches
            are conveniently listed based on the valuation method set for each
            part, streamlining your inventory management process.
          </InfoTooltip>
        </div>
        {/* <Button
          className=" !min-w-[10rem] !w-[10px] md:!min-w-[8rem] md:!w-[10px] !text-[12px]"
          onClick={() => handleBomClick()}
        >
          Upcoming&nbsp;Orders
        </Button> */}
        <Button
          onClick={() => setShowManualEntry(true)}
          size="small"
          type="primary"
        >
          <EditOutlined />
          Manual&nbsp;Entry
        </Button>
      </div>

      {showPopup && (
        <Modal
          isMobile={isMobile}
          isTablet={isTablet}
          title={'Job Order BOM'}
          description={`${isMobile ? '' : 'Create cart using job order details'}`}
          svg={<Analytics className="h-8 w-8" />}
          onCloseModal={() => {
            setShowPopup(false);
            setShowProducts(false);
            setProducts();
          }}
          onSubmit={handleBomSubmit}
          // btnIsLoading={isLoadingCreatePo}
        >
          {() => {
            return (
              <>
                <div className="flex flex-col">
                  {!showProducts && (
                    <Table className={`mt-5`}>
                      <Table.Head>
                        <Table.Row>
                          <Table.Th className={'text-center'}>
                            Work Order ID
                          </Table.Th>
                          <Table.Th className={'text-center'}>
                            Work Order Name
                          </Table.Th>
                        </Table.Row>
                      </Table.Head>
                      <Table.Body>
                        {bomsData?.map((item, idx) => {
                          return (
                            <Table.Row
                              key={idx}
                              className={'hover:cursor-pointer hover:shadow-md'}
                              onClick={() => {
                                // handleBomListClick(item);
                                setProducts(item.bom);
                                setSelectedWo(item);
                                setShowProducts(true);
                                setFormData((prev) => {
                                  return {
                                    ...prev,
                                    comments: `Work Order ${item.name} was partially checked out. Remaining quantity for `,
                                  };
                                });
                              }}
                            >
                              <Table.Td className={'text-center'}>
                                {item?.id}
                              </Table.Td>
                              <Table.Td className={'text-center'}>
                                {item?.name}
                              </Table.Td>
                            </Table.Row>
                          );
                        })}
                      </Table.Body>
                    </Table>
                  )}
                  {showProducts && (
                    <>
                      <div
                        className="justify-start flex border rounded-lg px-10 w-fit py-2 cursor-pointer"
                        onClick={() => setShowProducts(false)}
                      >
                        Back
                      </div>
                      <Table>
                        <Table.Head>
                          <Table.Row>
                            <Table.Th className={'text-center'}>
                              Bom Name
                            </Table.Th>
                            <Table.Th className={'text-center'}>
                              Stock Required
                            </Table.Th>
                            <Table.Th>Action</Table.Th>
                          </Table.Row>
                        </Table.Head>
                        <Table.Body>
                          {products?.map((part) => (
                            <Table.Row key={part?._id}>
                              <Table.Td className={'text-center'}>
                                {part?.name}
                              </Table.Td>
                              <Table.Td className={'text-center'}>
                                {selectedWo.orderQuantity[part._id]}
                              </Table.Td>
                              <Table.Td>
                                <Input
                                  type="checkbox"
                                  checked={bomIdList.includes(part._id)}
                                  onChange={(e) => {
                                    const partId = part._id;
                                    if (e.target.checked) {
                                      setBomIdList((prevList) => [
                                        ...prevList,
                                        partId,
                                      ]);
                                    } else {
                                      setBomIdList((prevList) =>
                                        prevList.filter((id) => id !== partId)
                                      );
                                    }
                                  }}
                                  className={`w-5 h-5 mb-5`}
                                />
                              </Table.Td>
                            </Table.Row>
                          ))}
                        </Table.Body>
                      </Table>
                    </>
                  )}
                </div>
              </>
            );
          }}
        </Modal>
      )}
      {/* Table */}
    </div>
  );
};

export default StockNavigator;

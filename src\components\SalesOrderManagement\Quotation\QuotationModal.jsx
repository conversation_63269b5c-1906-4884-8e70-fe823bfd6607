import { ArrowLeftOutlined } from '@ant-design/icons';
import { Button } from 'antd';
import { useCallback, useContext, useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { toast } from 'react-toastify';
import {
  generatePrefixId,
  handleFormula,
  renderElemBasedOnFormatV2,
  renderFieldsBasedOnType,
} from '../../../helperFunction.js';
import useCompanyDetailsSelector from '../../../hooks/useCompanyDetailsSelector.js';
import useNaigationBlocker from '../../../hooks/useNaigationBlocker.js';
import { useGetAdditionalChargesQuery } from '../../../slices/additionalChargesApiSllice.js';
import { useLazyGetAllcustomerQuery } from '../../../slices/customerDataSlice.js';
import { useUpdateDefaultsMutation } from '../../../slices/defaultsApiSlice.js';
import { useLazyQueryHistoryQuery } from '../../../slices/editHistoryApiSlice.js';
import {
  useCreateOrderMutation,
  useLazyGetOrderDetailsByIDQuery,
  useUpdateOrderMutation,
} from '../../../slices/orderApiSlice.js';
import { useGetPrefixIdQuery } from '../../../slices/prefixIdApiSlice.js';
import { useGetLatestQuotationQuery } from '../../../slices/quotationApiSlice.js';
import { useUpdateSalesInquiryStatusMutation } from '../../../slices/salesInquiryDashboardApiSlice.js';
import { Store } from '../../../store/Store';
import { gstOptions } from '../../../utils/Constant.js';
import ProductFormatManager from '../../ProductFormats/ProductFormatManager.jsx';
import Charges from '../../salesOrder/charges.jsx';
import ProductDetails from './ProductDetails';
import QuoteDetails from './QuoteDetails';
import TermsAndConditions from './TermsAndConditions';

const defaultFormData = {
  quoteID: '',
  SalesInquiryLink: '',
  date: {
    creationDate: '',
    expiryDate: '',
  },
  businessDetails: {
    companyName: '',
    address: {},
    contact: '',
    gstNumber: '',
  },
  vendorDetails: {
    id: '',
    name: '',
    companyName: '',
    email: [],
    address: [],
    billingAddress: [],
    mobileNumber: [],
    gstNumber: '',
    paymentTerm: '',
  },
  productDetails: [],
  termsAndConditions: [],
  additionalComments: '',
  emailStatus: false,
  additionalFields: null,
  attachments: [],
  charges: {},
  isPdfTableVertical: false,
  chargesHideStatus: {
    totalCGST: false,
    totalSGST: false,
    totalIGST: false,
  },
  bankDetailsHideStatus: false,
};

const defaultItemDetails = {
  productName: '',
  uom: '',
  quantity: 0,
  rate: 0,
  amount: '',
  totalAmount: '',
  cgst: '',
  sgst: '',
  igst: '',
  discount: 0,
  HsnSacCode: '',
  attachments: [],
};

const QuotationModal = ({
  isMobile,
  isTablet,
  method,
  isEdit,
  data,
  setIsAdd,
  setIsQuotationEdit,
  quotation,
  isCopy,
  setIsCopy,
  isQuotationLoading,
  isAdd,
  isQuotationEdit,
  opportunityCustomer,
  leadId,
}) => {
  const [searchParams] = useSearchParams();
  const [createDepOrder] = useCreateOrderMutation();
  const [updateDefaults] = useUpdateDefaultsMutation();
  const navigate = useNavigate();
  // const [getAllOrders] = useGetAllOrdersMutation();
  const [selectedSalesInquiry, setSelectedSalesInquiry] = useState();
  const [formData, setFormData] = useState(
    isEdit || isCopy ? data : defaultFormData
  );
  // product format
  const [tableFormatProducts, setTableFormatProducts] = useState([]);
  const [tableFormatsCharges, setTableFormatsCharges] = useState({});
  const [columnVisibility, setColumnVisibility] = useState({});
  const [chargesVisibility, setChargesVisibility] = useState({});
  const [displayFormat, setDisplayFormat] = useState(null);

  const { data: allAdditionalCharges, isLoading: isAdditionalChargesLoading } =
    useGetAdditionalChargesQuery();
  const [columnInputs, setColumnInputs] = useState(['']);

  const [additionalCharges, setAdditionalCharges] = useState({});
  const [isSetAsDefault, setIsSetAsDefault] = useState(false);
  const [itemDetails, setItemDetails] = useState(defaultItemDetails);
  const [selectedProducts, setSelectedProducts] = useState([]);
  const [currentDate, setCurrentDate] = useState();
  const [selectedTermAndCondition, setSelectedTermAndCondition] = useState([]);
  // const [defaultTermsAndConditions, setDefaultTermsAndConditions] =
  //   useState(false);
  const [templateDropDownModal, setTemplateDropDownModal] = useState(false);
  const [newOptionStatus, setNewOptionStatus] = useState(false);
  const [dropdownIdx, setDropdownIdx] = useState(null);
  const [defaultAdditionalComments, setDefaultAdditionalComments] =
    useState(false);
  const [SubmitTriggered, setSubmitTriggered] = useState(false);
  const [additionalFields, setAdditionalFields] = useState(
    isEdit ? data?.additionalFields : null
  );
  const {
    defaults: { defaultParam },
    state,
    dispatch,
  } = useContext(Store);
  const [showIgst, setShowIgst] = useState(false);
  const pageSlug = '/salesordermanagement/quotation';
  const [hidePoTable, setHidePoTable] = useState(() => {
    const savedValue = localStorage.getItem('hideProductDetailsObject');
    const parsedValue = savedValue ? JSON.parse(savedValue) : {};
    return parsedValue[pageSlug] || false;
  });

  const createdBy = state?.user?.name;
  const [getOrderDetailsByID] = useLazyGetOrderDetailsByIDQuery();
  const [getAllCustomers] = useLazyGetAllcustomerQuery();
  const [updateDepOrder] = useUpdateOrderMutation();
  const [getHistory, { data: historyData }] = useLazyQueryHistoryQuery();
  // Quotation Version
  const [quotationVersionID, setQuotationVersionID] = useState('');
  const [quotationVersionFormatComponent, setQuotationVersionFormatComponent] =
    useState({});
  const [quotationVersionFormat, setQuotationVersionFormat] = useState({});
  const [quotationVersionFormatArray, setQuotationVersionFormatArray] =
    useState([]);
  const [versionIndex, setVersionIndex] = useState(-1);
  const [latestVersionFormat, setLatestVersionFormat] = useState({});
  const [additionalFieldsHideStatus, setAdditionalFieldsHideStatus] = useState(
    {}
  );
  const { data: prefixIds } = useGetPrefixIdQuery();
  const { data: latestQuote } = useGetLatestQuotationQuery();

  // Product Templates
  const [allProductTemplates, setAllProductTemplates] = useState(
    isEdit ? data?.allProductTemplates : []
  );

  const { allowNavigation } = useNaigationBlocker();

  useEffect(() => {
    if (data?._id && isEdit) {
      getHistory({
        id: data?._id,
        populate: JSON.stringify([]),
      });
    }
  }, [data?._id, getHistory, isEdit]);
  useEffect(() => {
    if (!prefixIds) return;
    if (!historyData?.versionData || !isEdit) {
      // If there's no history data or we're not editing, reset to default
      setLatestVersionFormat({ prefixIds });
      setQuotationVersionFormatArray(prefixIds?.['quotationVersion'] || {});
      return;
    }

    // Create a map for unique version indices and their corresponding formats
    const uniqueVersionMap = new Map();

    // Loop through the versionData array in reverse to prioritize the latest versions
    for (let i = historyData.versionData.length - 1; i >= 0; i--) {
      const { versionIndex, latestVersionFormat } = historyData.versionData[i];
      if (versionIndex != null && !uniqueVersionMap.has(versionIndex)) {
        uniqueVersionMap.set(versionIndex, latestVersionFormat);
      }
    }

    // Create a new updated default parameter object
    const updatedDefaults = {
      prefixIds: {
        ...prefixIds,
        quotationVersion: prefixIds?.['quotationVersion']?.map((item, index) =>
          uniqueVersionMap.has(index) ? uniqueVersionMap.get(index) : item
        ),
      },
    };

    // Update state with the new defaults and quotation version formats
    setLatestVersionFormat(updatedDefaults);
    setQuotationVersionFormatArray(
      updatedDefaults?.prefixIds?.['quotationVersion']
    );
  }, [prefixIds, isEdit, historyData]);

  useEffect(() => {
    renderElemBasedOnFormatV2({
      idType: 'quotationVersion',
      format: quotationVersionFormat,
      setFormat: setQuotationVersionFormat,
      componentSetter: setQuotationVersionFormatComponent,
      defaults: latestVersionFormat,
      setDefault: setLatestVersionFormat,
      idIndex: versionIndex,
    });
    setQuotationVersionID(generatePrefixId(quotationVersionFormat));
  }, [quotationVersionFormat]); //eslint-disable-line

  const getIdOptions = useCallback(() => {
    if (
      !quotationVersionFormatArray ||
      Object.keys(quotationVersionFormatArray).length === 0
    )
      return;

    const formatArray = [];
    Object.keys(quotationVersionFormatArray).forEach((index) => {
      const idFormat = quotationVersionFormatArray[index];
      let transformedIdFormat = '';

      const idFormatKeys = Object.keys(idFormat);
      idFormatKeys.forEach((fieldType) => {
        const type = fieldType.substring(0, fieldType.indexOf('_'));
        if (type === 'String' || type === 'Increment') {
          transformedIdFormat += idFormat[fieldType];
        } else {
          transformedIdFormat += type;
        }
      });

      formatArray.push({
        label: transformedIdFormat,
        value: idFormat,
      });
    });

    return formatArray;
  }, [quotationVersionFormatArray]);

  useEffect(() => {
    let optionsValue = getIdOptions();
    if (optionsValue && optionsValue?.length > 0) {
      setQuotationVersionFormat(optionsValue?.[0]?.value);
      let index = -1;
      for (let i in quotationVersionFormatArray) {
        if (
          JSON.stringify(quotationVersionFormatArray[i]) ===
          JSON.stringify(optionsValue?.[0]?.value)
        ) {
          index = parseInt(i);
          break;
        }
      }
      setVersionIndex(index);
    }
  }, [getIdOptions, quotationVersionFormatArray]);

  useEffect(() => {
    const fetchOrderDetails = async (orderId) => {
      const orderDetails = await getOrderDetailsByID(orderId);
      const salesInquiry = orderDetails?.data?.steps?.find(
        (i) => i.refKey === 'SalesInquiryDashboard'
      );
      if (!salesInquiry) return;
      const customerDetails = await getAllCustomers();
      const customer = customerDetails?.data?.customers?.find(
        (customer) => customer?._id === salesInquiry?.data?.CustomerData?._id
      );
      const prods = [];
      const manualEntries = [];

      const products = salesInquiry?.data?.products || [];
      for (let i = 0; i < products.length; i++) {
        const pro = products[i];
        if (pro?.manualEntry) {
          manualEntries.push({
            productName: pro?.productName || '',
            uom: pro.uom || '',
            quantity: pro.quantity || 0,
          });
        } else {
          prods.push({
            ...pro?.productId,
            productName: pro?.productName,
            quantity: pro?.quantity,
          });
        }
      }

      setSelectedSalesInquiry(salesInquiry.data);
      setSelectedProducts(prods);
      setFormData((prev) => {
        return {
          ...prev,
          SalesInquiryLink: salesInquiry?.data?.id,
          SalesInquiryRef: salesInquiry?.data?._id,
          productDetails: manualEntries,
          vendorDetails: {
            id: customer?._id || '',
            name: customer?.name || '',
            companyName: customer?.company_name || '',
            address: customer?.address || '',
            gstNumber: customer?.gstNumber || '',
            email: customer?.unique_id || '',
            mobileNumber: customer?.phone_no || '',
            paymentTerm: customer?.paymentTerm || '',
          },
        };
      });
    };

    if (searchParams.get('kanban') === 'true') {
      const orderId = searchParams.get('orderId');
      if (orderId) {
        fetchOrderDetails(orderId);
      }
    }
  }, [getAllCustomers, getOrderDetailsByID, searchParams]);

  useEffect(() => {
    if (isEdit) {
      const productTemplates = data?.allProductTemplates || [];
      if (productTemplates && productTemplates?.length > 0) {
        let pDetails = [];
        data?.productDetails?.forEach((pro) => {
          const findProductWithTemp = productTemplates?.find(
            (temp) => pro?._id === temp?.productId
          );
          pDetails.push({
            ...(findProductWithTemp || []),
            ...pro,
          });
        });
        if (pDetails.length > 0) {
          setFormData((prev) => ({
            ...prev,
            productDetails: pDetails,
          }));
        }
      }
    }
  }, [data?.productDetails, data?.allProductTemplates, isEdit]);

  useEffect(() => {
    if (opportunityCustomer) {
      setFormData((prev) => ({
        ...prev,
        vendorDetails: {
          ...prev.vendorDetails,
          id: opportunityCustomer?._id,
          address: opportunityCustomer?.address,
          companyName: opportunityCustomer?.company_name,
          email: opportunityCustomer?.unique_id,
          gstNumber: opportunityCustomer?.gstNumber,
          mobileNumber: opportunityCustomer?.phone_no,
          name: opportunityCustomer?.name,
          paymentTerm: opportunityCustomer?.paymentTerm,
        },
      }));
    }
  }, [opportunityCustomer]);

  const [onEditSalesInquiry] = useUpdateSalesInquiryStatusMutation();
  const [totalPrice, setTotalPrice] = useState(0);
  const [subtotalFormulaPrice, setSubtotalFormulaPrice] = useState(0);

  const {
    companyDetails: companyData = {},
    addressSelector,
    contactSelector,
    emailSelector,
    bankDetailsSelector,
    profileSelector,
  } = useCompanyDetailsSelector(formData, setFormData);
  const [gstoptions, setGSToptions] = useState(() => {
    return localStorage.getItem('CustomGSTOptions')
      ? JSON.parse(localStorage.getItem('CustomGSTOptions'))
      : gstOptions;
  });

  /* The `useEffect` hook is used to perform side effects in a React component. In this case, the effect
is triggered only once when the component is mounted (empty dependency array `[]`), and it sets the
current date. */
  useEffect(() => {
    setCurrentDate(() => {
      const currentDate = new Date();
      return currentDate;
    });
  }, []);

  const setInitialInfo = useCallback(() => {
    if (!companyData?.name) return;
    const business = companyData;

    const address = business?.address?.find(
      (i) => i._id === formData?.selectedDetails?.address
    );
    const contact = business?.contactNumber?.find(
      (i) => i._id === formData?.selectedDetails?.contact
    );
    const email = business?.emailAddress?.find(
      (i) => i._id === formData?.selectedDetails?.email
    );

    const bankDetails = companyData?.bankDetails?.find(
      (i) => i._id === formData?.selectedDetails?.bank
    );

    setFormData((prev) => ({
      ...prev,
      quoteID: isEdit ? formData?.quoteID : `QT-${defaultParam?.quotationId}`,
      date: {
        ...prev?.date,
        creationDate: !isEdit ? currentDate : formData?.date?.creationDate,
      },
      bankDetails: {
        accountHolder: bankDetails?.accountHolder || '',
        accountNumber: bankDetails?.accountNumber,
        bankName: bankDetails?.bankName,
        ifsc: bankDetails?.ifsc,
        bankAddress: bankDetails?.bankAddress,
        upiId: bankDetails?.upiId || '',
      },
      businessDetails: {
        ...prev?.businessDetails,
        companyName: business?.name,
        address: address,
        contact: contact?.number,
        gstNumber: business?.gstNumber,
        email: email?.mail,
      },
    }));
  }, [
    defaultParam,
    currentDate,
    isEdit,
    formData?.quoteID,
    formData?.date?.creationDate,
    formData?.selectedDetails?.address,
    formData?.selectedDetails?.contact,
    formData?.selectedDetails?.email,
    formData?.selectedDetails?.bank,
    companyData,
  ]);

  useEffect(() => {
    if (allAdditionalCharges) {
      const charges = allAdditionalCharges.filter(
        (charge) => charge.pageSlug === '/salesordermanagement/quotation'
      );
      if (charges.length > 0) {
        setAdditionalCharges(charges[0]);
        const newCharges = charges[0]?.additionalCharges?.reduce(
          (acc, charge) => {
            acc[charge.name] = 0;
            return acc;
          },
          {}
        );

        // Update state
        if (newCharges) {
          setFormData((prev) => ({
            ...prev,
            charges: {
              ...prev.charges,
              ...newCharges,
            },
          }));
        }
      }
    }
  }, [allAdditionalCharges]);

  useEffect(() => {
    setInitialInfo();
  }, [setInitialInfo]);

  useEffect(() => {
    if ((isEdit || isCopy) && data) {
      setShowIgst(data?.showIgst || false);
      setFormData((prev) => ({
        ...prev,
        charges: data?.charges,
      }));
      setAdditionalFieldsHideStatus(data?.additionalFieldsHideStatus || {});
    } else if (latestQuote) {
      setAdditionalFieldsHideStatus(
        latestQuote?.additionalFieldsHideStatus || {}
      );
    }
  }, [isEdit, isCopy, data, latestQuote]);

  // useEffect(() => {
  //   const business = companyData;

  //   const address = business?.address?.find(
  //     (i) => i._id === formData?.selectedDetails?.address
  //   );
  //   const contact = business?.contactNumber?.find(
  //     (i) => i._id === formData?.selectedDetails?.contact
  //   );
  //   const email = business?.emailAddress?.find(
  //     (i) => i._id === formData?.selectedDetails?.email
  //   );

  //   setFormData((prev) => ({
  //     ...prev,
  //     quoteID: isEdit ? formData?.quoteID : `QT-${defaultParam?.quotationId}`,
  //     date: {
  //       ...prev?.date,
  //       creationDate: !isEdit ? currentDate : formData?.date?.creationDate,
  //     },
  //     businessDetails: {
  //       ...prev?.businessDetails,
  //       companyName: business?.name,
  //       address: address,
  //       contact: contact?.number,
  //       gstNumber: business?.gstNumber,
  //       email: email?.mail,
  //     },
  //   }));
  // }, [
  //   defaultParam,
  //   currentDate,
  //   isEdit,
  //   formData?.quoteID,
  //   formData?.date?.creationDate,
  //   formData?.selectedDetails?.address,
  //   formData?.selectedDetails?.contact,
  //   formData?.selectedDetails?.email,
  //   companyData,
  // ]);

  useEffect(() => {
    if (SubmitTriggered) {
      handleSubmit();
      setSubmitTriggered(false);
    }
  }, [SubmitTriggered]); // eslint-disable-line
  // commented because this cause the id exit issue, when we add customer at quotation creation time, after adding in newDefault the id is set what the current quote id, but is should be the incremneted id
  // useEffect(() => {
  //   if (defaultParam) setNewDefaults(defaultParam);
  // }, [defaultParam]);

  useEffect(() => {
    if (isCopy) {
      setFormData((prev) => ({
        ...prev,
        termsAndConditions: data?.termsAndConditions,
      }));
      setSelectedTermAndCondition(
        data?.termsAndConditions?.map((tc) => ({
          label: tc?.terms,
          value: { description: tc?.description, terms: tc?.terms, page: [] },
        })) || []
      );
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isCopy]);
  useEffect(() => {
    if (!isEdit) {
      setFormData((prev) => ({
        ...prev,
        termsAndConditions: selectedTermAndCondition,
      }));
    }
  }, [isEdit, selectedTermAndCondition]);
  useEffect(() => {
    if (!isEdit) {
      setFormData((prev) => ({
        ...prev,
        // termsAndConditions: defaultParam?.quotation?.termsAndConditions
        //   ? defaultParam?.quotation?.termsAndConditions
        //   : '',
        additionalComments: defaultParam?.quotation?.additionalComments
          ? defaultParam?.quotation?.additionalComments
          : '',
      }));
    }
    if (isCopy) {
      setFormData((prev) => ({
        ...prev,
        additionalComments: data?.additionalComments,
      }));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [defaultParam, isEdit, isCopy]);

  /**
   * The function `renderForm` takes in a `step` and `setFormStep` as parameters and returns a different
   * component based on the value of `step`.
   * @returns The `renderForm` function returns different components based on the value of the `step`
   * parameter. The components being returned are:
   */
  // const renderForm = (step, setFormStep) => {
  //   switch (step) {
  //     case 1:
  //       return (
  //         <>

  //         </>
  //       );

  //     case 2:
  //       return (

  //       );
  //     case 3:
  //       return (

  //       );

  //     default:
  //       return null;
  //   }
  // };
  const itemsFormulaHandler = () => {
    let transformedItems = [];

    // Combine the formData productDetails and selectedProducts
    const allProducts = [
      ...(formData?.productDetails || []),
      ...(selectedProducts || []),
    ];

    for (let item of allProducts) {
      let temp = { ...item };
      // delete temp.template;
      if (item?.customColumns) {
        for (let columnInput of columnInputs) {
          if (columnInput?.formula) {
            let val = {};
            let formula = columnInput?.formula?.substring(
              columnInput?.formula?.indexOf('{') + 1,
              columnInput?.formula?.indexOf('}')
            );
            formula = formula.trim();
            formula = formula + ' ';

            while (formula?.indexOf('$') !== -1) {
              let key = formula?.substring(
                formula?.indexOf('$') + 1,
                formula?.indexOf(' ')
              );
              let stringRegex = /^[A-Za-z]+$/;
              if (stringRegex.test(key)) {
                val = {
                  ...val,
                  [key]:
                    item?.[key] || parseFloat(item?.customColumns?.[key]) || 0,
                };
              }
              formula = formula?.substring(formula?.indexOf(' ') + 1);
            }
            const allFields = [
              {
                name: 'cgst',
                percentOf: 'amount',
              },
              {
                name: 'sgst',
                percentOf: 'amount',
              },
              {
                name: 'igst',
                percentOf: 'amount',
              },
              {
                name: 'discount',
                percentOf: 'amount',
              },
            ];
            let res = handleFormula(columnInput, val, allFields);

            // Add or update the customColumns field with the calculated value
            temp.customColumns = {
              ...temp.customColumns,
              [columnInput?.columnName]: res,
            };
          }
        }
      }

      transformedItems.push(temp); // Push the transformed product details into the array
    }
    return transformedItems;
  };

  /**
   * The `handleSubmit` function is used to handle form submissions, including sending an email and
   * updating data. If the number of steps is 5, then it means the user is also sending mail
   * and if there is a value in response, it means the email is sent and then formData will get updated
   * with emailStatus = true. And if isEdit is true then that means the method is set to update and
   * we need to pass the body otherwise the method should be add.
   *
   */

  const handleSubmit = async () => {
    setIsSetAsDefault(true);
    let res;
    try {
      let transformedItems = itemsFormulaHandler();
      if (!isEdit) {
        // if (!formData?.date?.expiryDate) {
        //   return toast.error('Expiry Date Is Required');
        // }
        for (let i = 0; i < formData?.productDetails; i++) {
          if (formData?.productDetails?.[i]?.quantity < 0) {
            toast.error('Quantity can not be negative');
            return;
          }
          if (formData?.productDetails?.[i]?.rate < 0) {
            toast.error('Rate can not be negative');
            return;
          }
          if (formData?.productDetails?.[i]?.discount < 0) {
            toast.error('Discount can not be negative');
            return;
          }
        }

        for (let i = 0; i < selectedProducts?.length; i++) {
          if (selectedProducts?.[i]?.quantity < 0) {
            toast.error('Quantity can not be negative');
            return;
          }
          if (selectedProducts?.[i]?.rate < 0) {
            toast.error('Rate can not be negative');
            return;
          }
          if (selectedProducts?.[i]?.discount < 0) {
            toast.error('Discount can not be negative');
            return;
          }
        }
        if (!formData?.date?.expiryDate) {
          return toast.error('Expiry Date Is Required');
        }
        if (isCopy) {
          const { _id, termsAndConditions, ...dataWithoutId } = formData;
          const updatedTerms = termsAndConditions?.map((terms) => terms?.value);
          res = await method({
            ...dataWithoutId,
            termsAndConditions: updatedTerms,
            emailStatus: false,
            productDetails: defaultParam?.projectDefaults
              ?.showProductFormatTable
              ? tableFormatProducts
              : transformedItems,
            createdBy,
            additionalFields,
            showIgst,
            subTotal: subtotalFormulaPrice,
            quoteVersion: quotationVersionID,
            latestVersionFormat:
              latestVersionFormat?.prefixIds?.['quotationVersion']?.[
                versionIndex
              ],
            versionIndex,
            allProductTemplates,
            additionalFieldsHideStatus,
            productTableFormat: displayFormat?._id,
            productDetailsFromFormat: tableFormatProducts,
            productChargesFromFormat: tableFormatsCharges,
            productTableChargesHideStatus: chargesVisibility,
            productTableColumnHideStatus: columnVisibility,
            productTableFormatHideStatus:
              defaultParam?.projectDefaults?.showProductFormatTable,
            quoteStatus: 'pending',
            createdAt: new Date(),
          }).unwrap();
        } else {
          const { termsAndConditions, ...data } = formData;
          const updatedTerms = termsAndConditions?.map((terms) => terms?.value);
          res = await method({
            ...data,
            termsAndConditions: updatedTerms,
            emailStatus: false,
            productDetails: defaultParam?.projectDefaults
              ?.showProductFormatTable
              ? tableFormatProducts
              : transformedItems,
            productDetailsFromFormat: tableFormatProducts,
            productTableFormat: displayFormat?._id,
            productTableChargesHideStatus: chargesVisibility,
            productTableColumnHideStatus: columnVisibility,
            productTableFormatHideStatus:
              defaultParam?.projectDefaults?.showProductFormatTable,
            productChargesFromFormat: tableFormatsCharges,
            createdBy,
            additionalFields,
            showIgst,
            subTotal: subtotalFormulaPrice,
            leadId,
            quoteVersion: quotationVersionID,
            latestVersionFormat:
              latestVersionFormat?.prefixIds?.['quotationVersion']?.[
                versionIndex
              ],
            versionIndex,
            allProductTemplates,
            additionalFieldsHideStatus,
          }).unwrap();
        }

        await updateDefaults({
          quotation: {
            ...defaultParam?.quotation,
            additionalComments: defaultAdditionalComments
              ? formData?.additionalComments
              : defaultParam?.quotation?.additionalComments,
          },
        });
        if (selectedSalesInquiry) {
          await onEditSalesInquiry({
            id: selectedSalesInquiry?._id,
            body: {
              status: true,
              ongoingStatus: 'Quote Created',
            },
          }).unwrap();
        }
        toast.success('New Quotation Created', {
          theme: 'colored',
          position: 'top-right',
          toastId: 'Quotation Created',
        });
      } else {
        const { _termsAndConditions, ...dataWithoutTerms } = formData;
        const updatedTerms = selectedTermAndCondition?.map(
          (terms) => terms?.value
        );
        res = await method({
          data: {
            ...dataWithoutTerms,
            emailStatus: false,
            termsAndConditions: updatedTerms,
            productDetails: defaultParam?.projectDefaults
              ?.showProductFormatTable
              ? tableFormatProducts
              : transformedItems,
            createdBy,
            subTotal: subtotalFormulaPrice,
            quoteVersion: quotationVersionID,
            latestVersionFormat:
              latestVersionFormat?.prefixIds?.['quotationVersion']?.[
                versionIndex
              ],
            versionIndex,
            allProductTemplates,
            showIgst,
            additionalFieldsHideStatus,
            productDetailsFromFormat: tableFormatProducts,
            productTableFormat: displayFormat?._id,
            productTableChargesHideStatus: chargesVisibility,
            productTableColumnHideStatus: columnVisibility,
            productTableFormatHideStatus:
              defaultParam?.projectDefaults?.showProductFormatTable,
            productChargesFromFormat: tableFormatsCharges,
            quoteStatus: 'pending',
          },
          id: formData._id,
        }).unwrap();
        if (res) {
          await updateDefaults({
            quotation: {
              ...defaultParam?.quotation,
              additionalComments: defaultAdditionalComments
                ? formData?.additionalComments
                : defaultParam?.quotation?.additionalComments,
            },
          });
          toast.success('Quotation updated', {
            theme: 'colored',
            position: 'top-right',
            toastId: 'Quotation updated',
          });
        } else {
          toast.error('Quote updation unsuccessful', {
            theme: 'colored',
            position: 'top-right',
            toastId: 'Quotation Created',
          });
        }
      }
      setFormData(defaultFormData);
      setItemDetails(defaultItemDetails);

      const kanban = searchParams.get('kanban') === 'true';
      const orderId = searchParams.get('orderId');
      const allProducts = transformedItems?.map(
        (product) => product?.productName
      );
      const kanbanFilterObj = {
        customer_name: formData?.vendorDetails?.name,
        company_name: formData?.vendorDetails?.companyName,
        product_details: allProducts,
      };
      const navigateParams = {
        department: searchParams.get('department'),
        id: res?._id,
        refType: searchParams.get('refType'),
        page: searchParams.get('page'),
        taskId: searchParams.get('taskId'),
        orderId,
        index: searchParams.get('index'),
        idIndex: additionalFields?.idIndex,
        filterDetails: JSON.stringify(kanbanFilterObj),
      };
      if (!kanban) {
        let obj = {
          objRef: res?._id,
          currentDepartment: 'sales',
          refKey: 'Quotation',
          currentPage: 'Quotation',
          userId: state?.user?._id,
          filterDetails: JSON.stringify(kanbanFilterObj),
        };
        if (isEdit) {
          await updateDepOrder({
            data: {
              ...obj,
              id: res?.taskId,
            },
          });
        } else {
          await createDepOrder({
            data: obj,
          });
        }
      }
      if (kanban && !orderId) {
        let time = new Date();
        dispatch({
          type: 'ADD_CARD',
          payload: {
            data: {
              taskId: searchParams.get('taskId'),
              stepPage: 'Quotation',
              updatedAt: time?.toDateString(),
              filterDetails: kanbanFilterObj,
            },
            currentColumn: 'Quotation',
          },
        });
      }

      const filteredParams = Object.fromEntries(
        Object.entries(navigateParams).filter(([_, value]) => value !== null)
      );

      const navigateStr = `/primary/kanban?${new URLSearchParams(filteredParams).toString()}`;
      allowNavigation();
      if (kanban) {
        navigate(navigateStr);
      } else if (searchParams.get('navigateTo')) {
        navigate(searchParams.get('navigateTo'));
      } else if (leadId) {
        navigate(`/crm/pipelines`);
      } else {
        setIsAdd(false);
        setIsQuotationEdit(false);
        setIsCopy(false);
      }
    } catch (error) {
      console.error(error); //eslint-disable-line
      // toast.error(
      //   error?.data?.message || 'An error occurred while saving data.'
      // );
    }
  };

  const handleInputChange = (
    fieldValue,
    fieldName,
    idx,
    colIndex,
    tableRowIndex
  ) => {
    if (tableRowIndex !== undefined && tableRowIndex !== null) {
      setAdditionalFields((prev) => {
        const updatedTemplateData = [...prev.templateData];
        const fieldWithTableIndex = idx;
        if (fieldWithTableIndex === -1) return prev;

        const updatedTableOptions = {
          ...updatedTemplateData[fieldWithTableIndex]?.tableOptions,
        };

        if (!updatedTableOptions.column) {
          updatedTableOptions.column = [];
        } else {
          updatedTableOptions.column = [...updatedTableOptions.column];
        }

        if (!updatedTableOptions.column[colIndex].selectedOptions) {
          updatedTableOptions.column[colIndex] = {
            columnName: updatedTableOptions.column[colIndex].columnName,
            columnType: updatedTableOptions.column[colIndex].columnType,
            dropdownOptions:
              updatedTableOptions.column[colIndex].dropdownOptions,
            selectedOptions: [],
          };
        }
        const updatedSelectedOptions = [
          ...updatedTableOptions.column[colIndex].selectedOptions,
        ];
        updatedSelectedOptions[tableRowIndex] = fieldValue;

        updatedTableOptions.column[colIndex] = {
          ...updatedTableOptions.column[colIndex],
          selectedOptions: updatedSelectedOptions,
        };

        updatedTemplateData[fieldWithTableIndex] = {
          ...updatedTemplateData[fieldWithTableIndex],
          tableOptions: updatedTableOptions,
        };

        return {
          ...prev,
          templateData: updatedTemplateData,
        };
      });
      return;
    }
    if (fieldValue === '+') {
      setDropdownIdx(idx);
      setTemplateDropDownModal(true);
    } else {
      const updatedAdditionalFields = additionalFields?.templateData?.map(
        (field) => {
          if (field?.fieldName === fieldName) {
            return {
              ...field,
              fieldValue,
            };
          } else {
            return field;
          }
        }
      );
      setAdditionalFields((prev) => {
        return {
          ...prev,
          templateData: updatedAdditionalFields,
        };
      });
      setFormData((prev) => {
        return {
          ...prev,
          additionalFields: {
            ...prev.additionalFields,
            templateData: updatedAdditionalFields,
            name: additionalFields.name,
          },
        };
      });
    }
  };

  return (
    <div className="bg-white border border-gray-100 shadow-sm rounded-lg overflow-hidden mx-auto max-w-7xl">
      {(isAdd || isCopy || isQuotationEdit || isEdit) && (
        <div className="bg-gray-50 px-4 py-3 border-b border-gray-100">
          <div className="flex items-center gap-3">
            <Button
              icon={<ArrowLeftOutlined />}
              onClick={() => {
                setIsAdd(false);
                setIsQuotationEdit(false);
                setIsCopy(false);
              }}
              type="text"
              size="small"
              className="hover:bg-gray-200"
            />
            <div>
              <h2 className="text-lg font-semibold text-gray-900 mb-0">
                {isEdit ? 'Edit' : 'Create'} Quotation
              </h2>
              <p className="text-sm text-gray-600 mb-0">
                {isEdit
                  ? 'Update Quotation information'
                  : 'Create a new Quotation'}
              </p>
            </div>
          </div>
        </div>
      )}
      {/* IF ID Already Exist This Modal will appear */}
      <div className="w-full space-y-4 p-6">
        <QuoteDetails
          isCopy={isCopy}
          formData={formData}
          setFormData={setFormData}
          isEdit={isEdit}
          selectedSalesInquiry={selectedSalesInquiry}
          setSelectedSalesInquiry={setSelectedSalesInquiry}
          quotation={quotation}
          additionalFields={additionalFields}
          setAdditionalFields={setAdditionalFields}
          addressSelector={addressSelector}
          contactSelector={contactSelector}
          emailSelector={emailSelector}
          profileSelector={profileSelector}
          isMobile={isMobile}
          isTablet={isTablet}
          getIdOptions={getIdOptions}
          setQuotationVersionFormatComponent={
            setQuotationVersionFormatComponent
          }
          setQuotationVersionFormat={setQuotationVersionFormat}
          quotationVersionFormatArray={quotationVersionFormatArray}
          versionIndex={versionIndex}
          setVersionIndex={setVersionIndex}
          quotationVersionFormat={quotationVersionFormat}
          quotationVersionFormatComponent={quotationVersionFormatComponent}
          setAdditionalFieldsHideStatus={setAdditionalFieldsHideStatus}
        />
        {defaultParam?.projectDefaults?.showProductFormatTable ? (
          <div className="space-y-4 px-6">
            <ProductFormatManager
              input={tableFormatProducts}
              setInput={setTableFormatProducts}
              charges={tableFormatsCharges}
              setCharges={setTableFormatsCharges}
              columnVisibility={columnVisibility}
              setColumnVisibility={setColumnVisibility}
              chargesVisibility={chargesVisibility}
              setChargesVisibility={setChargesVisibility}
              displayFormat={displayFormat}
              setDisplayFormat={setDisplayFormat}
              isEdit={isEdit}
              isCopy={isCopy}
              data={data}
              latestQuote={latestQuote}
            />
          </div>
        ) : (
          <div className="space-y-4 px-6">
            <ProductDetails
              isMobile={isMobile}
              isTablet={isTablet}
              isEdit={isEdit}
              isCopy={isCopy}
              formData={formData}
              setFormData={setFormData}
              gstoptions={gstoptions}
              setGSToptions={setGSToptions}
              itemDetails={itemDetails}
              setItemDetails={setItemDetails}
              defaultItemDetails={defaultItemDetails}
              selectedProducts={selectedProducts}
              setSelectedProducts={setSelectedProducts}
              totalPrice={totalPrice}
              setTotalPrice={setTotalPrice}
              showIgst={showIgst}
              setShowIgst={setShowIgst}
              columnInputs={columnInputs}
              setColumnInputs={setColumnInputs}
              hidePoTable={hidePoTable}
              setHidePoTable={setHidePoTable}
              pageSlug={pageSlug}
              allProductTemplates={allProductTemplates}
              setAllProductTemplates={setAllProductTemplates}
            />
            <Charges
              isMobile={isMobile}
              isTablet={isTablet}
              formData={formData}
              setFormData={setFormData}
              totalPrice={totalPrice}
              setTotalPrice={setTotalPrice}
              subtotalFormulaPrice={subtotalFormulaPrice}
              setSubtotalFormulaPrice={setSubtotalFormulaPrice}
              additionalCharges={additionalCharges}
              selectedProducts={selectedProducts}
              pageSlug={`/salesordermanagement/quotation`}
              hidePoTable={hidePoTable}
              showIgst={showIgst}
              isAdditionalChargesLoading={isAdditionalChargesLoading}
            />
          </div>
        )}
        <TermsAndConditions
          isMobile={isMobile}
          isTablet={isTablet}
          formData={formData}
          setFormData={setFormData}
          isEdit={isEdit}
          setDefaultAdditionalComments={setDefaultAdditionalComments}
          defaultAdditionalComments={defaultAdditionalComments}
          selectedTermAndCondition={selectedTermAndCondition}
          setSelectedTermAndCondition={setSelectedTermAndCondition}
          totalPrice={totalPrice}
          setTotalPrice={setTotalPrice}
          additionalFields={additionalFields}
          setAdditionalFields={setAdditionalFields}
          isSetAsDefault={isSetAsDefault}
          bankDetailsSelector={bankDetailsSelector}
          page={['SO']}
        />
        {additionalFields?.templateData?.length > 0 && (
          <div className="bg-white p-4 rounded-lg border border-gray-200">
            <h3 className="text-sm font-semibold text-gray-700">
              Template Details
            </h3>
            <div className="mb-1 w-full gap-3">
              {renderFieldsBasedOnType(
                additionalFields,
                handleInputChange,
                templateDropDownModal,
                setTemplateDropDownModal,
                setAdditionalFields,
                newOptionStatus,
                setNewOptionStatus,
                dropdownIdx,
                setDropdownIdx,
                searchParams,
                additionalFieldsHideStatus,
                setAdditionalFieldsHideStatus
              )}
            </div>
          </div>
        )}
      </div>
      <div className="bg-gray-50 px-4 py-3 border-t border-gray-100 mt-4">
        <div className="flex items-center justify-end">
          <Button
            isLoading={isQuotationLoading}
            type="primary"
            onClick={() => {
              setFormData((prev) => {
                return {
                  ...prev,
                  vendorDetails: {
                    ...prev?.vendorDetails,
                    email: [prev?.vendorDetails?.email?.[0]],
                    address: [prev?.vendorDetails?.address?.[0]],
                    mobileNumber: [prev?.vendorDetails?.mobileNumber?.[0]],
                  },
                };
              }),
                setSubmitTriggered(true);
            }}
          >
            {isEdit ? 'Update' : 'Save'} Quotation
          </Button>
        </div>
      </div>
    </div>
  );
};

export default QuotationModal;

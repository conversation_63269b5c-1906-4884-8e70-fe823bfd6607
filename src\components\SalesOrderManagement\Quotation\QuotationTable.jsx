import { LoadingOutlined } from '@ant-design/icons';
import { <PERSON><PERSON>, Spin } from 'antd';
import DOMPurify from 'dompurify';
import he from 'he';
import { htmlToText } from 'html-to-text';
import { Archive, ArrowDown, ArrowUp, Trash } from 'lucide-react';
import { useContext, useEffect, useState } from 'react';
import { FaLock } from 'react-icons/fa';
import { FiInfo } from 'react-icons/fi';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import { generateDateString, handlePdf } from '../../../helperFunction.js';
import { useGetFollowUpQuery } from '../../../slices/followUpApiSlice.js';
import { useGetHeaderByPageQuery } from '../../../slices/headerReorderApiSlice';
import { useLazyGetPdfQuery } from '../../../slices/pdfApiSlice.js';
import {
  useArchiveQuotationMutation,
  useDeleteManyQuotationsMutation,
  useGetQuotationFilterOptionsQuery,
  useSendQuotationMutation,
} from '../../../slices/quotationApiSlice.js';
import { useLazyGetSalesOrderOptionsQuery } from '../../../slices/salesOrderSlices.js';
import { Store } from '../../../store/Store.js';
import {
  DEFAULT_QUOTATION_HEADER,
  QUOTATION_FIELDS,
} from '../../../utils/Constant.js';
import WithSelectAll from '../../../utils/HOC/WithSelectAll.js';
import { customConfirm } from '../../../utils/customConfirm.js';
import { toCapitalize } from '../../../utils/toCapitalize.js';
import AddButton from '../../AddButton.jsx';
import HistorySidebar from '../../Kanban/HistorySidebar.jsx';
import HeaderReorder from '../../Pipeline/HeaderReorder.jsx';
import CustomToolTip from '../../global/CustomToolTip.jsx';
import Button from '../../global/components/Button.jsx';
import { FilterIcon, FilterV2 } from '../../global/components/FilterV2.jsx';
import { InfoTooltip } from '../../global/components/InfoTooltip.jsx';
import RightSidebar from '../../global/components/RightSidebar.jsx';
import { TabButton, TabContainer } from '../../global/components/TabContainer';
import Table from '../../global/components/Table';
import TablePopup from '../../global/components/TablePopup.jsx';
import Tooltip from '../../global/components/ToolTip.jsx';
import WhatsappShareModal from '../../global/components/WhatsappShareModal.jsx';
import MediaModal from '../../v3/global/components/MediaModal.jsx';
import SendMail from '../../v3/global/components/SendMail';
import FollowUP from './FollowUP.jsx';
import QuotationRightSidebar from './QuotationRightSidebar';
import StatusModal from './StatusModal';

const colors = {
  Pending: 'bg-yellow-200 text-yellow-600',
  Rejected: 'bg-red-200 text-red-600',
  Approved: 'bg-[#DCF0DD] text-[#0F6A2E]',
};
const getStatusColor = (status) => {
  if (colors[status]) return colors[status];
  return 'bg-fuchsia-200 text-fuchsia-700';
};

// const DESKTOP_VIEW_HEADERS = [
//   '',
//   'TASK ID',
//   'DATE',
//   'QUOTE NO.',
//   'COMPANY NAME',
//   'QUOTE STATUS',
// ];
const MOBILE_VIEW_HEADERS = ['DATE', 'QUOTE NO.', 'QUOTE STATUS'];

const QuotationTable = ({
  data,
  setExpired,
  expired,
  setField,
  type,
  setType,
  isQuotationEdit,
  setIsQuotationEdit,
  editData,
  setEditData,
  updateQuotations,
  handleCheckBoxChange,
  handleSelectAll,
  selectAll,
  checkedRows,
  setCheckedRows,
  copyData,
  setCopyData,
  setIsCopy,
  rows,
  setRows,
  field,
  setSearchTerm,
  isAdd,
  isCopy,
  setIsAdd,
  isMobile,
  setFilters,
  showFilters,
  setShowFilters,
  isQuotationLoading,
}) => {
  let realTimeUser = JSON.parse(localStorage.getItem('user'))?.user;
  let columnKeys = QUOTATION_FIELDS?.map((elem) => elem?.value);
  const { state, defaults, isTrialUser } = useContext(Store);
  const user = state?.user;

  const { data: headerInfo } = useGetHeaderByPageQuery({
    headerFor: 'activeQuotatinHeader',
  });

  const [allHeader, setAllHeader] = useState([]);
  const [activeHeader, setActiveHeader] = useState(
    headerInfo?.headers || DEFAULT_QUOTATION_HEADER
  );
  const navigate = useNavigate();
  const [historySidebar, setHistorySidebar] = useState({
    open: false,
    steps: [],
  });
  const [SendingMail, setSendingMail] = useState(false);
  const [istrialUserLimitExceed, setIstrialUserLimitExceed] = useState(false);
  const [getPdf, { isFetching: isFetchingPdf }] = useLazyGetPdfQuery();

  const [getSo, { data: salesOrders }] = useLazyGetSalesOrderOptionsQuery();
  const [mailData, setMailData] = useState({
    receiver: '',
    body: '',
    subject: '',
    input: {},
    attachments: [],
  });
  const activeHeaderFromLocal = 'activeQuotatinHeader';
  const [sendMail] = useSendQuotationMutation();
  const [ShowEmailModal, setShowEmailModal] = useState(false);
  const [deleteManyQuotations] = useDeleteManyQuotationsMutation();
  const [archiveQuotations] = useArchiveQuotationMutation();
  const [selectedquotation, setSelectedQuotation] = useState([]);
  const [openSideBar, setOpenSideBar] = useState(false);
  const [isOpenHeaders, setIsOpenHeaders] = useState(false);
  const [visibleConvertButton, setVisibleConvertButton] =
    useState(false);
  const [clickedRow, setClickedRow] = useState('');
  const [mobileClickedRow, setMobileClickedRow] = useState(null);
  const [Tabs, setTabs] = useState({
    ongoing: {
      tab: 'Ongoing',
      active: true,
    },
    expired: {
      tab: 'Expired',
      active: false,
    },
    FollowUp: {
      tab: 'Follow Up',
      active: false,
    },
  });
  // const [followUpLength, setFollowUpLength] = useState(0);
  const [ReadMore, setReadMore] = useState(false);
  const [isDeleteVisible, setIsDeleteVisible] = useState(false);
  const [isArchiveVisible, setIsArchiveVisible] = useState(false);
  const [whatsappShare, setWhatsappShare] = useState(false);
  const [selctedId, setSelectedId] = useState('');
  const [additionalOptions, setAdditionOptions] = useState([]);
  const [openStatusModal, setOpenStatusModal] = useState(false);
  const { data: filterOptions } = useGetQuotationFilterOptionsQuery();

  const { data: followUps, isLoading } = useGetFollowUpQuery({
    followFor: 'Quotation',
  });

  const filterConfig = [
    {
      key: 'createdAt',
      path: 'createdAt',
      label: 'Date',
      type: 'date',
    },
    {
      key: 'quoteID',
      path: 'quoteID',
      label: 'Quotation No.',
      type: 'multiSelect',
      options: filterOptions?.quoteID || [],
    },
    {
      key: 'quoteStatus',
      path: 'quoteStatus',
      label: 'Status',
      type: 'multiSelect',
      options: filterOptions?.quoteStatus || [],
    },
    {
      key: 'vendorDetails.companyName',
      path: 'vendorDetails.companyName',
      label: 'Company',
      type: 'multiSelect',
      options: filterOptions?.companyName || [],
    },
  ];

  const handleSendmail = async () => {
    setSendingMail(true);
    const selectedquotation = data?.find((item) => item?._id === clickedRow);
    const fd = new FormData();
    fd.append('id', selectedquotation?._id);
    fd.append('receiver', mailData?.receiver);
    fd.append('subject', mailData?.subject);
    fd.append('body', mailData?.body);
    if (mailData?.attachments?.length !== 0) {
      mailData.attachments.forEach((file, index) => {
        // Convert the file object to a JSON string
        fd.append(`attachments[${index}][data]`, file.data);
        fd.append(`attachments[${index}][name]`, file.name);
        fd.append(`attachments[${index}][type]`, file.type);
      });
    }
    await sendMail(fd)
      .unwrap()
      .finally(() => {
        setSendingMail(false);
      });
    await updateQuotations({
      data: {
        ...data,
        emailStatus: true,
      },
      id: data[0]._id,
    }).unwrap();
    toast.success('Mail Sent Successfully');
    setShowEmailModal(false);
    setMailData({
      receiver: '',
      body: '',
      subject: '',
      input: {},
      attachments: [],
    });
  };

  const SetActiveTab = (tabname) => {
    let NewActiveTabs = {};
    for (let keys in Tabs) {
      if (Tabs[keys].tab === tabname) {
        NewActiveTabs[keys] = {
          tab: Tabs[keys].tab,
          active: true,
        };
      } else {
        NewActiveTabs[keys] = {
          tab: Tabs[keys].tab,
          active: false,
        };
      }
    }
    setTabs(NewActiveTabs);
  };

  useEffect(() => {
    if (isMobile) {
      setActiveHeader(
        MOBILE_VIEW_HEADERS.map((item) => {
          return {
            headerName: item,
            key: item.toLocaleLowerCase(),
            isAdditional: false,
          };
        })
      );
    } else {
      setActiveHeader(headerInfo?.headers || DEFAULT_QUOTATION_HEADER);
    }
  }, [isMobile, headerInfo]);

  useEffect(() => {
    if (rows) {
      let allHead = [];
      rows.forEach((row) => {
        if (row?.additionalFields?.templateData?.length > 0) {
          let temp = row?.additionalFields?.templateData;
          temp.forEach((item) => {
            if (item?.fieldType === 'Table') return;

            const key = item?.fieldName;
            if (!allHead.some((item) => item.key === key)) {
              allHead.push({
                headerName: item?.fieldName?.toUpperCase(),
                key: item?.fieldName,
                isAdditional: true,
                type: item?.fieldType,
              });
            }
          });
        }
      });
      setAllHeader([...DEFAULT_QUOTATION_HEADER, ...allHead]);
    }
  }, [rows, activeHeader]);

  useEffect(() => {
    getSo();
  }, [getSo]);

  useEffect(() => {
    if (data) {
      setRows(data);
    } else {
      setRows([]);
    }
  }, [data, setRows, type, field]);

  useEffect(() => {
    if (Tabs?.expired?.active) {
      setExpired(true);
    } else {
      setExpired(false);
    }
  }, [Tabs, setExpired]);

  useEffect(() => {
    if (checkedRows?.length > 0) {
      setIsDeleteVisible(true);
      setIsArchiveVisible(true);
    } else {
      setIsDeleteVisible(false);
      setIsArchiveVisible(false);
    }
  }, [checkedRows]);

  const handleDeleteAll = async () => {
    const confirm = await customConfirm(
      'Are you sure you want to delete these Quotations?',
      'delete'
    );
    if (!confirm) return;
    const idsToDelete = checkedRows.map((item) => item._id);
    const res = await deleteManyQuotations({ ids: idsToDelete });
    if (res?.data?.message) {
      const { msg1, msg2 } = res.data.message;

      if (msg1 && msg2) {
        toast.error(msg1);
        toast.success(msg2);
      } else if (msg1) {
        toast.error(msg1);
      } else {
        toast.success(msg2);
      }
    }
    setCheckedRows([]);
  };
  const handleArchiveAll = async () => {
    const confirm = await customConfirm(
      'Are you sure you want to archive these Quotations?',
      'success'
    );
    if (!confirm) return;
    try {
      const idsToArchive = checkedRows.map((item) => item._id);
      const res = await archiveQuotations({ ids: idsToArchive });
      if (res) {
        toast.success('Quotations archived successfully');
        setCheckedRows([]);
      }
    } catch (err) {
      toast.error(err?.response?.data?.message || err.message, {
        theme: 'colored',
        position: 'top-right',
      });
    }
  };

  const handleSelectConvertToSO = (e, el) => {
    if (e && e.target && e.target.checked) {
      setSelectedQuotation((prev) => {
        return [...prev, el];
      });

      if (el?.quoteStatus === 'Approved') {
        setVisibleConvertButton(true);
      }
    } else {
      const filtereduncheckeddata = selectedquotation?.filter(
        (item) => item.quoteID !== el.quoteID
      );

      setSelectedQuotation(filtereduncheckeddata);
    }
  };

  useEffect(() => {
    const approvedQuotation = selectedquotation.filter(
      (item) => item.quoteStatus.toLowerCase() === 'approved'
    );
    if (approvedQuotation.length === 0 || approvedQuotation.length > 1) {
      setVisibleConvertButton(false);
    } else {
      setVisibleConvertButton(true);
    }
  }, [selectedquotation]);

  const handleConversionToSO = () => {
    const convertedQuotations = selectedquotation?.filter((quote) =>
      salesOrders?.some((so) => so.quotationID === quote.quoteID)
    );
    if (convertedQuotations?.length > 0) {
      const convertedIDs = convertedQuotations.map((quote) => quote.quoteID);
      const message = `Quotations with ID ${convertedIDs.join(', ')} have already been converted to Sales Orders.`;
      toast.error(message);
      return;
    }
    if (selectedquotation.length > 0) {
      if (
        selectedquotation.filter((item) => item?.quoteStatus === 'Approved')
          .length > 1
      ) {
        toast.error('Select Only one approved quotation at a time');
        return;
      }
      const isApprovedQuotation = selectedquotation.filter(
        (item) =>
          item?.quoteStatus === 'pending' || item?.quoteStatus === 'Rejected'
      );
      if (isApprovedQuotation.length > 0) {
        toast.error('Can not proceed with pending or rejected Quotation');
      } else {
        localStorage.setItem(
          'quotaionCovertedData',
          JSON.stringify(selectedquotation)
        );
        navigate('/salesordermanagement/orders?createModal=true');
      }
    }
  };
  const handleConversionToSI = () => {
    if (selectedquotation.length > 0) {
      if (
        selectedquotation.filter((item) => item?.quoteStatus === 'Approved')
          .length > 1
      ) {
        toast.error('Select Only one approved quotation at a time');
        return;
      }
      const isApprovedQuotation = selectedquotation.filter(
        (item) =>
          item?.quoteStatus === 'pending' || item?.quoteStatus === 'Rejected'
      );
      if (isApprovedQuotation.length > 0) {
        toast.error('Can not proceed with pending or rejected Quotation');
      } else {
        navigate(
          `/accountmanagement/invoices/create/sales?returnTab=sales&quoteId=${selectedquotation[0]?._id}`
        );
      }
    }
  };
  const handleCheckTrialUserLimit = () => {
    if (isTrialUser) {
      if (rows.length >= 5) {
        setIstrialUserLimitExceed(true);
        setIsAdd(false);
      }
    }
  };

  const setSteps = (taskId) => {
    let tasks = state?.allTiles;
    let chosenTask = tasks?.find((elem) => elem?.taskId === taskId);
    if (chosenTask) {
      setHistorySidebar({
        open: true,
        steps: chosenTask?.steps,
        orderId: chosenTask?._id,
      });
    }
  };

  return (
    <>
      {isOpenHeaders && (
        <HeaderReorder
          activeHeaderFromLocal={activeHeaderFromLocal}
          setIsOpenHeaders={setIsOpenHeaders}
          activeHeader={activeHeader}
          setActiveHeader={setActiveHeader}
          allHeader={allHeader}
        />
      )}
      {isMobile && mobileClickedRow && (
        <TablePopup
          isEdit={false}
          onDownload={() =>
            handlePdf(getPdf, mobileClickedRow?._id, 'quotation')
          }
          downloading={isFetchingPdf}
          onBack={() => setMobileClickedRow(null)}
        >
          <div className="space-y-4 !text-[12px]">
            <div className="w-full flex items-center justify-between gap-4">
              <label className="font-semibold">TASK ID</label>
              <p>
                {mobileClickedRow?.taskId?.customTaskId
                  ? `${mobileClickedRow?.taskId?.customTaskId}(${mobileClickedRow?.taskId?.taskId})`
                  : mobileClickedRow?.taskId?.taskId
                    ? mobileClickedRow?.taskId?.taskId
                    : '-'}
              </p>
            </div>
            <div className="w-full flex items-center justify-between gap-4">
              <label className="font-semibold">CREATION DATE</label>
              <p>
                {generateDateString(
                  new Date(mobileClickedRow?.date?.creationDate)
                ) || '-'}
              </p>
            </div>
            <div className="w-full flex items-center justify-between gap-4">
              <label className="font-semibold">QUOTE NO.</label>
              <p>{mobileClickedRow?.quoteID || '-'}</p>
            </div>
            <div className="w-full flex items-center justify-between gap-4">
              <label className="font-semibold">SALES INQUIRY LINK</label>
              <p>
                {mobileClickedRow?.SalesInquiryLink
                  ? `SI - ${mobileClickedRow?.SalesInquiryLink}` || '-'
                  : '-'}
              </p>
            </div>
            <div className="w-full flex items-center justify-between gap-4">
              <label className="font-semibold">COMPANY NAME</label>
              <p>{mobileClickedRow?.vendorDetails?.companyName}</p>
            </div>
            <div className="w-full flex items-center justify-between gap-4">
              <label className="font-semibold">CUSTOMER NAME</label>
              <p>{mobileClickedRow?.vendorDetails?.name}</p>
            </div>
            <div className="w-full flex items-center justify-between gap-4">
              <label className="font-semibold">CUSTOMER NAME</label>
              <p>{mobileClickedRow?.vendorDetails?.email[0]}</p>
            </div>
            <div className="w-full flex items-center justify-between gap-4">
              <label className="font-semibold">CONTACT NUMBER</label>
              <p>{mobileClickedRow?.vendorDetails?.mobileNumber[0]}</p>
            </div>
            <div className="w-full flex items-center justify-between gap-4">
              <label className="font-semibold">QUOTE STATUS</label>
              <p>
                {!defaults?.defaultParam?.projectDefaults?.disableApprovals ? (
                  <span
                    className={`${
                      colors[
                        mobileClickedRow?.quoteStatus.charAt(0).toUpperCase() +
                          mobileClickedRow?.quoteStatus.slice(1)
                      ]
                    } px-3 py-1 rounded-md font-medium whitespace-nowrap`}
                  >
                    {mobileClickedRow?.quoteStatus.charAt(0).toUpperCase() +
                      mobileClickedRow?.quoteStatus.slice(1)}
                  </span>
                ) : (
                  <span>-</span>
                )}
              </p>
            </div>
            <div className="w-full flex items-center justify-between gap-4">
              <label className="font-semibold">EXPIRY DATE</label>
              <p>
                {generateDateString(
                  new Date(mobileClickedRow?.date?.expiryDate)
                ) || '-'}
              </p>
            </div>
            <div className="w-full flex items-center justify-between gap-4">
              <label className="font-semibold">EMAIL STATUS</label>
              <p>{mobileClickedRow?.emailStatus ? 'Sent' : 'Not Sent'}</p>
            </div>
          </div>
        </TablePopup>
      )}
      <WhatsappShareModal
        whatsappShare={whatsappShare}
        setWhatsappShare={setWhatsappShare}
        type={'quotation'}
        id={selctedId}
        additionalOptions={additionalOptions}
      />
      <HistorySidebar sidebar={historySidebar} setSidebar={setHistorySidebar} />
      {ShowEmailModal && (
        <SendMail
          title={'Send QUOTATION Mail'}
          setShowEmailModal={setShowEmailModal}
          SendingMail={SendingMail}
          handleSendmail={handleSendmail}
          mailData={mailData}
          setMailData={setMailData}
        />
      )}

      <div className="relative inline-block mt-3">
        <TabContainer className="!mb-2">
          {Object.keys(Tabs).map((tabdata) => {
            return (
              <TabButton
                key={Tabs[tabdata]['tab']}
                onClick={() => {
                  SetActiveTab(Tabs[tabdata].tab);
                }}
                isactive={Tabs[tabdata]['active']}
              >
                {toCapitalize(Tabs[tabdata].tab)}
              </TabButton>
            );
          })}
        </TabContainer>
        {followUps?.length > 0 && (
          <span className="absolute top-[8px] right-1 transform translate-x-1/2 -translate-y-1/2 bg-red-400  text-white rounded-full w-4 h-4 flex items-center justify-center text-xs">
            {followUps?.length}
          </span>
        )}
      </div>

      <div className="w-full mt-1">
        <RightSidebar
          openSideBar={openSideBar}
          setOpenSideBar={setOpenSideBar}
          scale={736}
        >
          <QuotationRightSidebar
            openSideBar={openSideBar}
            setOpenSideBar={setOpenSideBar}
            setShowEmailModal={setShowEmailModal}
            ShowEmailModal={ShowEmailModal}
            allData={data}
            data={data?.find((item) => item?._id === clickedRow)}
            clickedRow={clickedRow}
            setClickedRow={setClickedRow}
            expired={expired}
            generateDateString={generateDateString}
            isQuotationEdit={isQuotationEdit}
            setIsQuotationEdit={setIsQuotationEdit}
            editData={editData}
            copyData={copyData}
            setCopyData={setCopyData}
            setIsCopy={setIsCopy}
            setEditData={setEditData}
            updateQuotations={updateQuotations}
            setReadMore={setReadMore}
            setWhatsappShare={setWhatsappShare}
            setSelectedId={setSelectedId}
            setAdditionOptions={setAdditionOptions}
          />
        </RightSidebar>
        {ReadMore && (
          <MediaModal
            FormData={
              data?.find((item) => item?._id === clickedRow)?.attachments || []
            }
            isView={true}
            setShowModal={setReadMore}
            ShowModal={ReadMore}
          />
        )}

        <div className="flex flex-col justify-center  w-full bg-white rounded-tl-xl rounded-tr-xl mt-2">
          {!Tabs?.FollowUp?.active ? (
            <>
              <div className="flex justify-between items-center !p-3">
                <div className="mt-[3px] relative ">
                  <div className="absolute top-4 left-1">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                      strokeWidth={1.5}
                      stroke="currentColor"
                      className="absolute top-1/2 left-3 transform -translate-y-1/2 text-gray-400 cursor-pointer"
                      style={{
                        width: '1.2rem',
                        height: '1.2rem',
                        marginLeft: '3px',
                      }}
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        d="m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z"
                      />
                    </svg>
                  </div>
                  <input
                    className={`${isMobile ? 'w-[180px] ml-3 !pl-7' : '!min-w-[100px] !pl-10 ml-3 w-[400px]'} !rounded-3xl !px-5 !py-1.5 outline-none  text-[12px] md:text-sm  bg-[#F2F1FB]`}
                    placeholder="Search Items from list..."
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
                <div className="flex items-start gap-3 justify-end mb-1 w-full mr-2">
                  {!isMobile && (
                    <>
                      {user?.archiveSalesOrderManagement && (
                        <CustomToolTip
                          tooltipId="archive-tooltip"
                          content="Archive"
                          place="top"
                          effect="solid"
                          className={`bg-black text-white p-1 rounded ${isArchiveVisible ? '' : 'hidden'}`}
                        >
                          <Archive
                            className={`h-5 w-5 ${isArchiveVisible ? 'text-blue-500  cursor-pointer ' : 'text-gray-400 cursor-not-allowed'}`}
                            onClick={handleArchiveAll}
                          />
                        </CustomToolTip>
                      )}
                      <CustomToolTip
                        tooltipId="delete-tooltip"
                        content="Delete"
                        place="top"
                        effect="solid"
                        className={`bg-black text-white p-1 rounded ${isDeleteVisible ? '' : 'hidden'}`}
                      >
                        <Trash
                          className={`h-5 w-5 ${!isDeleteVisible ? 'text-gray-400 cursor-not-allowed' : 'text-red-500 cursor-pointer'}`}
                          onClick={handleDeleteAll}
                        />
                      </CustomToolTip>

                      <div className="flex p-1 cursor-pointer">
                        {!isAdd && !isQuotationEdit && !isCopy && (
                          <>
                            <FilterIcon
                              showFilters={showFilters}
                              setShowFilters={setShowFilters}
                            />
                          </>
                        )}
                      </div>

                      <div className="flex gap-3">
                        <Button
                          className="!px-2 !p-1 text-[12px] font-medium"
                          onClick={handleConversionToSO}
                          disabled={!visibleConvertButton}
                        >
                          Convert to SO
                        </Button>
                        <Button
                          className="!px-2 !p-1 text-[12px] font-medium"
                          onClick={handleConversionToSI}
                          disabled={!visibleConvertButton}
                        >
                          Convert to SI
                        </Button>
                      </div>
                    </>
                  )}

                  {!isAdd && !isQuotationEdit && !isCopy && (
                    <span onClick={handleCheckTrialUserLimit}>
                      <AddButton
                        className="!px-2 !py-0.5 !text-[13px]"
                        modalOpen={isAdd}
                        setModalOpen={setIsAdd}
                      />
                    </span>
                  )}
                  <Modal
                    open={istrialUserLimitExceed}
                    footer={null}
                    centered
                    onCancel={() => setIstrialUserLimitExceed(false)}
                  >
                    <div className="my-10 flex w-full items-center justify-center flex-col">
                      <FiInfo className="text-5xl text-blue-primary" />
                      <h3 className="text-lg leading-5  font-bold mt-[2px] text-black font-inter text-center">
                        Limit exceed
                      </h3>
                      <p className="text-xs text-center font-medium text-black font-inter leading-2 mt-2 px-2">
                        You have reached the limit for creating quotations.
                        Please upgrade your plan to create more quotations.
                      </p>
                    </div>
                  </Modal>
                </div>
              </div>
              <FilterV2
                config={filterConfig}
                showFilters={showFilters}
                setFilters={setFilters}
              />
              <div className="w-full overflow-x-scroll">
                <Table>
                  <Table.Head>
                    <Table.Row>
                      {!isMobile && (
                        <Table.Th>
                          {checkedRows.length > 0 ? (
                            <div>
                              <input
                                type="checkbox"
                                className="mr-2"
                                checked={selectAll}
                                onChange={(e) => handleSelectAll(e)}
                              />
                              Select All
                            </div>
                          ) : (
                            ''
                          )}
                        </Table.Th>
                      )}
                      {activeHeader?.map((header, idx) => {
                        const isHide =
                          isMobile &&
                          !MOBILE_VIEW_HEADERS?.includes(header?.headerName);

                        if (header?.headerName === 'DATE' && !isMobile) {
                          return (
                            <Table.Th key={idx}>
                              <div className="flex">
                                <div>Date </div>
                                {type === 'aesc' && field === 'createdAt' ? (
                                  <ArrowUp
                                    cursor={'pointer'}
                                    size={15}
                                    onClick={() => {
                                      setField('createdAt');
                                      setType('desc');
                                    }}
                                  />
                                ) : (
                                  <ArrowDown
                                    cursor={'pointer'}
                                    size={15}
                                    onClick={() => {
                                      setField('createdAt');
                                      setType('aesc');
                                    }}
                                  />
                                )}
                              </div>
                            </Table.Th>
                          );
                        }
                        if (
                          header?.headerName === 'COMPANY NAME' &&
                          !isMobile
                        ) {
                          return (
                            <Table.Th key={idx}>
                              <div className="flex">
                                <div>Company Name </div>
                                {type === 'aesc' &&
                                field === 'vendorDetails.companyName' ? (
                                  <ArrowUp
                                    cursor={'pointer'}
                                    size={15}
                                    onClick={() => {
                                      setField('vendorDetails.companyName');
                                      setType('desc');
                                    }}
                                  />
                                ) : (
                                  <ArrowDown
                                    cursor={'pointer'}
                                    size={15}
                                    onClick={() => {
                                      setField('vendorDetails.companyName');
                                      setType('aesc');
                                    }}
                                  />
                                )}
                              </div>
                            </Table.Th>
                          );
                        }
                        return (
                          !isHide && (
                            <Table.Th key={idx}>{header?.headerName}</Table.Th>
                          )
                        );
                      })}
                      {!isMobile && (
                        <Table.Options
                          className={'!p-2 border-b-2'}
                          onRearrange={() => {
                            setIsOpenHeaders(true);
                          }}
                        />
                      )}
                    </Table.Row>
                  </Table.Head>
                  <Table.Body>
                    {isQuotationLoading ? (
                      <Table.Row>
                        <Table.Td colSpan={activeHeader?.length + 2}>
                          <div className="flex justify-center items-center">
                            <Spin
                              size="large"
                              indicator={<LoadingOutlined spin />}
                            />
                          </div>
                        </Table.Td>
                      </Table.Row>
                    ) : (
                      rows?.map((item, index) => {
                        return (
                          <Table.Row
                            key={index}
                            isclickable={isMobile}
                            onClick={() => setMobileClickedRow(item)}
                            className={'hover:cursor-pointer hover:bg-gray-100'}
                          >
                            {!isMobile && (
                              <Table.Td>
                                <input
                                  type="checkbox"
                                  onChange={(event) => {
                                    handleSelectConvertToSO(event, item);
                                    handleCheckBoxChange(event, item);
                                  }}
                                  checked={checkedRows.includes(item)}
                                />
                              </Table.Td>
                            )}

                            {activeHeader?.map((el, index) => {
                              if (el?.isAdditional) {
                                if (el?.type === 'MultiSelect') {
                                  let val =
                                    item?.additionalFields?.templateData
                                      ?.find((e) => e.fieldName === el?.key)
                                      ?.fieldValue?.map((e) => e?.value)
                                      ?.join(', ') || '-';
                                  return (
                                    <Table.Td
                                      key={index}
                                      onClick={(e) => {
                                        setOpenSideBar(true);
                                        e.stopPropagation();
                                      }}
                                    >
                                      {val}
                                    </Table.Td>
                                  );
                                } else if (el?.type === 'Description') {
                                  let val =
                                    item?.additionalFields?.templateData?.find(
                                      (e) => e.fieldName === el?.key
                                    )?.fieldValue || '-';
                                  const decodedHTML = he?.decode(val || '');
                                  const sanitizedHTML =
                                    DOMPurify.sanitize(decodedHTML);
                                  const completeText = htmlToText(
                                    sanitizedHTML,
                                    {
                                      wordwrap: false,
                                    }
                                  );

                                  const oneLineText = completeText
                                    .replace(/(\r\n|\n|\r)/gm, '')
                                    .replace(/\s+/g, '');

                                  return (
                                    <Table.Td
                                      key={index}
                                      onClick={(e) => {
                                        setOpenSideBar(true);
                                        e.stopPropagation();
                                      }}
                                    >
                                      <Tooltip
                                        text={sanitizedHTML}
                                        isText={false}
                                      >
                                        {oneLineText.length > 10
                                          ? oneLineText.slice(0, 10) + '...'
                                          : oneLineText}
                                      </Tooltip>
                                    </Table.Td>
                                  );
                                } else {
                                  return (
                                    <Table.Td
                                      key={index}
                                      className={'!text-nowrap'}
                                      onClick={(e) => {
                                        setOpenSideBar(true);
                                        e.stopPropagation();
                                      }}
                                    >
                                      {item?.additionalFields?.templateData?.find(
                                        (e) => e.fieldName === el?.key
                                      )?.fieldValue || '-'}
                                    </Table.Td>
                                  );
                                }
                              } else {
                                switch (el?.key) {
                                  case 'date':
                                    return (
                                      <>
                                        {realTimeUser?.columnAccess?.includes(
                                          columnKeys?.[2]
                                        ) ? (
                                          <Table.Td>
                                            {generateDateString(
                                              new Date(item?.createdAt)
                                            ) || '-'}
                                          </Table.Td>
                                        ) : (
                                          <Table.Td>
                                            <div className="h-[3rem] w-full flex items-center">
                                              <FaLock className="text-2xl text-slate-400" />
                                            </div>
                                          </Table.Td>
                                        )}
                                      </>
                                    );
                                  case 'task id':
                                    return (
                                      !isMobile && (
                                        <Table.Td
                                          className="hover:cursor-pointer hover:underline min-w-[50px] font-medium !text-blue-400"
                                          onClick={() => {
                                            setSteps(item?.taskId?.taskId);
                                          }}
                                        >
                                          {item?.taskId?.customTaskId
                                            ? `${item?.taskId?.customTaskId} (${item?.taskId?.taskId})`
                                            : item?.taskId?.taskId
                                              ? item?.taskId?.taskId
                                              : '-'}
                                        </Table.Td>
                                      )
                                    );

                                  case 'quote no.':
                                    return (
                                      <>
                                        <Table.Td
                                          className="hover:cursor-pointer hover:underline min-w-[50px] font-medium !text-blue-400"
                                          onClick={(e) => {
                                            if (
                                              e.target.tagName.toLowerCase() !==
                                                'input' &&
                                              !isMobile
                                            )
                                              setOpenSideBar(true);
                                            setClickedRow(item?._id);
                                          }}
                                        >
                                          {item?.quoteID || '-'}
                                        </Table.Td>
                                      </>
                                    );
                                  case 'company name':
                                    return (
                                      <>
                                        {!isMobile && (
                                          <>
                                            {realTimeUser?.columnAccess?.includes(
                                              columnKeys?.[5]
                                            ) ? (
                                              <Table.Td>
                                                <div className="flex items-center">
                                                  {/* small indicator for createdFrom */}
                                                  {item?.createdFrom && (
                                                    <Tooltip
                                                      text={`Created By: ${item?.createdFrom}`}
                                                    >
                                                      <div className="mr-2 h-3 w-3 bg-yellow-400 rounded-full" />
                                                    </Tooltip>
                                                  )}
                                                  {item?.vendorDetails
                                                    ?.companyName?.length <= 55
                                                    ? item?.vendorDetails
                                                        ?.companyName
                                                    : (
                                                        <Tooltip
                                                          text={
                                                            item?.vendorDetails
                                                              ?.companyName
                                                          }
                                                        >
                                                          {item?.vendorDetails?.companyName?.substring(
                                                            0,
                                                            55
                                                          ) + '...'}
                                                        </Tooltip>
                                                      ) || '-'}
                                                </div>
                                              </Table.Td>
                                            ) : (
                                              <Table.Td>
                                                <div className="h-[3rem] w-full flex items-center ml-[2.5rem]">
                                                  <FaLock className="text-2xl text-slate-400" />
                                                </div>
                                              </Table.Td>
                                            )}
                                          </>
                                        )}
                                      </>
                                    );
                                  case 'quote status':
                                    return (
                                      <>
                                        {realTimeUser?.columnAccess?.includes(
                                          columnKeys?.[1]
                                        ) ? (
                                          <Table.Td>
                                            {!defaults?.defaultParam?.projectDefaults?.disabledApprovalFor?.includes(
                                              'quotation'
                                            ) ? (
                                              <div className="flex items-center">
                                                <span
                                                  className={`${getStatusColor(
                                                    item?.quoteStatus
                                                      .charAt(0)
                                                      .toUpperCase() +
                                                      item?.quoteStatus.slice(1)
                                                  )} px-3 py-1 rounded-full font-medium whitespace-nowrap  hover:shadow-md cursor-pointer`}
                                                  onClick={() => {
                                                    setClickedRow(item?._id);
                                                    setOpenStatusModal(true);
                                                  }}
                                                >
                                                  {item?.quoteStatus
                                                    .charAt(0)
                                                    .toUpperCase() +
                                                    item?.quoteStatus.slice(1)}
                                                </span>
                                                {item?.quoteStatus?.toLowerCase() ===
                                                  'rejected' &&
                                                  [
                                                    ...(item?.statusTimeline ||
                                                      []),
                                                  ].sort(
                                                    (a, b) =>
                                                      new Date(
                                                        b.timestamp
                                                      ).getTime() -
                                                      new Date(
                                                        a.timestamp
                                                      ).getTime()
                                                  )?.[0]?.remark && (
                                                    <InfoTooltip
                                                      id="quote status"
                                                      position="top"
                                                      className="ml-2"
                                                    >
                                                      {
                                                        [
                                                          ...(item?.statusTimeline ||
                                                            []),
                                                        ].sort(
                                                          (a, b) =>
                                                            new Date(
                                                              b.timestamp
                                                            ).getTime() -
                                                            new Date(
                                                              a.timestamp
                                                            ).getTime()
                                                        )?.[0]?.remark
                                                      }
                                                    </InfoTooltip>
                                                  )}
                                              </div>
                                            ) : (
                                              <span className="text-gray-400">
                                                -
                                              </span>
                                            )}
                                          </Table.Td>
                                        ) : (
                                          <Table.Td>
                                            <div className="h-12 w-full flex items-center justify-center">
                                              <FaLock className="text-xl text-gray-400" />
                                            </div>
                                          </Table.Td>
                                        )}
                                      </>
                                    );

                                  default:
                                    return null;
                                }
                              }
                            })}
                          </Table.Row>
                        );
                      })
                    )}
                  </Table.Body>
                </Table>
              </div>
            </>
          ) : (
            <>
              <FollowUP
                followUpFor="Quotation"
                followUps={followUps}
                isLoading={isLoading}
                // setFollowUpLength={setFollowUpLength}
              />
            </>
          )}
        </div>
      </div>
      <StatusModal
        openModal={openStatusModal}
        setModalOpen={setOpenStatusModal}
        clickedRow={clickedRow}
      />
    </>
  );
};

export default WithSelectAll(QuotationTable);

import { Checkbox } from 'antd';
import { useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { toast } from 'react-toastify';
import { createObjects } from '../../helperFunction';
import useNaigationBlocker from '../../hooks/useNaigationBlocker';
import usePrefixIds from '../../hooks/usePrefixIds';
import { useGetAllBomsQuery } from '../../slices/assemblyBomApiSlice';
import { useGetAllFormQuery } from '../../slices/createFormapiSlice';
import { useGetDropdownsQuery } from '../../slices/dropdownApiSlice';
import { useLazyQueryTemplateByIdQuery } from '../../slices/dsahboardTemplateApiSlice';
import {
  useAddProductMutation,
  useLazyGetProductByIdForEditQuery,
  useUpdateProductMutation,
} from '../../slices/productApiSlice';
import { gstOptions } from '../../utils/Constant';
import AddCustomGST from '../AddCustomGST';
import Button from '../global/components/Button';
import Select from '../global/components/Select';
import SelectV2 from '../global/components/SelectV2';
import Spinner from '../global/components/Spinner';
import AdditionalUoms from '../v3/InventoryMasters/AdditionalUoms';
import PartTableInspectonForm from '../v3/InventoryMasters/PartTableInspectonForm';
import GeneralInfo from '../v3/ProductMasters/ProductVariantFormComponents/GeneralInfo';
import ItemDetails from '../v3/ProductMasters/ProductVariantFormComponents/ItemDetails';
import ProductionDetails from '../v3/ProductMasters/ProductVariantFormComponents/ProductionDetails';
import NestedTabs from './NestedTabs';

const INITIAL_DATA = {
  name: '',
  uom: '',
  valuationField: '',
  jobTemplate: '',
  packagingCharges: 0,
  shippingCharges: 0,
  hsn: 0,
  rate: 0,
  discount: 0,
  amount: 0,
  cgst: 0,
  igst: 0,
  sgst: 0,
  remarks: '',
  category: '',
  stores: [],
  vendors: [],
  vendor_details: [],
  productionDetails: {},
  bom: [],
  requiresInspections: false,
  inspectionForm: null,
  defaultFormData: {},
  commontToAllInspection: false,
  quantityThreshold: 0,
  errorPercentage: 0,
};
function ManageProductsForm({
  isMobile,
  isTablet,
  hierarchyIdx = 0,
  parent,
  selectedTabs,
  setSelectedTabs,
  refetchParent,
  loadingProductData,
}) {
  const { id } = useParams();
  const navigate = useNavigate();

  const [selectedTemplate, setSelectedTemplate] = useState(null);
  const [input, setInput] = useState({});
  const [files, setFiles] = useState([]);
  const [thumbnail, setThumbnail] = useState(null);
  const [additionalFields, setAdditionalFields] = useState({});
  const [otherThreshold, setOtherThreshold] = useState([]);
  const [templateDropDownModal, setTemplateDropDownModal] = useState(false);
  const [dropdownIdx, setDropdownIdx] = useState(null);
  const [newOptionStatus, setNewOptionStatus] = useState(false);
  const [productData, setProductData] = useState(null);
  const [ShowGSTModal, setShowGSTModal] = useState(false);
  const [gstoptions, setGSToptions] = useState(() => {
    return localStorage.getItem('CustomGSTOptions')
      ? JSON.parse(localStorage.getItem('CustomGSTOptions'))
      : gstOptions;
  });

  const tabSelected = selectedTabs?.[hierarchyIdx];

  const { IdGenComp, idCompData } = usePrefixIds({
    idFor: 'productId',
    templateIndex:
      additionalFields?.idIndex > -1 ? additionalFields?.idIndex : null,
  });

  const { data: allBoms = [] } = useGetAllBomsQuery();
  const [getTemplates, { data: templatesData }] =
    useLazyQueryTemplateByIdQuery();
  const { data: createForms } = useGetAllFormQuery({ query: 'Inspection' });
  const { data: dropdownsData } = useGetDropdownsQuery();
  const [getProductById] = useLazyGetProductByIdForEditQuery();
  const [addNewProduct] = useAddProductMutation();
  const [updateProduct] = useUpdateProductMutation();
  const [additionalUoms, setAdditionalUoms] = useState([]);

  const { allowNavigation } = useNaigationBlocker();

  useEffect(() => {
    if (!id) return;
    if (tabSelected === 'addVariant') {
      if (input?.commontToAllInspection) {
        setInput((prev) => {
          return {
            ...INITIAL_DATA,
            commontToAllInspection: true,
            inspectionData: prev?.inspectionData,
            requiresInspections: prev?.requiresInspections,
            inspectionForm: prev?.inspectionForm,
            defaultFormData: prev?.defaultFormData,
          };
        });
      } else {
        setInput(INITIAL_DATA);
      }
      // setInput({});
      setFiles([]);
      setThumbnail(null);
      setSelectedTemplate('');
      setAdditionalFields({});
      setProductData(null);
      setOtherThreshold([]);
    } else if (!tabSelected && parent) {
      setInput({
        name: parent?.name || '',
        uom: parent?.uom || '',
        valuationField: parent?.valuation || '',
        jobTemplate: parent?.productionDetails?.jobTemplate || '',
        packagingCharges: parent?.additionalCharges?.packaging || 0,
        shippingCharges: parent?.additionalCharges?.shipping || 0,
        hsn: parent?.itemDetails?.hsn || 0,
        rate: parent?.itemDetails?.rate || 0,
        discount: parent?.itemDetails?.discount || 0,
        amount: parent?.itemDetails?.amount || 0,
        cgst: parent?.itemDetails?.cgst || 0,
        igst: parent?.itemDetails?.igst || 0,
        sgst: parent?.itemDetails?.sgst || 0,
        remarks: parent?.remarks || '',
        category: parent?.category || '',
        stores: parent?.stores || [],
        vendors: parent?.vendors || [],
        productionDetails: parent?.productionDetails || {},
        bom: parent?.productionDetails?.bom || [],
        vendor_details: parent?.vendor_details || [],
      });
      setFiles(parent?.attachments || []);
      setOtherThreshold(parent.othersThreshold);
      setSelectedTemplate(parent?.template || null);
      setAdditionalFields(parent?.additionalFields);
      setProductData(null);
      setThumbnail(parent?.thumbNail || null);
    } else if (tabSelected) {
      getProductById({ id: tabSelected }, false)
        .unwrap()
        .then((res) => {
          setProductData(res);
        });
    }
  }, [tabSelected, id, parent, getProductById]); // eslint-disable-line

  useEffect(() => {
    const getCols = async () => {
      const path = '/settings/inventory/products/manage';
      getTemplates({ path });
    };
    getCols();
  }, [getTemplates]);

  const changeVendorDetails = (recordId, name, value) => {
    let temp = input?.vendor_details;
    temp = temp?.map((elem) => {
      if (elem?.vendor === recordId) {
        return {
          ...elem,
          [name]: value,
        };
      } else {
        return {
          ...elem,
        };
      }
    });
    setInput((prev) => ({
      ...prev,
      vendor_details: temp,
    }));
  };

  const inputChangeHandler = (e) => {
    const { name, value } = e.target;

    if (['cgst', 'sgst', 'igst'].includes(name) && value === '+') {
      setShowGSTModal(true);
      return;
    }

    if (name === 'inspectionForm') {
      setInput((prev) => ({
        ...prev,
        [name]: value,
        // createForm: value,
        inspectionForm: value,
      }));
    } else {
      setInput((prev) => {
        return { ...prev, [name]: value };
      });
    }
  };

  const pdfChangeHandler = (e) => {
    for (let i in e) {
      let fileName = e[i].name;
      let fileType = e[i].type;

      const fr = new FileReader();
      if (i === 'length') return;
      fr.readAsDataURL(e[i]);
      fr.addEventListener('load', () => {
        const url = fr.result;
        let data = {
          name: fileName,
          type: fileType,
          data: url,
        };

        setFiles((prev) => [...(prev || []), data]);
      });
    }
  };

  const removePdf = (el) => {
    const filtered = files.filter((item) => item.name !== el.name);
    const deletedFile = files?.filter((file) => {
      return file?.name === el?.name;
    });
    if (deletedFile?._id) {
      setInput((prev) => {
        return {
          ...prev,
          deletedMedia: [...prev.deletedMedia, deletedFile?._id],
        };
      });
    }
    setFiles(filtered);
  };

  const handleSubmit = async (e) => {
    if (e) {
      e.preventDefault();
    }

    if (!input?.category) {
      toast.error('Category is required');
      return;
    }
    if (!input?.name) {
      toast.error('Product Name is required');
      return;
    }
    if (!input?.uom) {
      toast.error('UOM is required');
      return;
    }
    if (input?.category === 'Inhouse Finished Goods') {
      if (!input?.stores?.length) {
        toast.error('Please select at least one store');
        return;
      }
    }
    // else {
    //   if (!input?.vendors?.length) {
    //     toast.error('Please select at least one vendor');
    //     return;
    //   }
    // }

    let bomsInfo = input?.bom?.map((elem) => elem?.value || elem);

    const objInfo = {
      idData: idCompData?.dataToReturn,
      template: selectedTemplate || null,
      name: input?.name || '',
      color: input?.color || '',
      uom: input?.uom || '',
      valuation: input?.valuationField || '',
      additionalCharges: {
        packaging: input?.packagingCharges || 0,
        shipping: input?.shippingCharges || 0,
      },
      itemDetails: {
        hsn: input?.hsn || 0,
        rate: input?.rate || 0,
        discount: input?.discount || 0,
        amount: input?.amount || 0,
        cgst: input?.cgst || 0,
        igst: input?.igst || 0,
        sgst: input?.sgst || 0,
      },
      productionDetails: {
        bom: bomsInfo || [],
        jobTemplate: input?.jobTemplate || null,
        thumbnail: thumbnail || null,
      },
      vendors: input?.vendors || [],
      vendor_details: input?.vendor_details || [],
      createForm: input.inspectionForm,
      inspectionForm: input.inspectionForm,
      requiresInspections: input?.requiresInspections || false,
      defaultFormData: input?.defaultFormData || {},
      commontToAllInspection: input?.commontToAllInspection || false,
      quantityThreshold: 0,
      stores: input?.stores || [],
      category: input?.category || '',
      remarks: input?.remarks || '',
      attachments: files,
      additionalFields: additionalFields || {},
      othersThreshold: otherThreshold,
      additionalUoms:
        additionalUoms?.filter(
          (el) => el.additionalUnit && el.conversionUnit && el.conversionValue
        )?.length > 0
          ? additionalUoms
          : [],
    };

    const addProductFunc = async () => {
      const res = await addNewProduct({ data: objInfo });

      if (res?.data) {
        return res;
      }
      return null;
    };

    // if top level and no id exists means only run when creating a new part
    if (hierarchyIdx === 0 && !id) {
      const res = await addProductFunc();
      if (res?.data) {
        toast.success('Product added successfully', { toastId: 'success' });
        navigate(res?.data?._id);
      }
      // run when adding a variant
    } else if (tabSelected === 'addVariant') {
      objInfo.parent = parent?._id;
      const res = await addProductFunc();
      if (res?.data) {
        refetchParent();
        toast.success('Product variant added successfully', {
          toastId: 'success',
        });
      }
      // run for updating part
    } else {
      await updateProduct({ product: objInfo, id: parent?._id }).unwrap();
      toast.success('Updated successfully', {
        toastId: 'success',
      });
    }
    allowNavigation();
  };

  const handleInputChange = (
    fieldValue,
    fieldName,
    idx,
    colIndex,
    tableRowIndex
  ) => {
    if (tableRowIndex !== undefined && tableRowIndex !== null) {
      setAdditionalFields((prev) => {
        const updatedTemplateData = [...prev?.templateData];
        const fieldWithTableIndex = idx;
        if (fieldWithTableIndex === -1) return prev;

        const updatedTableOptions = {
          ...updatedTemplateData[fieldWithTableIndex]?.tableOptions,
        };

        if (!updatedTableOptions.column) {
          updatedTableOptions.column = [];
        } else {
          updatedTableOptions.column = [...updatedTableOptions.column];
        }

        if (!updatedTableOptions.column[colIndex].selectedOptions) {
          updatedTableOptions.column[colIndex] = {
            columnName: updatedTableOptions.column[colIndex].columnName,
            columnType: updatedTableOptions.column[colIndex].columnType,
            dropdownOptions:
              updatedTableOptions.column[colIndex].dropdownOptions,
            selectedOptions: [],
          };
        }
        const updatedSelectedOptions = [
          ...updatedTableOptions.column[colIndex].selectedOptions,
        ];
        updatedSelectedOptions[tableRowIndex] = fieldValue;

        updatedTableOptions.column[colIndex] = {
          ...updatedTableOptions.column[colIndex],
          selectedOptions: updatedSelectedOptions,
        };

        updatedTemplateData[fieldWithTableIndex] = {
          ...updatedTemplateData[fieldWithTableIndex],
          tableOptions: updatedTableOptions,
        };

        return {
          ...prev,
          templateData: updatedTemplateData,
        };
      });
      return;
    }
    if (fieldValue === '+') {
      setTemplateDropDownModal(true);
      setDropdownIdx(idx);
    } else {
      const updatedAdditionalFields = additionalFields?.templateData?.map(
        (field, index) => {
          if (field?.fieldName === fieldName && index === idx) {
            return {
              ...field,
              fieldValue,
            };
          } else {
            return field;
          }
        }
      );
      setAdditionalFields((prev) => {
        return {
          ...prev,
          templateData: updatedAdditionalFields,
        };
      });
    }
  };

  const refetchProduct = () => {
    if (tabSelected && tabSelected !== 'addVariant')
      getProductById({ id: tabSelected }, false)
        .unwrap()
        .then((res) => setProductData(res));
  };

  const handleSubmitAndGoBack = async () => {
    try {
      if (!input?.name) {
        toast.error('Please enter the name of the Product');
        return;
      }
      if (!input?.category) {
        toast.error('Please select the category of the Product');
        return;
      }
      if (!input?.uom) {
        toast.error('Please select the UOM of the Product');
        return;
      }
      if (!input?.stores?.length) {
        toast.error('Please select at least one store');
        return;
      }

      // else {
      //   if (!input?.vendors?.length) {
      //     toast.error('Please select at least one vendor');
      //     return;
      //   }
      // }
      await handleSubmit();
      navigate('/settings/inventory/products');
    } catch (_err) {
      // do nothing
      // add try catch block to prevent navigation when submit fails
    }
  };

  useEffect(() => {
    if (input?.inspectionForm) {
      let currForm = createForms?.find(
        (form) => form?._id === input?.inspectionForm
      );

      let defaultInitialValues = {};

      currForm?.formData?.forEach((field) => {
        switch (field?.fieldType?.toString()) {
          case 'Date':
            defaultInitialValues[field.fieldName] = {
              fieldType: field.fieldType,
              date: '',
              value: '',
              checkCondition: '',
              filledValue: '',
            };
            break;

          case 'Check':
            defaultInitialValues[field.fieldName] = {
              value: false,
              fieldType: field.fieldType,
              filledValue: false,
            };
            break;

          case 'MultiCheckbox':
            defaultInitialValues[field.fieldName] = {
              fieldType: field.fieldType,
              labelArray: field.labelArray || [],
              value: [],
              filledValue: [],
            };

            break;
          case 'Range':
            defaultInitialValues[field.fieldName] = {
              fieldType: field.fieldType,
              value: '',
              min: 0,
              max: 0,
            };
            break;

          case 'Min-Max':
            defaultInitialValues[field.fieldName] = {
              fieldType: field.fieldType,
              value: '',
              filledMaxValue: 0,
              filledMinValue: 0,
            };
            break;

          case 'Media':
            defaultInitialValues[field.fieldName] = {
              fieldType: field.fieldType,
              value: '',
              filledValue: [],
            };
            break;

          case 'Range Threshold':
            defaultInitialValues[field.fieldName] = {
              fieldType: field.fieldType,
              value: '',
              average: 0,
              margin: 0,
              filledValue: '',
            };
            break;

          case 'Table':
            defaultInitialValues[field.fieldName] = {
              type: field?.fieldType,
              columns: field?.tableOptions?.column || [],
              noOfRows: +field?.tableOptions?.rows || 0,
              newRowNo: 0,
              rows: +field?.tableOptions?.rows || 0,
              row:
                field?.tableOptions?.row ||
                Array.from({ length: field?.tableOptions?.rows }, () => ''),
              rowData: createObjects(
                +field?.tableOptions?.rows || 0,
                field?.tableOptions?.column || []
              ),
            };
            break;

          default:
            defaultInitialValues[field.fieldName] = {
              fieldType: field.fieldType,
              value: '',
              options: field?.fieldOptions || [],
              filledValue: [],
            };

            break;
        }
      });

      if ((parent?.createForm?._id || parent?.createForm) === currForm?._id) {
        setInput((prev) => ({
          ...prev,
          defaultFormData: parent?.defaultFormData,
        }));
      } else {
        setInput((prev) => ({
          ...prev,
          defaultFormData: defaultInitialValues,
        }));
      }
    }
  }, [input?.inspectionForm, id]); //eslint-disable-line

  if (loadingProductData) {
    return (
      <div className="flex justify-center items-center h-screen bg-transparent ">
        <Spinner />
      </div>
    );
  }

  return (
    <div>
      {ShowGSTModal && (
        <AddCustomGST
          setShowModal={setShowGSTModal}
          GstInfo={gstoptions}
          setGSToptions={setGSToptions}
        />
      )}
      {id && (
        <NestedTabs
          hierarchyIdx={hierarchyIdx}
          tabSelected={tabSelected}
          setSelectedTabs={setSelectedTabs}
          variants={parent?.children}
          refetchParent={refetchParent}
        />
      )}
      {!tabSelected || tabSelected === 'addVariant' ? (
        <form onSubmit={handleSubmit}>
          <div className="p-3 space-y-4">
            <div className="bg-white border border-gray-200 rounded-lg p-3">
              <h3 className="text-sm font-medium text-gray-700 mb-3">
                Basic Information
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                <div className="mb-3">
                  <label className="block text-sm font-medium text-gray-600 mb-1">
                    FG Product ID
                  </label>
                  {tabSelected === 'addVariant' || !parent ? (
                    <IdGenComp {...idCompData} />
                  ) : (
                    <p>{parent?.id}</p>
                  )}
                </div>
                <div className="mb-3">
                  <label className="block text-sm font-medium text-gray-600 mb-1">
                    Category
                  </label>
                  <SelectV2
                    value={input?.category || ''}
                    onChange={(e) => {
                      setInput((prev) => ({
                        ...prev,
                        category: e.target.value,
                      }));
                    }}
                    options={[
                      {
                        value: 'Inhouse Finished Goods',
                        label: 'Inhouse Finished Goods',
                      },
                      {
                        value: 'Outsource Finished Goods',
                        label: 'Outsource Finished Goods',
                      },
                    ]}
                  />
                </div>
              </div>
            </div>

            <GeneralInfo
              input={input}
              inputChangeHandler={inputChangeHandler}
              templatesData={templatesData}
              dropdownsData={dropdownsData}
              selectedTemplate={selectedTemplate}
              setSelectedTemplate={setSelectedTemplate}
              additionalFields={additionalFields}
              setAdditionalFields={setAdditionalFields}
              handleInputChange={handleInputChange}
              dropdownIdx={dropdownIdx}
              setDropdownIdx={setDropdownIdx}
              templateDropDownModal={templateDropDownModal}
              setTemplateDropDownModal={setTemplateDropDownModal}
              newOptionStatus={newOptionStatus}
              setNewOptionStatus={setNewOptionStatus}
            />
            <ItemDetails
              input={input}
              inputChangeHandler={inputChangeHandler}
              gstoptions={gstoptions}
              otherThreshold={otherThreshold}
              setOtherThreshold={setOtherThreshold}
              changeVendorDetails={changeVendorDetails}
              setInput={setInput}
            />
            <ProductionDetails
              input={input}
              inputChangeHandler={inputChangeHandler}
              pdfChangeHandler={pdfChangeHandler}
              files={files}
              setFiles={setFiles}
              removePdf={removePdf}
              allBoms={allBoms}
              setInput={setInput}
              thumbnail={thumbnail}
              setThumbnail={setThumbnail}
            />

            {/* Inspection Details */}

            <div className="bg-white border border-gray-200 rounded-lg p-3 flex justify-between items-center">
              <h3 className="text-sm font-medium text-gray-700 mb-3">
                Inspection Details
              </h3>
              <input
                type="checkbox"
                name="requiresInspections"
                checked={input?.requiresInspections}
                onChange={(e) => {
                  const reset = !e.target.checked;
                  setInput((prev) => ({
                    ...prev,
                    requiresInspections: e.target.checked,
                    inspectionForm: reset ? null : prev?.inspectionForm,
                    // createForm: reset ? null : prev?.inspectionForm,
                    inspectionData: reset ? {} : prev?.inspectionData,
                  }));
                }}
              />
            </div>
            {input?.requiresInspections && (
              <section className="grid grid-cols-2 gap-2">
                <div className={`md:w-3/4 w-full`}>
                  <div className="flex gap-x-2">
                    <label className="mb-1 font-semibold text-[#667085]">
                      Common Inspection to its all variants
                    </label>
                    <Checkbox
                      name="commonInspection"
                      disabled={
                        !input?.requiresInspections || !input?.inspectionForm
                      }
                      checked={input?.commontToAllInspection}
                      onChange={(e) => {
                        setInput((prev) => ({
                          ...prev,
                          commontToAllInspection: e.target.checked,
                        }));
                      }}
                    />
                  </div>
                  <label>Inspection Form</label>
                  <Select
                    menuPlacement="auto"
                    disabled={!input?.requiresInspections}
                    name="inspectionForm"
                    options={[
                      // { label: '+ Add New Form', value: '+' },
                      ...(createForms?.map((form) => ({
                        label: form.formName,
                        value: form._id,
                      })) || []),
                    ]}
                    value={input?.inspectionForm?._id || input?.inspectionForm}
                    onChange={inputChangeHandler}
                  />
                </div>
                <div className={`col-span-full`}>
                  <PartTableInspectonForm
                    data={input}
                    setData={setInput}
                    forms={createForms}
                  />
                </div>
              </section>
            )}

            <div className="bg-white border border-gray-200 rounded-lg p-3 flex justify-between items-center">
              <h3 className="text-sm font-medium text-gray-700 mb-3">
                Additional UOM's
              </h3>
              <input
                type="checkbox"
                name="isAdditionalUoms"
                checked={input?.isAdditionalUoms}
                onChange={(e) => {
                  if (!e.target.checked) {
                    setAdditionalUoms([]);
                  } else if (additionalUoms?.length === 0) {
                    setAdditionalUoms([
                      {
                        additionalUnit: '',
                        conversionValue: '',
                        conversionUnit: '',
                      },
                    ]);
                  }
                  setInput((prev) => ({
                    ...prev,
                    isAdditionalUoms: e.target.checked,
                  }));
                }}
              />
            </div>

            {input?.isAdditionalUoms && (
              <AdditionalUoms
                additionalUoms={additionalUoms}
                setAdditionalUoms={setAdditionalUoms}
                dropdownsData={dropdownsData}
                uom={input.uom}
              />
            )}
          </div>
          <div className="bg-gray-50 px-4 py-3 border-t border-gray-100">
            <div className="flex items-center justify-end gap-2">
              <Button type="submit" size="sm">
                Save and Add Variants
              </Button>
              <Button
                color="green"
                type="button"
                size="sm"
                onClick={handleSubmitAndGoBack}
              >
                Submit and Go Back
              </Button>
            </div>
          </div>
        </form>
      ) : (
        <>
          {parent && (
            <ManageProductsForm
              hierarchyIdx={hierarchyIdx + 1}
              isMobile={isMobile}
              isTablet={isTablet}
              parent={productData}
              selectedTabs={selectedTabs}
              setSelectedTabs={setSelectedTabs}
              refetchParent={refetchProduct}
            />
          )}
        </>
      )}
    </div>
  );
}

export default ManageProductsForm;

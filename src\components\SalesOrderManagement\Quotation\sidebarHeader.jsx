import { useContext } from 'react';
import {
  getLocalDateTime,
  handleImage,
  handlePdf,
} from '../../../helperFunction';
import {
  useLazyGetPdfForImageQuery,
  useLazyGetPdfQuery,
} from '../../../slices/pdfApiSlice';
import { Store } from '../../../store/Store';

import {
  ArrowLeftIcon,
  ArrowRightIcon,
  ClockIcon,
  DocumentDuplicateIcon,
  DocumentIcon,
  PencilIcon,
  PhotoIcon,
  TrashIcon,
} from '@heroicons/react/24/outline';
import { BsWhatsapp } from 'react-icons/bs';

const ActionButton = ({
  onClick,
  disabled,
  icon,
  tooltip,
  color = 'text-gray-700',
}) => {
  const Icon = icon;

  return (
    <button
      onClick={onClick}
      disabled={disabled}
      className={`p-2 rounded-full transition-all ${
        disabled
          ? 'opacity-50 cursor-not-allowed'
          : 'hover:bg-gray-100 active:bg-gray-200'
      } group relative`}
    >
      <Icon className={`w-5 h-5 ${color}`} />
      {tooltip && (
        <span className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 hidden group-hover:block bg-gray-800 text-white text-xs px-2 py-1 rounded whitespace-nowrap z-10">
          {tooltip}
        </span>
      )}
    </button>
  );
};

const LoadingSpinner = () => (
  <div className="w-5 h-5 rounded-full border-2 border-gray-300 border-t-blue-500 animate-spin" />
);

const QuotationsidebarHeader = ({
  data,
  setOpenSideBar,
  setIsQuotationEdit,
  setEditData,
  disabled = false,
  fromKanban = false,
  setWhatsappShare,
  fromLead = false,
  setSelectedId,
  setAdditionOptions,
  handleCopy,
  handlePrevClick,
  handleNextClick,
  setShowTable,
  showTable,
  handleSingleDelete,
  historyData,
  showVersionDetails,
  setShowVersionDetails,
}) => {
  const {
    state: { user },
  } = useContext(Store);
  const [getPdf, { isFetching: isFetchingPdf }] = useLazyGetPdfQuery();
  const [getPdfForImage, { isFetching: isFetchingPdfForImage }] =
    useLazyGetPdfForImageQuery();

  // Check if edit operations are allowed
  const isEditAllowed = () => {
    if (!user?.canEditQuotation) {
      return false;
    }
    if (fromLead) {
      return false;
    }
    const findApproveStatus = data?.statusTimeline?.find(
      (item) => item?.status?.toLowerCase() === 'approved'
    );
    if (findApproveStatus && user?.canEditApprovedQuotation) {
      return true;
    }
    return !findApproveStatus;
  };

  const isDeleteAllowed = () => {
    if (!user?.canDeleteQuotation) {
      return false;
    } else {
      return data?.quoteStatus?.toLowerCase() !== 'approved';
    }
  };

  // Action handlers
  const handleEdit = () => {
    setOpenSideBar(false);
    setIsQuotationEdit(true);
    setEditData(data);
  };

  const handleHistory = () => {
    setShowTable(!showTable);
  };

  const handlePdfDownload = () => {
    handlePdf(getPdf, data?._id, 'quotation');
  };

  const handleImageDownload = () => {
    handleImage(getPdfForImage, data?._id, 'quotation', '', [], 'image');
  };

  const handleWhatsappShare = () => {
    const customer = data?.vendorDetails;
    setWhatsappShare(true);
    setSelectedId(data?._id);
    setAdditionOptions([
      {
        name: 'customer',
        options: [
          {
            label: `${customer?.name} (${customer?.mobileNumber?.[0] || customer?.mobileNumber})`,
            value: customer?.mobileNumber?.[0] || customer?.mobileNumber,
          },
        ],
      },
    ]);
    setOpenSideBar(false);
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-4">
        {/* Quote ID with navigation */}
        <div className="flex items-center">
          {!fromKanban && !fromLead && (
            <div className="flex items-center">
              <button
                onClick={handlePrevClick}
                disabled={disabled}
                className={`p-1.5 ${disabled ? 'text-gray-300 cursor-not-allowed' : 'text-gray-500 hover:text-gray-800 hover:bg-gray-100 rounded'}`}
              >
                <ArrowLeftIcon className="w-4 h-4" />
              </button>

              <h1 className="mx-2 text-lg font-semibold text-gray-800">
                {data?.quoteID}
              </h1>

              <button
                onClick={handleNextClick}
                disabled={disabled}
                className={`p-1.5 ${disabled ? 'text-gray-300 cursor-not-allowed' : 'text-gray-500 hover:text-gray-800 hover:bg-gray-100 rounded'}`}
              >
                <ArrowRightIcon className="w-4 h-4" />
              </button>
            </div>
          )}
        </div>

        {/* Actions */}
        {!fromKanban && (
          <div className="flex items-center space-x-1">
            {!fromLead && (
              <ActionButton
                icon={() => <BsWhatsapp className="w-5 h-5 text-green-600" />}
                tooltip="Share"
                onClick={handleWhatsappShare}
              />
            )}

            {isEditAllowed() && (
              <ActionButton
                icon={PencilIcon}
                tooltip="Edit"
                onClick={handleEdit}
              />
            )}

            {!fromLead && (
              <ActionButton
                icon={DocumentDuplicateIcon}
                tooltip="Copy"
                onClick={handleCopy}
              />
            )}

            {!fromLead && (
              <ActionButton
                icon={ClockIcon}
                tooltip="History"
                onClick={handleHistory}
              />
            )}

            <ActionButton
              icon={isFetchingPdf ? LoadingSpinner : DocumentIcon}
              tooltip="Download PDF"
              onClick={handlePdfDownload}
              disabled={isFetchingPdf}
            />

            <ActionButton
              icon={isFetchingPdfForImage ? LoadingSpinner : PhotoIcon}
              tooltip="Download Image"
              onClick={handleImageDownload}
              disabled={isFetchingPdfForImage}
            />

            {isDeleteAllowed() && (
              <ActionButton
                icon={TrashIcon}
                tooltip="Delete"
                onClick={() => {
                  handleSingleDelete(data?.quoteID);
                  setOpenSideBar(false);
                }}
                color="text-red-500"
              />
            )}
          </div>
        )}
      </div>

      {/* History table */}
      {showTable && (
        <div className="mt-5">
          <div className="show-info text-sm flex gap-2 items-center justify-end my-2">
            <div className="w-2 h-2 bg-red-500 rounded-full"></div>
            <span>Indicates Edited Field</span>
          </div>
          <div className="bg-white rounded-md shadow-sm border border-gray-100 overflow-hidden">
            <table className="w-full border-collapse">
              <thead>
                <tr className="bg-gray-50">
                  {[
                    '#',
                    'Version',
                    'Edited/Created By',
                    'Edited/Created At',
                  ].map((heading) => (
                    <th
                      key={heading}
                      className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      {heading}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {historyData?.versionData?.map((version, idx) => {
                  const isSelected = showVersionDetails?._id === version._id;
                  return (
                    <tr
                      key={version._id}
                      className={`hover:bg-gray-50 cursor-pointer ${isSelected ? 'bg-blue-50' : ''}`}
                      onClick={() => setShowVersionDetails(version)}
                    >
                      <td className="px-4 py-3 text-sm text-gray-500">{idx}</td>
                      <td className="px-4 py-3 text-sm font-medium text-gray-900">
                        {version.version === historyData?.versionData?.length
                          ? 'Latest'
                          : version.version}
                      </td>
                      <td className="px-4 py-3 text-sm text-gray-500">
                        {version.editedBy}
                      </td>
                      <td className="px-4 py-3 text-sm text-gray-500">
                        {getLocalDateTime(version?.editedAt)}
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        </div>
      )}
    </div>
  );
};

export default QuotationsidebarHeader;

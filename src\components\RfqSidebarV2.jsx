import {
  CalendarOutlined,
  CreditCardOutlined,
  FileDoneOutlined,
  HomeOutlined,
  IdcardOutlined,
} from '@ant-design/icons';
import { XMarkIcon } from '@heroicons/react/24/outline';
import { <PERSON><PERSON>, Card, Tooltip, Typography } from 'antd';
import { Trash } from 'lucide-react';
import { useContext, useEffect, useState } from 'react';
import Marquee from 'react-fast-marquee';
import { IoIosAttach } from 'react-icons/io';
import { LiaEdit } from 'react-icons/lia';
import { MdCopyAll } from 'react-icons/md';
import { PiFilePdfDuotone } from 'react-icons/pi';
import { VscSaveAs } from 'react-icons/vsc';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import pdf from '../assets/images/pdf.png';
import {
  getCorrespondingConversionFactor,
  getPartVariantName,
  getProductVariantName,
  handlePdf,
} from '../helperFunction';
import { useLazyGetPdfQuery } from '../slices/pdfApiSlice';
import { useUpdateVedorCommentMutation } from '../slices/rfqSlice';
import { Store } from '../store/Store';
import DragAndDrop from './global/components/DragAndDrop';
import Input from './global/components/Input';
import Modal from './global/components/Modal';
import Select from './global/components/Select';
import Spinner from './global/components/Spinner';
import TermAndConditionUi from './Ui/TermAndConditionUi';
import MediaDetails from './v3/global/components/MediaDetails';
const { Title, Text, Paragraph } = Typography;

function RfqSidevarV2({
  partSidebarData, //all required data for sidebar
  handleUpdateStatus, //used to update the rfq status
  setMailData, //used to update the mailData
  setShowEmailModal, // used to open mail modal
  setShowSidebar, //used to open and close the sidebar
  handleDelete, // handle to delete the the rfq
  fromKanban = false,
  setPreviewMed,
  setShowFullScreenModal,
  setReadMore,
}) {
  const [vendorInput, setVendorInput] = useState([]);
  const [media, setMedia] = useState([]);
  const [isOpenModal, setIsOpenModal] = useState(false);
  const [activeIdx, setActiveIdx] = useState(null);
  const [isEdit, setIsEdit] = useState(false);
  const [showEditIcon, setShowEditIcon] = useState(true);
  const [getPdf, { isFetching: isFetchingPdf }] = useLazyGetPdfQuery();
  const [updateVedorComment] = useUpdateVedorCommentMutation();
  const navigate = useNavigate();
  const {
    state: { user },
  } = useContext(Store);
  /*
  the code is responsible for setting up the initial state of an input field based on the presence or absence of vendor comments. If vendor comments exist, it populates the input field with those comments and sets the component to a non-editable state. If no vendor comments exist, it creates placeholders for vendors to add comments and sets the component to an editable state.
  */
  useEffect(() => {
    if (partSidebarData?.vendorsComment?.length > 0) {
      setVendorInput(
        partSidebarData?.vendorsComment.map((el) => {
          const { _id, ...rest } = el;
          return rest;
        })
      );
      setIsEdit(false);
      setShowEditIcon(true);
    } else {
      if (partSidebarData?.vendor) {
        let data = [];
        partSidebarData?.vendor.forEach((el) => {
          let info = {
            vendorId: el?._id,
            media: [],
            comment: '',
          };
          data.push(info);
        });
        setVendorInput(data);
        setShowEditIcon(false);
        setIsEdit(true);
      }
    }
  }, [partSidebarData]);
  /*
  The purpose of this code is to prepare the necessary data for sending an email to vendors. When the handleSendMail function is called, it first shows an email modal and hides the sidebar component. It then filters the list of vendors to include only those with valid email addresses and creates a comma-separated string of those email addresses. Finally, it updates the mailData state object with the list of recipient email addresses, likely to be used in the email modal for sending the actual email.
  */
  const handleSendMail = () => {
    setShowEmailModal(true);
    setShowSidebar(false);

    const Vendoremails = partSidebarData?.vendor
      ?.filter((vendor) => vendor?.email.length > 0)
      .map((vendor) => vendor?.email);
    if (Vendoremails?.length !== 0) {
      setMailData((prev) => ({
        ...prev,
        receiver: Vendoremails?.join(','),
      }));
    }
  };
  /*this code is responsible for handling file uploads. It reads each selected file as a base64-encoded data URL and stores the file name, type, and data in the component's state. This data could then be used for various purposes, such as displaying previews of the uploaded files or sending the files to a server for further processing.*/
  const changeHandler = (e) => {
    for (let i in e) {
      const fr = new FileReader();
      if (i === 'length') return;
      fr.readAsDataURL(e[i]);
      let name = e[i].name;
      let type = e[i].type;
      fr.addEventListener('load', () => {
        setMedia((prev) => [...prev, { name, type, data: fr.result }]);
        setShowEditIcon(false);
      });
    }
  };
  /*this code is responsible for removing a specific media file from the list of attachments. It takes the index of the file to be removed as a parameter and updates the media state by filtering out the file at the specified index.*/
  const removeHandler = (idx) => {
    setShowEditIcon(false);
    setMedia((prev) => prev.filter((_, i) => i !== idx));
  };
  /*this code is responsible for submitting the media files and comments for each vendor. It first updates the vendorInput state with the current media files and comments for the active vendor. Then, it closes the modal, resets the media state, and sets the activeIdx to null.*/

  const handleSubmit = () => {
    let data = vendorInput;

    data[activeIdx] = {
      ...data[activeIdx],
      media: media,
    };

    setVendorInput(data);
    setIsOpenModal(false);
    setMedia([]);
    setActiveIdx(null);
  };
  /*
  In summary, this code is responsible for updating a vendor's comment by calling an asynchronous function (updateVedorComment) with the necessary data ( id and vendorInput). If the update is successful, it updates the component's state (isEdit and showEditIcon) and displays a success notification to the user.
  */
  const handleSave = async () => {
    let id = partSidebarData?._id;
    const res = await updateVedorComment({
      id,
      data: vendorInput,
    }).unwrap();
    if (res) {
      setIsEdit(false);
      setShowEditIcon(true);
      toast.success('Comment Updated');
    }
  };
  // Check if edit operations are allowed
  const isEditAllowed = () => {
    if (!user?.canEditRequestForQuotation) {
      return false;
    }
    if (partSidebarData?.status?.toLowerCase() === 'approved') {
      if (user?.canEditApprovedRequestForQuotation) {
        return true;
      }
      return false;
    } else {
      return true;
    }
  };

  const isDeleteAllowed = () => {
    if (!user?.canDeleteRequestForQuotation) {
      return false;
    } else {
      return partSidebarData?.status?.toLowerCase() !== 'approved';
    }
  };
  const renderIconButton = (
    onClick,
    tooltipText,
    IconComponent,
    additionalClass = ''
  ) => (
    <Tooltip title={tooltipText} placement="top">
      <Button
        type="text"
        onClick={onClick}
        className={`flex items-center justify-center ${additionalClass}`}
        icon={IconComponent}
      />
    </Tooltip>
  );

  return (
    <>
      {/* this is modal used to add and update the media for each vendor  */}
      {isOpenModal && (
        <Modal
          title="Attachments"
          showModal={isOpenModal}
          onCloseModal={() => {
            setIsOpenModal(false);
          }}
          onSubmit={handleSubmit}
          canSubmit={false}
          modalWidth="30%"
          modalTop="78.5%"
          modalLeft="10%"
        >
          {() => {
            return (
              <div className="w-full">
                <DragAndDrop
                  className="h-10  text-xs  w-[100%]  "
                  accept="image/*, application/pdf"
                  onChange={(e) => changeHandler(e)}
                  multiple={true}
                />

                <div>
                  <div className="flex gap-4 flex-wrap mt-2 overflow-y-scroll">
                    {media?.map((item, uIdx) => (
                      <section
                        key={uIdx}
                        className="p-2 border rounded-md w-[170px] flex flex-col justify-between"
                      >
                        <section
                          className="h-full min-w-[100px] max-w-[160px] hover:cursor-pointer "
                          onClick={() => {
                            setPreviewMed(item);
                            setShowFullScreenModal(true);
                          }}
                        >
                          <img
                            className="w-[150px] aspect-video object-contain"
                            src={
                              item?.type === 'application/pdf'
                                ? pdf
                                : item?.data
                            }
                            alt=""
                          />
                        </section>

                        <section className="flex justify-between items-center text-sm mt-2">
                          <Marquee className="w-">{item.name}</Marquee>
                          <button
                            type="button"
                            onClick={() => removeHandler(uIdx)}
                            className="outline-none text-red-primary hover:text-white hover:bg-red-primary rounded px-2 py-1"
                          >
                            <XMarkIcon className="h-4 w-4" />
                          </button>
                        </section>
                      </section>
                    ))}
                  </div>
                </div>
              </div>
            );
          }}
        </Modal>
      )}
      {!fromKanban && (
        <div className="flex items-center justify-end gap-2">
          {/* Status Selector */}
          <div className="w-48">
            <Select
              value={partSidebarData?.status}
              options={[
                { label: 'Pending', value: 'pending' },
                { label: 'Mail Sent', value: 'mail sent' },
                { label: 'Lookup', value: 'lookup' },
                { label: 'Approve', value: 'approved' },
                { label: 'Reject', value: 'rejected' },
              ]}
              onChange={(e) =>
                handleUpdateStatus(e.target.value, partSidebarData?._id)
              }
              className="w-full"
            />
          </div>

          {/* Edit Button */}
          {isEditAllowed() &&
            renderIconButton(
              () =>
                navigate(
                  `/purchase/requestforquotation/createv2/${partSidebarData._id}`
                ),
              'Edit',
              <LiaEdit className="w-5 h-5" />
            )}

          {/* PDF Generator */}
          {isFetchingPdf ? (
            <div className="cursor-not-allowed">
              <Spinner size={5} />
            </div>
          ) : (
            renderIconButton(
              () => handlePdf(getPdf, partSidebarData?._id, 'RFQ'),
              'PDF',
              <PiFilePdfDuotone className="w-5 h-5" />
            )
          )}

          {/* Delete Button */}
          {isDeleteAllowed() &&
            renderIconButton(
              () => handleDelete(partSidebarData._id),
              'Delete',
              <Trash className=" text-red-500 w-5 h-5" />,
              '!text-red-500'
            )}

          {/* Copy Button */}
          {renderIconButton(
            () =>
              navigate(
                `/purchase/requestforquotation/createv2/${partSidebarData._id}?isCopy=true`
              ),
            'Copy',
            <MdCopyAll className="w-5 h-5" />
          )}
        </div>
      )}
      <section className="mt-1">
        <div className="grid gap-6 border rounded-lg p-4">
          <div className="flex items-center justify-between border-b pb-2">
            <Text className="flex items-center gap-3 text-gray-600">
              <CalendarOutlined className="text-indigo-500 text-xl" />
              <span className="font-medium">Creation Date</span>
            </Text>
            <Text className="text-gray-800 font-medium">
              {new Date(partSidebarData?.createdAt).toLocaleDateString('en-IN')}
            </Text>
          </div>

          <div className="flex items-center justify-between border-b pb-2">
            <Text className="flex items-center gap-3 text-gray-600">
              <IdcardOutlined className="text-purple-500 text-xl" />
              <span className="font-medium">RFQ ID</span>
            </Text>
            <Text className="text-gray-800 font-medium">
              {partSidebarData?.id}
            </Text>
          </div>

          <div className="flex items-center justify-between border-b pb-2">
            <Text className="flex items-center gap-3 text-gray-600">
              <FileDoneOutlined className="text-green-500 text-xl" />
              <span className="font-medium">Status</span>
            </Text>
            <Text className="px-3 py-1 rounded-full bg-green-100 text-green-700 font-medium">
              {partSidebarData?.status}
            </Text>
          </div>

          <div className="flex items-center justify-between border-b pb-2">
            <Text className="flex items-center gap-3 text-gray-600">
              <HomeOutlined className="text-blue-500 text-xl" />
              <span className="font-medium">Delivery Address</span>
            </Text>
            <Text className="text-gray-800 font-medium break-all max-w-[50%]">
              {partSidebarData?.deliveryAddress}
            </Text>
          </div>

          <div className="flex items-center justify-between border-b pb-2">
            <Text className="flex items-center gap-3 text-gray-600">
              <CalendarOutlined className="text-orange-500 text-xl" />
              <span className="font-medium">Delivery Date</span>
            </Text>
            <Text className="text-gray-800 font-medium">
              {partSidebarData?.deliveryDate
                ? new Date(partSidebarData?.deliveryDate).toLocaleDateString(
                    'en-IN'
                  )
                : 'NA'}
            </Text>
          </div>

          <div className="flex items-center justify-between border-b pb-2">
            <Text className="flex items-center gap-3 text-gray-600">
              <CreditCardOutlined className="text-pink-500 text-xl" />
              <span className="font-medium">Payment Term</span>
            </Text>
            <Text className="text-gray-800 font-medium">
              {partSidebarData?.paymentTerm}
            </Text>
          </div>
        </div>
      </section>

      <section className="mt-4">
        {partSidebarData?.vendor?.length > 0 && (
          <div className="h-full">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4 gap-2">
              <Title level={5} className="text-gray-800 text-lg font-semibold">
                Vendor Details
              </Title>
              <div className="flex gap-2 self-start sm:self-center">
                {showEditIcon ? (
                  <button
                    onClick={() => {
                      setIsEdit(true);
                      setShowEditIcon(false);
                    }}
                    className="p-2 hover:bg-indigo-100 rounded-full transition-all duration-200 shadow-sm hover:shadow-md"
                    aria-label="Edit vendor details"
                  >
                    <LiaEdit title="Edit" className="w-5 h-5 text-indigo-600" />
                  </button>
                ) : (
                  <button
                    onClick={handleSave}
                    className="p-2 hover:bg-green-100 rounded-full transition-all duration-200 shadow-sm hover:shadow-md"
                    aria-label="Save changes"
                  >
                    <VscSaveAs className="w-5 h-5 text-green-600" />
                  </button>
                )}
              </div>
            </div>

            {/* Desktop Table View */}
            <div className="overflow-hidden rounded-xl border border-gray-200">
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="bg-gradient-to-r from-gray-50 to-gray-100">
                      {[
                        'Vendor ID',
                        'Name',
                        'Address',
                        'GST No',
                        'Contact',
                        'Comment',
                        'Media',
                      ].map((header) => (
                        <th
                          key={header}
                          className="px-4 py-3 text-xs font-semibold text-gray-700 uppercase tracking-wider text-left"
                        >
                          {header}
                        </th>
                      ))}
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200">
                    {partSidebarData?.vendor?.map((vendor, idx) => (
                      <tr
                        key={idx}
                        className="hover:bg-gray-50 transition-colors duration-150"
                      >
                        <td className="px-4 py-4 text-sm text-gray-600 font-medium">
                          {vendor?.id || '-'}
                        </td>
                        <td className="px-4 py-4 text-sm text-gray-900 font-medium">
                          {vendor?.name || '-'}
                        </td>
                        <td className="px-4 py-4 text-sm text-gray-600 max-w-xs">
                          <div
                            className="truncate"
                            title={vendor?.address?.join(', ')}
                          >
                            {vendor?.address?.join(', ') || '-'}
                          </div>
                        </td>
                        <td className="px-4 py-4 text-sm text-gray-600">
                          {vendor?.gstin || '-'}
                        </td>
                        <td className="px-4 py-4 text-sm text-gray-600">
                          {vendor?.contact?.join(', ') || '-'}
                        </td>
                        <td className="px-4 py-4 text-sm">
                          {isEdit ? (
                            <Input
                              type="text"
                              className="w-40 border-indigo-200 focus:border-indigo-500 focus:ring-2 focus:ring-indigo-200 rounded-lg transition-all duration-200"
                              value={vendorInput[idx]?.comment}
                              onChange={(e) => {
                                setVendorInput((prev) => {
                                  const data = [...prev];
                                  data[idx] = {
                                    ...data[idx],
                                    comment: e.target.value,
                                  };
                                  return data;
                                });
                                setShowEditIcon(false);
                              }}
                              placeholder="Add comment..."
                            />
                          ) : (
                            <span className="text-gray-600">
                              {vendorInput[idx]?.comment || '-'}
                            </span>
                          )}
                        </td>
                        <td className="px-4 py-4">
                          <button
                            onClick={() => {
                              setIsOpenModal(true);
                              if (partSidebarData?.vendorsComment) {
                                setMedia(vendorInput[idx]?.media || []);
                              }
                              setActiveIdx(idx);
                            }}
                            className="flex items-center justify-center w-full p-2 rounded-lg hover:bg-gray-50 transition-all duration-200"
                            aria-label="Manage media files"
                          >
                            {vendorInput[idx]?.media?.length > 0 ? (
                              <span className="px-3 py-1 text-xs font-medium text-indigo-600 bg-indigo-50 rounded-full whitespace-nowrap border border-indigo-200">
                                {vendorInput[idx]?.media?.length} files
                              </span>
                            ) : (
                              <IoIosAttach className="w-5 h-5 text-gray-400 hover:text-indigo-500 transition-colors" />
                            )}
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        )}

        {/* Items Details Section */}
        <div className="space-y-4 mt-4">
          {(partSidebarData?.part?.length > 0 ||
            partSidebarData?.partVariants?.length > 0 ||
            partSidebarData?.product?.length > 0 ||
            partSidebarData?.productVariants?.length > 0) && (
            <div className="mt-2">
              <Title
                level={5}
                className="text-gray-800 text-lg font-semibold mb-4"
              >
                Items Details
              </Title>
              <div className="w-full border border-gray-200 rounded-xl overflow-hidden">
                <div className="overflow-x-auto">
                  <table className="w-full min-w-full divide-y divide-gray-200">
                    <thead>
                      <tr className="bg-gradient-to-r from-gray-50 to-gray-100">
                        {['Item', 'UOM', 'Quantity'].map((header) => (
                          <th
                            key={header}
                            className="text-left px-6 py-4 text-xs font-semibold text-gray-700 uppercase tracking-wider"
                          >
                            {header}
                          </th>
                        ))}
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {[
                        { data: partSidebarData?.part, type: 'Part' },
                        {
                          data: partSidebarData?.partVariants,
                          type: 'PartVariant',
                        },
                        { data: partSidebarData?.product, type: 'Product' },
                        {
                          data: partSidebarData?.productVariants,
                          type: 'ProductVariant',
                        },
                      ].map(({ data, type }) =>
                        data?.map((i, idx) => {
                          const conversion = getCorrespondingConversionFactor(
                            i?.uom || i.item.uom,
                            i?.item
                          );
                          return (
                            <tr
                              key={`${type}-${idx}`}
                              className="hover:bg-gray-50 transition-colors duration-150"
                            >
                              <td className="px-6 py-4">
                                <div className="text-sm text-gray-900 font-medium max-w-xs">
                                  <div className="break-words">
                                    {i?.manualEntry ||
                                      (type === 'Part'
                                        ? i?.item?.name
                                        : type === 'PartVariant'
                                          ? getPartVariantName(i?.item)
                                          : type === 'Product'
                                            ? i?.item?.name
                                            : type === 'ProductVariant'
                                              ? getProductVariantName(i?.item)
                                              : null)}
                                  </div>
                                  <div className="text-xs text-gray-500 mt-1">
                                    {type}
                                  </div>
                                </div>
                              </td>
                              <td className="px-6 py-4">
                                <div className="text-sm text-gray-700">
                                  <span className="font-medium">
                                    {i.uom || i.item.uom}
                                  </span>
                                  {conversion && (
                                    <div className="text-xs text-gray-500 mt-1">
                                      = {conversion?.conversionValue}{' '}
                                      {conversion?.conversionUnit}
                                    </div>
                                  )}
                                </div>
                              </td>
                              <td className="px-6 py-4">
                                <div className="text-sm font-semibold text-gray-900">
                                  {i.quantity}
                                </div>
                              </td>
                            </tr>
                          );
                        })
                      )}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          )}
        </div>
      </section>
      {/* term and condition section */}
      <TermAndConditionUi
        termsAndConditions={partSidebarData?.termsAndConditions}
      />

      <div className="mt-2">
        <Title level={5} className="text-gray-600">
          Additional Comments
        </Title>
        <Card
          bordered={false}
          className={`mt-2 bg-gray-50 py-2 text-gray-600 ${
            fromKanban ? 'w-[46.5rem]' : 'w-full'
          }`}
        >
          <Paragraph className="mb-0 break-words">
            {partSidebarData?.comments || 'No comments provided.'}
          </Paragraph>
        </Card>
      </div>
      {/* handle to open media modal */}
      {partSidebarData?.attachments?.length > 0 && (
        <div className="mt-2">
          <MediaDetails
            files={partSidebarData?.attachments}
            setOpenSideBar={setShowSidebar}
            setReadMore={setReadMore}
          />
        </div>
      )}
      {/* handle to open vendor modal */}
      <Button onClick={handleSendMail} type="primary" className="mt-2">
        Send Mail
      </Button>
    </>
  );
}

export default RfqSidevarV2;

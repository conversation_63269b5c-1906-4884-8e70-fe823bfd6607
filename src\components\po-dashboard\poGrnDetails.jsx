import { Collapse, Table } from 'antd';
import { getLocalDateTime } from '../../helperFunction';

const pendingQuantityMap = {};

const PoGrnDetails = ({ grnSidebarData, showGrnSidebar }) => {
  const getGrnStatus = (orderQty, receivedQty) => {
    return orderQty === receivedQty
      ? 'Completed'
      : receivedQty < orderQty
        ? 'Partially Completed'
        : 'Excess Material';
  };

  const columns = [
    {
      title: 'Product Name',
      dataIndex: 'productName',
      key: 'productName',
      render: (text) => <span className="text-sm">{text}</span>,
    },
    {
      title: 'Order Quantity',
      dataIndex: 'orderQuantity',
      key: 'orderQuantity',
      render: (text) => <span className="text-sm">{text}</span>,
    },
    {
      title: 'Pending Quantity',
      dataIndex: 'pendingQuantity',
      key: 'pendingQuantity',
      render: (text) => <span className="text-sm">{text}</span>,
    },
    {
      title: 'Received Quantity',
      dataIndex: 'receivedQuantity',
      key: 'receivedQuantity',
      render: (text) => <span className="text-sm">{text}</span>,
    },
    {
      title: 'Remaining Quantity',
      dataIndex: 'remainingQuantity',
      key: 'remainingQuantity',
      render: (text) => <span className="text-sm">{text}</span>,
    },
    {
      title: 'Excess Quantity',
      dataIndex: 'excessQuantity',
      key: 'excessQuantity',
      render: (text) => <span className="text-sm">{text}</span>,
    },
  ];

  const generateTableData = (grn, showGrnSidebar, grnIndex) => {
    const items = showGrnSidebar?.items || [];
    const inpages = grn?.inpages || [];

    return items.map((item, idx) => {
      const grnItemId = item?.item?._id || item?.item?.value;
      const pendingQuantity =
        grnIndex === 0
          ? item?.quantity || 0
          : pendingQuantityMap?.[grnItemId] || 0;
      const receivedQuantity =
        inpages?.find((i) => (i?.part || i?.Product) === grnItemId)?.quantity ||
        0;
      const remainingQuantity = pendingQuantity - receivedQuantity;

      pendingQuantityMap[grnItemId] =
        remainingQuantity < 0 ? 0 : remainingQuantity;

      return {
        key: idx,
        productName: item.item?.name,
        orderQuantity: item.quantity,
        pendingQuantity,
        receivedQuantity,
        remainingQuantity: remainingQuantity < 0 ? 0 : remainingQuantity,
        excessQuantity:
          pendingQuantity < receivedQuantity
            ? receivedQuantity - pendingQuantity
            : 0,
      };
    });
  };

  const renderGrnDetails = (grn, grnIndex) => {
    const receivedQty =
      grn?.inpages?.reduce((acc, item) => acc + item.quantity, 0) || 0;
    const orderQty =
      showGrnSidebar?.items?.reduce((acc, item) => acc + item.quantity, 0) || 0;
    const grnStatus = getGrnStatus(orderQty, receivedQty);
    const tableData = generateTableData(grn, showGrnSidebar, grnIndex);

    return (
      <div className="p-4">
        <div className="grid grid-cols-2 gap-4 mb-4">
          <div>
            <p className="font-semibold">Received By</p>
            <p>{grn?.received_by || '-'}</p>
          </div>
          <div>
            <p className="font-semibold">Form Status</p>
            <p>Pending</p>
          </div>
          <div>
            <p className="font-semibold">GRN Status</p>
            <p>{grnStatus}</p>
          </div>
        </div>

        <div className="w-full overflow-auto">
          <Table
            columns={columns}
            dataSource={tableData}
            pagination={false}
            className="mt-4"
          />
        </div>
      </div>
    );
  };

  return (
    <Collapse className="w-full">
      {grnSidebarData?.map((grn, index) => (
        <Collapse.Panel
          header={
            <div>
              <p className="font-semibold">GRN ID: {grn.id}</p>
              <p>{getLocalDateTime(grn.createdAt)}</p>
            </div>
          }
          key={grn.id}
          className="text-sm"
        >
          {renderGrnDetails(grn, index)}
        </Collapse.Panel>
      ))}
    </Collapse>
  );
};

export default PoGrnDetails;

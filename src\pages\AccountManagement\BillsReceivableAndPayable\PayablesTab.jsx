import { DeleteOutlined, EditOutlined } from '@ant-design/icons';
import { Button, Space, Table, Tooltip } from 'antd';
import { useState } from 'react';
import { toast } from 'react-toastify';
import Pagination from '../../../components/global/components/Pagination';
import SelectV2 from '../../../components/global/components/SelectV2';
import {
  useDeleteBillMutation,
  useUpdateBillStatusMutation,
} from '../../../slices/AccountManagement/billsApiSlice';
import { customConfirm } from '../../../utils/customConfirm';
import BillSidebar from './BillSidebar';

const PayablesTab = ({
  bills,
  onEdit,
  isLoading,
  total,
  totalPages,
  limit,
  setLimit,
  page,
  setPage,
}) => {
  const [openSideBar, setOpenSideBar] = useState(false);
  const [selectedBillId, setSelectedBillId] = useState(null);
  const [updateBillStatus] = useUpdateBillStatusMutation();
  const [deleteBill] = useDeleteBillMutation();

  const statusOptions = ['Pending', 'Paid', 'Overdue', 'Cancelled'];

  const handleStatusChange = async (billId, newStatus) => {
    try {
      await updateBillStatus({ id: billId, status: newStatus });
      toast.success('Bill status updated successfully');
    } catch (error) {
      toast.error('Failed to update bill status');
    }
  };
  const handleDelete = async (billId) => {
    const confirm = await customConfirm(
      'Are you sure you want to delete this bill?',
      'error'
    );
    if (!confirm) return;
    try {
      await deleteBill({ id: billId });
      toast.success('Bill deleted successfully');
    } catch (error) {
      toast.error('Failed to delete bill');
    }
  };

  const handleClick = (id) => {
    setOpenSideBar(true);
    setSelectedBillId(id);
  };

  const columns = [
    {
      title: 'Bill Id',
      key: 'payableBillId',
      render: (_, record) => (
        <span
          className="font-medium text-blue-500 cursor-pointer"
          onClick={() => handleClick(record?._id)}
        >
          {record.payableBillId}
        </span>
      ),
    },
    {
      title: 'Vendor',
      key: 'vendor',
      render: (_, record) => (
        <span
          className="font-medium text-blue-500 cursor-pointer"
          onClick={() => handleClick(record?._id)}
        >
          {record.vendor?.name || 'N/A'}
        </span>
      ),
    },
    {
      title: 'Amount',
      dataIndex: 'totalAmount',
      key: 'totalAmount',
      render: (amount) => (
        <span className="font-semibold text-red-600">₹{amount || 0}</span>
      ),
      sorter: (a, b) => (a.totalAmount || 0) - (b.totalAmount || 0),
    },
    {
      title: 'Date',
      dataIndex: 'date',
      key: 'date',
      render: (date) => new Date(date).toLocaleDateString(),
      sorter: (a, b) => new Date(a.date).getTime() - new Date(b.date).getTime(),
    },
    {
      title: 'Due Date',
      dataIndex: 'dueOn',
      key: 'dueOn',
      render: (date) => new Date(date).toLocaleDateString(),
      sorter: (a, b) =>
        new Date(a.dueOn).getTime() - new Date(b.dueOn).getTime(),
    },
    {
      title: 'Overdue Days',
      dataIndex: 'overdueByDays',
      key: 'overdueByDays',
      render: (days) => (
        <span
          className={`font-medium ${
            days > 0
              ? 'text-red-600'
              : days === 0
                ? 'text-orange-600'
                : 'text-gray-500'
          }`}
        >
          {days > 0 ? `+${days}` : days === 0 ? '0' : days} days
        </span>
      ),
      sorter: (a, b) => a.overdueByDays - b.overdueByDays,
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status, record) => (
        <SelectV2
          value={status}
          onChange={(e) => handleStatusChange(record._id, e.target.value)}
          size="small"
          menuPosition="fixed"
          className="w-full max-w-2"
          options={statusOptions?.map((el) => ({
            label: el,
            value: el,
          }))}
        />
      ),
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 60,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="Edit Ledger">
            <Button
              type="text"
              icon={<EditOutlined />}
              size="small"
              onClick={() => {
                onEdit(record._id);
              }}
              style={{ color: '#52c41a' }}
            />
          </Tooltip>
          <Tooltip title="Delete Ledger">
            <Button
              type="text"
              icon={<DeleteOutlined />}
              size="small"
              danger
              onClick={() => handleDelete(record._id)}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  return (
    <div className="p-6">
      <Table
        columns={columns}
        dataSource={bills}
        rowKey="_id"
        pagination={false}
        scroll={{ x: true }}
        loading={isLoading}
      />
      <Pagination
        limit={limit}
        page={page}
        totalPages={totalPages}
        totalResults={total}
        setPage={setPage}
        setLimit={setLimit}
        className={`w-full`}
      />
      {openSideBar && (
        <BillSidebar
          id={selectedBillId}
          openSidebar={openSideBar}
          setOpenSidebar={setOpenSideBar}
          onEdit={onEdit}
        />
      )}
    </div>
  );
};

export default PayablesTab;

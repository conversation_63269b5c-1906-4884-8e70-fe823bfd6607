import { apiSlice } from './apiSlice';
const baseRoute = '/v1/sales/quotation';

export const quotationApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    getQuotations: builder.query({
      query: () => baseRoute,
      providesTags: ['Quotations'],
    }),
    getQuotationInfoTileData: builder.query({
      query: () => baseRoute + '/getTileInfo',
      providesTags: ['Quotations'],
    }),
    getQuotationPages: builder.query({
      query: ({
        page = 1,
        limit = 1,
        expired = '',
        filters,
        debounceSearch = '',
        type = '',
        field = '',
      }) =>
        baseRoute +
        `/query?page=${page}&limit=${limit}&expired=${expired}&filters=${filters}&searchTerm=${debounceSearch}&type=${type}&field=${field}`,
      providesTags: ['Quotations'],
    }),

    addQuotation: builder.mutation({
      query: (quotation) => ({
        url: baseRoute + '/add',
        method: 'POST',
        body: quotation,
      }),
      invalidatesTags: ['Quotations', 'PrefixIds'],
    }),
    updateQuotations: builder.mutation({
      query: ({ data, id }) => ({
        url: baseRoute + `/update/${id}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: [
        'Quotations',
        'PendingDatas',
        'EditHistory',
        'CustomerOrders',
      ],
    }),
    deleteQuotations: builder.mutation({
      query: ({ id }) => ({
        url: baseRoute + `/delete/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['Quotations'],
    }),
    deleteManyQuotations: builder.mutation({
      query: ({ ids }) => ({
        url: baseRoute + '/',
        method: 'DELETE',
        body: { ids },
      }),
      invalidatesTags: ['Quotations'],
    }),
    sendQuotation: builder.mutation({
      query: (data) => ({
        url: baseRoute + '/sendmail',
        method: 'POST',
        body: data,
      }),
      invalidatesTags: ['Quotations'],
    }),
    getLatestQuotation: builder.query({
      query: () => ({
        url: `${baseRoute}/latest`,
        method: 'GET',
      }),
      providesTags: ['Quotations'],
    }),
    createQuotationStatus: builder.mutation({
      query: (data) => ({
        url: `${baseRoute}/status`,
        method: 'POST',
        body: data,
      }),
      invalidatesTags: ['Quotations', 'EditHistory', 'CustomerOrders'],
    }),
    getQuotationStatuses: builder.query({
      query: () => `${baseRoute}/status`,
      providesTags: ['Quotations'],
    }),
    deleteQuotationStatus: builder.mutation({
      query: ({ status }) => ({
        url: `${baseRoute}/status`,
        body: { status },
        method: 'DELETE',
      }),
      invalidatesTags: ['Quotations', 'CustomerOrders'],
    }),
    updateStatusOfQuotation: builder.mutation({
      query: ({ id, data }) => ({
        url: `${baseRoute}/status/${id}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: ['Quotations', 'EditHistory', 'CustomerOrders'],
    }),
    archiveQuotation: builder.mutation({
      query: ({ ids }) => ({
        url: `${baseRoute}/archived`,
        method: 'POST',
        body: { ids },
      }),
      invalidatesTags: ['Quotations'],
    }),
    getQuotationFilterOptions: builder.query({
      query: () => `${baseRoute}/filterOptions`,
      providesTags: ['Quotations'],
    }),
    getQuotationsByCustomer: builder.query({
      query: ({ page = 1, limit = 10, field, searchTerm }) => ({
        url: `${baseRoute}/get-quotation-by-customer?page=${page}&limit=${limit}&field=${field}&searchTerm=${searchTerm}`,
        method: 'GET',
      }),
      providesTags: ['Quotations'],
    }),
    getQuotationById: builder.query({
      query: ({ id }) => ({
        url: `${baseRoute}/get-quotation-by-id/${id}`,
        method: 'GET',
      }),
      providesTags: ['Quotations'],
    }),
  }),
});

export const {
  useGetQuotationsQuery,
  useLazyGetQuotationsQuery,
  useAddQuotationMutation,
  useUpdateQuotationsMutation,
  useDeleteQuotationsMutation,
  useGetQuotationPagesQuery,
  useLazyGetQuotationPagesQuery,
  useSendQuotationMutation,
  useDeleteManyQuotationsMutation,
  useGetQuotationInfoTileDataQuery,
  useGetLatestQuotationQuery,
  useCreateQuotationStatusMutation,
  useGetQuotationStatusesQuery,
  useDeleteQuotationStatusMutation,
  useUpdateStatusOfQuotationMutation,
  useArchiveQuotationMutation,
  useGetQuotationFilterOptionsQuery,
  useGetQuotationsByCustomerQuery,
  useGetQuotationByIdQuery,
} = quotationApiSlice;

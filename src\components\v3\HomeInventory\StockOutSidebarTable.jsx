import { useMemo } from 'react';
import { Table, Tag, Collapse, Card, Badge, Tooltip } from 'antd';
import { User } from 'lucide-react';

const { Panel } = Collapse;

const StockOutSidebarTable = ({ orderData }) => {
  const groupedData = useMemo(() => {
    const items = orderData?.productsInfo || [];
    const groupedItems = {};

    items.forEach((item, index) => {
      if (!groupedItems[item?.name]) {
        groupedItems[item?.name] = {
          key: item?.name,
          itemName: item?.name,
          totalQty: item.qty,
          totalStockOutQty: item.stockOut,
          totalRemainingQty: item.qty - item?.stockOut,
          minRealTimeQty: item?.realtime,
          details: [],
          receivedBy: item?.user || '-',
        };
      }

      const detailRow = {
        key: `${item?.name}-${index}`,
        batch: item?.batch || '-',
        totalQty: item.qty,
        stockOutQty: item.stockOut,
        remainingQty: item.qty - item.stockOut,
        uom: item?.selectedUOM || '-',
        currentRealTimeQuantity: item?.realtime,
        comment: item?.comment || '-',
      };
      groupedItems[item?.name].details.push(detailRow);
    });

    return Object.values(groupedItems);
  }, [orderData]);

  const detailColumns = [
    {
      title: 'Batch',
      dataIndex: 'batch',
      key: 'batch',
      width: 80,
      render: (text) => <span style={{ fontSize: '12px' }}>{text}</span>,
    },
    {
      title: 'Qty',
      dataIndex: 'totalQty',
      key: 'totalQty',
      width: 80,
      render: (text) => (
        <Tag color="green" style={{ fontSize: '12px', fontWeight: 'bold' }}>
          {Number.isInteger(text) ? text : text?.toFixed(2)}
        </Tag>
      ),
    },
    {
      title: 'Stock Out',
      dataIndex: 'stockOutQty',
      key: 'stockOutQty',
      width: 100,
      render: (text) => (
        <Tag color="red" style={{ fontSize: '12px', fontWeight: 'bold' }}>
          {Number.isInteger(text) ? text : text?.toFixed(2)}
        </Tag>
      ),
    },
    {
      title: 'Remaining',
      dataIndex: 'remainingQty',
      key: 'remainingQty',
      width: 100,
      render: (text) => (
        <Tag color="orange" style={{ fontSize: '12px', fontWeight: 'bold' }}>
          {Number.isInteger(text) ? text : text?.toFixed(2)}
        </Tag>
      ),
    },
    {
      title: 'Real Time',
      dataIndex: 'currentRealTimeQuantity',
      key: 'currentRealTimeQuantity',
      width: 100,
      render: (text) => {
        if (text === undefined) return 'N/A';
        return (
          <Tag color="blue" style={{ fontSize: '12px', fontWeight: 'bold' }}>
            {Number.isInteger(text) ? text : text?.toFixed(2)}
          </Tag>
        );
      },
    },
    {
      title: 'UOM',
      dataIndex: 'uom',
      key: 'uom',
      width: 80,
      render: (text) => (
        <span style={{ fontSize: '12px' }}>
          {text?.conversion?.conversionValue ? (
            <>
              <p>{text?.uom}</p>
              <p>
                {text?.conversion?.conversionValue}-(
                {text?.conversion?.conversionUnit})
              </p>
            </>
          ) : (
            text?.uom
          )}
        </span>
      ),
    },
    {
      title: 'Comments',
      dataIndex: 'comment',
      key: 'comment',
      width: 100,
      render: (text) =>
        text?.length > 30 ? (
          <Tooltip title={text} placement="top">
            <p>{text?.slice(0, 30)}</p>
          </Tooltip>
        ) : (
          text
        ),
    },
    // ...(rightBarHeading?.data?.customColumns
    //   ? Object.entries(rightBarHeading.data.customColumns).map(
    //       ([key, _value]) => ({
    //         title: key,
    //         dataIndex: key,
    //         key: key,
    //         width: 100,
    //         render: (text, record) => {
    //           // Get the actual value from the custom column
    //           const customValue =
    //             record[key] || rightBarHeading.data.customColumns[key];
    //
    //           return customValue?.length > 30 ? (
    //             <Tooltip title={customValue} placement="top">
    //               <p>{customValue.slice(0, 30)}</p>
    //             </Tooltip>
    //           ) : (
    //             customValue
    //           );
    //         },
    //       })
    //     )
    //   : []),
  ];

  return (
    <Card
      className="mb-4"
      style={{ backgroundColor: 'white', fontSize: '12px' }}
    >
      <div>
        <div className="flex justify-self-end mt- gap-x-3">
          <Badge text="Real Time qty" color="blue" />
          <Badge text="Total qty" color="green" />
          <Badge text="Stock Out qty" color="red" />
          <Badge text="Remaining" color="orange" />
        </div>
        <Collapse accordion>
          {groupedData.map((item) => (
            <Panel
              key={item.key}
              header={
                <div className="flex justify-between items-center w-full">
                  <Tooltip title={item.itemName} placement="top">
                    <span
                      className="font-medium"
                      style={{
                        fontSize: '14px',
                        whiteSpace: 'nowrap',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                      }}
                    >
                      {item.itemName.length > 25
                        ? `${item.itemName.substring(0, 25)}...`
                        : item.itemName}
                    </span>
                  </Tooltip>
                  <div className="flex space-x-2">
                    <Tag
                      color="blue"
                      style={{
                        fontSize: '12px',
                        width: '80px',
                        textAlign: 'center',
                        fontWeight: 'bold',
                      }}
                    >
                      {item?.minRealTimeQty === undefined
                        ? 'N/A'
                        : Number.isInteger(item?.minRealTimeQty)
                          ? item?.minRealTimeQty
                          : item?.minRealTimeQty?.toFixed(2)}
                    </Tag>

                    <Tag
                      color="green"
                      style={{
                        fontSize: '12px',
                        width: '80px',
                        textAlign: 'center',
                        fontWeight: 'bold',
                      }}
                    >
                      {Number.isInteger(item?.totalQty)
                        ? item?.totalQty
                        : item?.totalQty?.toFixed(2)}
                    </Tag>
                    <Tag
                      color="red"
                      style={{
                        fontSize: '12px',
                        width: '80px',
                        textAlign: 'center',
                        fontWeight: 'bold',
                      }}
                    >
                      {Number.isInteger(item?.totalStockOutQty)
                        ? item?.totalStockOutQty
                        : item?.totalStockOutQty?.toFixed(2)}
                    </Tag>
                    <Tag
                      color="orange"
                      style={{
                        fontSize: '12px',
                        width: '80px',
                        textAlign: 'center',
                        fontWeight: 'bold',
                      }}
                    >
                      {Number.isInteger(item?.totalRemainingQty)
                        ? item?.totalRemainingQty
                        : item?.totalRemainingQty?.toFixed(2)}
                    </Tag>
                    <Tooltip
                      title={
                        item?.receivedBy
                          ? `Received By: ${item?.receivedBy}`
                          : ''
                      }
                    >
                      <User className="w-10 h-[22px] rounded bg-yellow-100 border border-yellow-300" />
                    </Tooltip>
                  </div>
                </div>
              }
            >
              <Table
                columns={detailColumns}
                dataSource={item.details}
                pagination={false}
                scroll={{ x: 'max-content' }}
                size="small"
                bordered
              />
            </Panel>
          ))}
        </Collapse>
      </div>
    </Card>
  );
};

export default StockOutSidebarTable;

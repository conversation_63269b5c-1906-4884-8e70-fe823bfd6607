import { Modal } from 'antd';
import { useContext, useEffect, useState } from 'react';
import { MdDeleteOutline } from 'react-icons/md';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { toast } from 'react-toastify';
import {
  downloadMedia,
  removeSpaces,
  renderFieldsBasedOnType,
} from '../../helperFunction';
import usePrefixIds from '../../hooks/usePrefixIds';
import { useUpdateLeadsDefaultParamMutation } from '../../slices/defaultsApiSlice';
import { useLazyQueryTemplateByIdQuery } from '../../slices/dsahboardTemplateApiSlice';
import {
  useCreateLeadsMutation,
  useEditLeadsMutation,
  useGetLatestLeadQuery,
} from '../../slices/leadsApiSlice';
import { useCreateOrderMutation } from '../../slices/orderApiSlice';
import { useGetAllUsersQuery } from '../../slices/userApiSlice';
import { Store } from '../../store/Store';
import { CITIES, STATES } from '../../utils/citiesStates';
import Input from '../global/components/Input';
import MultiSelect from '../global/components/MultiSelect';
import { default as Select } from '../global/components/Select';
import Textarea from '../global/components/Textarea';
import PreviewImgPdfFullscreen from '../salesOrder/PreviewImgPdfFullscreen';
import LeadAttachements from './LeadAttachements';
import LeadsTable from './LeadsTable';

const states = STATES?.map((el) => ({
  name: el,
  value: el,
}));

const CreateLead = ({
  setIsOpenCreate,
  leadData,
  setLeadData,
  isEdit,
  setIsEdit,
  isCopy,
  setIsCopy,
  isOpenCreate,
}) => {
  const [createLeads, { isLoading: isCreatingLead }] = useCreateLeadsMutation();
  const [editLeads, { isLoading: isEdittingLead }] = useEditLeadsMutation();
  const [additionalFields, setAdditionalFields] = useState(null);
  const [selectedTemplate, setSelectedTemplate] = useState(null);
  const [deleteCategory, setDeleteCategory] = useState(false);
  const [deleteCompany, setDeleteCompany] = useState(false);
  const [categoryModal, setCategoryModal] = useState(false);
  const [companyModal, setCompanyModal] = useState(false);
  const { data: latestLead } = useGetLatestLeadQuery();
  const [templateDropDownModal, setTemplateDropDownModal] = useState(false);
  const [newOptionStatus, setNewOptionStatus] = useState(false);
  const [dropdownIdx, setDropdownIdx] = useState(null);
  const [updateLeadsDefaultParam] = useUpdateLeadsDefaultParamMutation();
  const { data: userData } = useGetAllUsersQuery();
  const [getTemplates, { data: templatesData }] =
    useLazyQueryTemplateByIdQuery();
  const [leadsDefault, setLeadsDefault] = useState({
    category: [],
    company: [],
  });
  const [products, setProducts] = useState([]);
  const [createDepOrder] = useCreateOrderMutation();
  const { defaults, state, dispatch } = useContext(Store);
  const [defaultCategory, setDefaultCategory] = useState([]);
  const [defaultCompany, setDefaultCompany] = useState([]);
  const [attachments, setAttachments] = useState([]);
  const [mediaToPreview, setMediaToPreview] = useState(null);
  const [previewMedia, setPreviewMedia] = useState(false);
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const disableAssignUserLeads =
    defaults?.defaultParam?.projectDefaults?.disableAssignUserLeads;

  const { IdGenComp, idCompData } = usePrefixIds({
    idFor: 'leadId',
    templateIndex:
      additionalFields?.idIndex > -1 ? additionalFields?.idIndex : null,
  });
  useEffect(() => {
    if ((isEdit || isCopy) && leadData?.products?.length > 0) {
      const mappedProducts = leadData.products.map((product) => ({
        productId: product?.productId?._id || product?.productId || null,
        manualEntry: product.manualEntry || null,
        quantity: product.quantity || 0,
        remarks: product.remarks || null,
        uom: product.uom || null,
      }));
      setProducts(mappedProducts);
    }

    if (leadData?.media?.length > 0) {
      setAttachments(leadData.media);
    }
  }, [isCopy, isEdit, leadData]);

  useEffect(() => {
    if (state?.user?.role !== 'admin' && state?.user?.role !== 'superuser') {
      setLeadData((prev) => ({
        ...prev,
        assignedTo: [{ label: state.user.name, value: state.user._id }],
      }));
    }
  }, [state]); //eslint-disable-line

  useEffect(() => {
    if (defaults?.defaultParam) {
      setDefaultCategory(defaults?.defaultParam?.leadsDefault?.category || []);
      setDefaultCompany(defaults?.defaultParam?.leadsDefault?.company || []);
    }
  }, [defaults]);

  useEffect(() => {
    const getCols = async () => {
      const path = '/crm/leads';
      getTemplates({ path });
    };
    getCols();
  }, [getTemplates]);

  useEffect(() => {
    const setIdFormatFunc = () => {
      if (!latestLead) {
        if (templatesData) {
          const defaultTemplate = templatesData?.find((template) =>
            template.name.startsWith('Default')
          );
          setAdditionalFields(defaultTemplate);
          setSelectedTemplate(defaultTemplate);
        }
      } else {
        const templateParamsId =
          searchParams.get('templateId') === 'undefined'
            ? null
            : searchParams.get('templateId');
        if (latestLead) {
          const templateToUse = templatesData?.find((template) => {
            return (
              template?._id ===
              (templateParamsId
                ? templateParamsId
                : latestLead?.additionalFields?._id)
            );
          });
          setSelectedTemplate(templateToUse);
          setAdditionalFields(templateToUse);
        }
      }
    };
    if (isEdit) {
      const templateToUse = templatesData?.find((template) => {
        return template?._id === leadData?.additionalFields?._id;
      });
      setSelectedTemplate(templateToUse);
      setAdditionalFields(leadData?.additionalFields);
    } else {
      setIdFormatFunc();
    }
  }, [
    searchParams,
    latestLead,
    isEdit,
    templatesData,
    leadData?.additionalFields,
  ]);

  const handleChange = (e) => {
    const value =
      typeof e.target.value === 'string'
        ? e.target.value.replace(/\s{2,}/g, ' ')
        : e.target.value;
    setLeadData((prev) => ({
      ...prev,
      [e.target.name]: value,
    }));
  };
  const pdfChangeHandler = (e) => {
    for (let i in e) {
      let fileName = e[i].name;
      let fileType = e[i].type;

      const fr = new FileReader();
      if (i === 'length') return;
      fr.readAsDataURL(e[i]);
      fr.addEventListener('load', () => {
        const url = fr.result;
        let data = {
          name: fileName,
          type: fileType,
          data: url,
        };
        setAttachments((prev) => [...(prev || []), data]);
      });
    }
  };
  const removePdf = (el) => {
    const filtered = attachments?.filter((item) => item?.name !== el?.name);
    setAttachments(filtered);
  };
  const handleInputChange = (
    fieldValue,
    fieldName,
    idx,
    colIndex,
    tableRowIndex
  ) => {
    if (tableRowIndex !== undefined && tableRowIndex !== null) {
      setAdditionalFields((prev) => {
        const updatedTemplateData = [...prev.templateData];
        const fieldWithTableIndex = idx;
        if (fieldWithTableIndex === -1) return prev;

        const updatedTableOptions = {
          ...updatedTemplateData[fieldWithTableIndex]?.tableOptions,
        };

        if (!updatedTableOptions.column) {
          updatedTableOptions.column = [];
        } else {
          updatedTableOptions.column = [...updatedTableOptions.column];
        }

        if (!updatedTableOptions.column[colIndex].selectedOptions) {
          updatedTableOptions.column[colIndex] = {
            columnName: updatedTableOptions.column[colIndex].columnName,
            columnType: updatedTableOptions.column[colIndex].columnType,
            dropdownOptions:
              updatedTableOptions.column[colIndex].dropdownOptions,
            selectedOptions: [],
          };
        }
        const updatedSelectedOptions = [
          ...updatedTableOptions.column[colIndex].selectedOptions,
        ];
        updatedSelectedOptions[tableRowIndex] = fieldValue;

        updatedTableOptions.column[colIndex] = {
          ...updatedTableOptions.column[colIndex],
          selectedOptions: updatedSelectedOptions,
        };

        updatedTemplateData[fieldWithTableIndex] = {
          ...updatedTemplateData[fieldWithTableIndex],
          tableOptions: updatedTableOptions,
        };

        return {
          ...prev,
          templateData: updatedTemplateData,
        };
      });
      return;
    }

    if (fieldValue === '+') {
      setTemplateDropDownModal(true);
      setDropdownIdx(idx);
    } else {
      setAdditionalFields((prev) => ({
        ...prev,
        templateData: prev.templateData?.map((el) => {
          if (el.fieldName === fieldName) {
            return {
              ...el,
              fieldValue,
            };
          } else {
            return el;
          }
        }),
      }));
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    // validate is any empty product details are added or not
    if (
      products.some(
        (product) => product.productId === null && product.manualEntry === null
      )
    ) {
      toast.error('Please fill all product details');
      return;
    }
    if (leadData?.firstName === '') {
      toast.error('First Name is Required');
      return;
    }
    let res = {};
    if (isEdit) {
      try {
        let id = leadData?._id;
        let data = {
          ...leadData,
          email: removeSpaces(leadData?.email),
          phoneNumber: removeSpaces(leadData?.phoneNumber),
          additionalFields,
          assignedTo: leadData?.assignedTo?.map((item) => item?.value) || [],
          isAssigned: leadData?.assignedTo > 0 ? true : false,
          products,
        };
        delete data._id;

        res = await editLeads({ id, data });
        if (res) {
          setIsEdit?.(false);
          setIsOpenCreate(false);
          toast.success('Lead Updated Successfully');
        }
        if (res && isEdit) {
          return;
        }
      } catch (error) {
        console.log(error); //eslint-disable-line
      }
    } else {
      try {
        res = await createLeads({
          ...leadData,
          email: removeSpaces(leadData?.email),
          phoneNumber: removeSpaces(leadData?.phoneNumber),
          additionalFields,
          assignedTo: leadData?.assignedTo?.map((item) => item?.value) || [],
          isAssigned: leadData?.assignedTo > 0 ? true : false,
          products,
          attachments,
          idData: idCompData?.dataToReturn,
        });
        if (res) {
          setLeadData({});
          setIsOpenCreate(false);
          toast.success('Lead Created Successfully');
        }
      } catch (error) {
        console.log(error); //eslint-disable-line
      }
    }
    const kanban = searchParams.get('kanban') === 'true';
    const orderId = searchParams.get('orderId');
    const navigateParams = {
      department: searchParams.get('department'),
      id: res?.data?._id,
      refType: searchParams.get('refType'),
      page: searchParams.get('page'),
      taskId: searchParams.get('taskId'),
      orderId,
      index: searchParams.get('index'),
      idIndex: additionalFields?.idIndex,
    };

    if (!kanban) {
      let obj = {
        objRef: res?.data?._id,
        currentDepartment: 'CRM',
        refKey: 'Leads',
        currentPage: 'Leads',
        userId: state?.user?._id,
        currentStatus: 'Leads',
      };

      await createDepOrder({
        data: obj,
      });
    }

    if (kanban) {
      let time = new Date();
      dispatch({
        type: 'ADD_CARD',
        payload: {
          data: {
            taskId: searchParams.get('taskId'),
            firstStepId: res?.data?.leadId,
            stepPage: 'Leads',
            updatedAt: time?.toDateString(),
          },
          currentColumn: 'Leads',
        },
      });
    }

    const filteredParams = Object.fromEntries(
      Object.entries(navigateParams).filter(([_, value]) => value !== null)
    );

    const navigateStr = `/primary/kanban?${new URLSearchParams(filteredParams).toString()}`;
    if (kanban) {
      navigate(navigateStr);
    }
  };
  const handleDeleteCategory = async (idx) => {
    setDefaultCategory(defaultCategory.filter((_, i) => i !== idx));
    setDeleteCategory(true);
  };

  const handleDeleteCompany = async (idx) => {
    setDefaultCompany(defaultCompany.filter((_, i) => i !== idx));
    setDeleteCompany(true);
  };

  const handleUpdateDefaultLeads = async (e) => {
    e.preventDefault();

    let updatedLeadsDefaults = {
      category: defaultCategory,
      company: defaultCompany,
    };

    if (categoryModal && !deleteCategory) {
      updatedLeadsDefaults.category = [
        ...(defaultCategory || []),
        leadsDefault?.category,
      ];
    }
    if (companyModal && !deleteCompany) {
      updatedLeadsDefaults.company = [
        ...(defaultCompany || []),
        leadsDefault?.company,
      ];
    }

    if (deleteCategory) {
      updatedLeadsDefaults.category = defaultCategory;
    }
    if (deleteCompany) {
      updatedLeadsDefaults.company = defaultCompany;
    }

    await updateLeadsDefaultParam({ leadsDefaults: updatedLeadsDefaults });

    if (categoryModal && !deleteCategory) {
      toast.success('New category is added');
      setCategoryModal(false);
    }
    if (companyModal && !deleteCompany) {
      toast.success('New company is added');
      setCompanyModal(false);
    }
    if (deleteCategory) {
      toast.success('Category is deleted');
      setDeleteCategory(false);
      setCategoryModal(false);
    }
    if (deleteCompany) {
      toast.success('Company is deleted');
      setDeleteCompany(false);
      setCompanyModal(false);
    }
  };

  return (
    <>
      {categoryModal && (
        <Modal
          title="Add New Category"
          onOk={handleUpdateDefaultLeads}
          onCancel={() => setCategoryModal(false)}
          open={categoryModal}
          // className={`z-[99999]`}
        >
          <div className="mb-4">
            <label
              htmlFor="category"
              className="block text-sm font-medium text-gray-700"
            >
              Category Name
            </label>
            <Input
              type="text"
              id="category"
              name="category"
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
              onChange={(e) =>
                setLeadsDefault((prev) => ({
                  ...prev,
                  category: e.target.value,
                }))
              }
            />
          </div>
          <div className="mb-4">
            <span className="block text-sm font-medium text-gray-700">
              Manage Categories
            </span>
            <div className="flex flex-wrap gap-x-2">
              {defaultCategory?.length === 0 && (
                <span className="text-zinc-500">No categories added yet</span>
              )}
              {defaultCategory?.map((category, idx) => (
                <div
                  key={idx}
                  className="bg-zinc-50 border-[1px] text-zinc-700 px-4 py-2 w-fit rounded-xl"
                >
                  <div className="flex gap-x-2 items-center">
                    {category}
                    <MdDeleteOutline
                      size={20}
                      color="red"
                      cursor={'pointer'}
                      onClick={async () => await handleDeleteCategory(idx)}
                    />
                  </div>
                </div>
              ))}
            </div>
          </div>
        </Modal>
      )}
      {companyModal && (
        <Modal
          title="Add New Company"
          onOk={handleUpdateDefaultLeads}
          onCancel={() => setCompanyModal(false)}
          open={companyModal}
          // className={`z-[99999]`}
        >
          <div className="mb-4">
            <label
              htmlFor="company"
              className="block text-sm font-medium text-gray-700"
            >
              Company Name
            </label>
            <Input
              type="text"
              id="company"
              name="company"
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
              onChange={(e) =>
                setLeadsDefault((prev) => ({
                  ...prev,
                  company: e.target.value,
                }))
              }
            />
          </div>
          <div className="mb-4">
            <span className="block text-sm font-medium text-gray-700">
              Manage Companies
            </span>
            <div className="flex flex-wrap gap-x-2">
              {defaultCompany?.length === 0 && (
                <span className="text-zinc-500">No companies added yet</span>
              )}
              {defaultCompany?.map((company, idx) => (
                <div
                  key={idx}
                  className="bg-zinc-50 border-[1px] text-zinc-700 px-4 py-2 w-fit rounded-xl"
                >
                  <div className="flex gap-x-2 items-center">
                    {company}
                    <MdDeleteOutline
                      size={20}
                      color="red"
                      cursor={'pointer'}
                      onClick={async () => await handleDeleteCompany(idx)}
                    />
                  </div>
                </div>
              ))}
            </div>
          </div>
        </Modal>
      )}

      <Modal
        title={isEdit ? 'Edit Lead' : isCopy ? 'Copy Lead' : 'Create Lead'}
        description="Quickly create a new sales lead"
        onCancel={() => {
          setLeadData({});
          setIsOpenCreate(false);
          setIsEdit?.(false);
          setIsCopy?.(false);
        }}
        open={isOpenCreate}
        onOk={handleSubmit}
        confirmLoading={isCreatingLead || isEdittingLead}
        okText={'Submit'}
        width={1200}
        centered
        styles={{
          body: {
            maxHeight: `calc(100vh - 150px)`,
            overflowY: 'auto',
          },
        }}
      >
        <div className=" mt-8 px-2">
          <div className=" mb-4">
            <label className="block text-sm font-medium  text-gray-700">
              Lead Id
            </label>
            <div className="border-2 rounded-md p-2 bg-gray-100 m-0">
              {!isEdit ? (
                <IdGenComp {...idCompData} />
              ) : (
                <span>{leadData?.leadId}</span>
              )}
            </div>
          </div>

          <div className="grid grid-cols-2 gap-x-4">
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700">
                Choose Template:
              </label>
              <Select
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                options={templatesData?.map((template) => ({
                  value: template,
                  name: template.name,
                }))}
                onChange={(e) => {
                  if (selectedTemplate === e.target.value) {
                    return;
                  }
                  setAdditionalFields(e.target.value);
                  setSelectedTemplate(e.target.value);
                  if (selectedTemplate?.idIndex === e.target.value.idIndex) {
                    return;
                  }
                }}
                value={selectedTemplate}
              />
            </div>
            <div className="mb-4">
              <label
                htmlFor="leadName"
                className="block text-sm font-medium text-gray-700"
              >
                Assigned To
              </label>
              <MultiSelect
                disabled={disableAssignUserLeads}
                options={userData?.map((el) => ({
                  label: el?.name,
                  value: el?._id,
                }))}
                value={leadData.assignedTo}
                onChange={(e) => {
                  setLeadData((prev) => ({
                    ...prev,
                    assignedTo: e.target.value,
                  }));
                }}
              />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="mb-4">
              <label
                htmlFor="leadName"
                className="block text-sm font-medium text-gray-700 after:content-['*'] after:ml-0.5 after:text-red-500"
              >
                First Name
              </label>
              <Input
                type="text"
                id="firstName"
                name={'firstName'}
                value={leadData.firstName}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                onChange={(e) => handleChange(e)}
                required={true}
                placeholder="First Name"
              />
            </div>
            <div className="mb-4">
              <label
                htmlFor="leadName"
                className="block text-sm font-medium text-gray-700"
              >
                Last Name
              </label>
              <Input
                type="text"
                id="leadName"
                name={'lastName'}
                value={leadData.lastName}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                onChange={(e) => handleChange(e)}
                placeholder="Last Name"
              />
            </div>
          </div>

          <div className="mb-4">
            <label
              htmlFor="description"
              className="block text-sm font-medium text-gray-700"
            >
              Description
            </label>
            <Textarea
              type="text"
              id="description"
              name={'description'}
              placeholder={'Enter Description'}
              value={leadData.description}
              className="mt-1 block w-full rounded-md border-gray-300"
              onChange={(e) => handleChange(e)}
            />
          </div>

          <div className="grid grid-cols-2 gap-x-4">
            <div className="mb-4">
              <label
                htmlFor="leadName"
                className="block text-sm font-medium text-gray-700"
              >
                Email
              </label>
              <Input
                type="email"
                id="leadName"
                name={'email'}
                value={leadData.email}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                onChange={(e) => handleChange(e)}
                required={true}
              />
            </div>

            <div className="mb-4">
              <label
                htmlFor="leadName"
                className="block text-sm font-medium text-gray-700"
              >
                Phone Number
              </label>
              <Input
                type="text"
                id="leadName"
                name={'phoneNumber'}
                value={leadData.phoneNumber}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                onChange={(e) => handleChange(e)}
              />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-x-4">
            <div className="mb-4">
              <label
                htmlFor="company"
                className="block text-sm font-medium text-gray-700"
              >
                Company
              </label>
              <Select
                options={[
                  { label: '+ Add / Manage Company', value: '+' },
                  ...(defaults?.defaultParam?.leadsDefault?.company?.map(
                    (com) => ({
                      label: com,
                      value: com,
                    })
                  ) || []),
                ]}
                value={leadData.company}
                onChange={(e) => {
                  if (e.target.value === '+') {
                    setCompanyModal(true);
                  } else {
                    setLeadData((prev) => ({
                      ...prev,
                      company: e.target.value,
                    }));
                  }
                }}
              />
            </div>

            <div className="mb-4">
              <label
                htmlFor="industry"
                className="block text-sm font-medium text-gray-700"
              >
                Industry
              </label>
              <Input
                type="text"
                id="industry"
                name={'industry'}
                value={leadData.industry}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                onChange={(e) => handleChange(e)}
              />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-x-4 ">
            <div className="mb-4">
              <label
                htmlFor="category"
                className="block text-sm font-medium text-gray-700"
              >
                Business Category
              </label>
              <Select
                options={[
                  { label: '+ Add / Manage Category', value: '+' },
                  ...(defaults?.defaultParam?.leadsDefault?.category?.map(
                    (com) => ({
                      label: com,
                      value: com,
                    })
                  ) || []),
                ]}
                value={leadData.category}
                onChange={(e) => {
                  if (e.target.value === '+') {
                    setCategoryModal(true);
                  } else {
                    setLeadData((prev) => ({
                      ...prev,
                      category: e.target.value,
                    }));
                  }
                }}
              />
            </div>
            <div className="mb-4">
              <label
                htmlFor="leadValue"
                className="block text-sm font-medium text-gray-700"
              >
                Lead Value
              </label>
              <Input
                type="number"
                id="leadValue"
                name={'leadValue'}
                value={leadData.leadValue}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                onChange={(e) => handleChange(e)}
              />
            </div>
          </div>

          {/* Address */}
          <div className="w-full grid grid-cols-2 gap-4">
            <div className="w-full ">
              <label className="text-gray-500 text-sm">Country</label>
              <Select
                placeholder="Select Your Country"
                value={leadData?.country}
                name="country"
                onChange={(e) => handleChange(e)}
                options={[
                  {
                    label: 'India',
                    value: 'India',
                  },
                ]}
              />
            </div>
            <div className="w-full ">
              <label className="text-gray-500 text-sm ">State</label>
              <Select
                placeholder="Select Your State"
                value={leadData?.state}
                name="state"
                onChange={(e) => handleChange(e)}
                options={states}
              />
            </div>
            <div className="w-full ">
              <label className="text-gray-500 text-sm ">City</label>
              <Select
                placeholder="Select Your City"
                value={leadData?.city}
                name="city"
                onChange={(e) => handleChange(e)}
                options={CITIES?.[leadData?.state]?.map((el) => ({
                  name: el,
                  value: el,
                }))}
              />
            </div>
            <div className="w-full ">
              <label className="text-gray-500 text-sm">Postal Code</label>
              <Input
                placeholder="Enter Your Postal Code"
                value={leadData?.postalCode}
                name="postalCode"
                type="number"
                onChange={(e) => handleChange(e)}
                inputClassname="!py-1.5"
              />
            </div>
          </div>

          {/* Products Table */}

          <LeadsTable products={products} setProducts={setProducts} />

          {additionalFields?.templateData?.length > 0 && (
            <div className="w-full mt-5">
              <div className="w-full">
                {/* ANCHOR */}
                {renderFieldsBasedOnType(
                  additionalFields,
                  handleInputChange,
                  templateDropDownModal,
                  setTemplateDropDownModal,
                  setAdditionalFields,
                  newOptionStatus,
                  setNewOptionStatus,
                  dropdownIdx,
                  setDropdownIdx,
                  searchParams
                )}
              </div>
            </div>
          )}
          <LeadAttachements
            attachments={attachments}
            pdfChangeHandler={pdfChangeHandler}
            removePdf={removePdf}
            setMediaToPreview={setMediaToPreview}
            setPreviewMedia={setPreviewMedia}
            downloadMedia={downloadMedia}
          />
        </div>
      </Modal>
      {previewMedia && (
        <PreviewImgPdfFullscreen
          media={mediaToPreview}
          showPreview={previewMedia}
          setShowPreview={setPreviewMedia}
        />
      )}
    </>
  );
};

export default CreateLead;

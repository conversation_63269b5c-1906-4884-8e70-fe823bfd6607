import { memo, useCallback, useEffect, useMemo, useState } from 'react';
import { FixedSizeList as List } from 'react-window';
import InfiniteLoader from 'react-window-infinite-loader';
import ApprovalCard from './ApprovalCard';

const ITEM_HEIGHT = 280;

const ApprovalList = memo(
  ({
    items,
    hasNextPage,
    loadNextPage,
    selectedCards,
    onCardSelect,
    setShowSidebar,
    setSidebarData,
    setSidebarDataType,
    setShowEmailModal,
    setDataForMail,
    selectedTab,
  }) => {
    const [listHeight, setListHeight] = useState(600);

    useEffect(() => {
      const calculateHeight = () => {
        const viewportHeight = window.innerHeight;
        const headerHeight = 200;
        const footerHeight = 50;
        const availableHeight = viewportHeight - headerHeight - footerHeight;
        setListHeight(Math.max(400, availableHeight));
      };

      calculateHeight();
      window.addEventListener('resize', calculateHeight);
      return () => window.removeEventListener('resize', calculateHeight);
    }, []);

    const itemCount = useMemo(() => {
      return hasNextPage ? items.length + 1 : items.length;
    }, [items.length, hasNextPage]);

    const isItemLoaded = useCallback(
      (index) => {
        return !!items[index];
      },
      [items]
    );

    const Row = memo(({ index, style }) => {
      const item = items[index];
      if (!item) {
        return (
          <div
            style={style}
            className="flex items-center justify-center px-4 py-2"
          >
            <div className="bg-gradient-to-br from-slate-50 to-slate-100 border border-slate-200 rounded-xl w-full h-44 flex items-center justify-center shadow-sm">
              <div className="flex flex-col items-center gap-3">
                <div className="relative">
                  <div className="animate-spin rounded-full h-8 w-8 border-3 border-blue-200"></div>
                  <div className="animate-spin rounded-full h-8 w-8 border-3 border-blue-600 border-t-transparent absolute top-0 left-0"></div>
                </div>
                <div className="text-center">
                  <span className="text-sm font-medium text-slate-700">
                    Loading more approvals...
                  </span>
                  <div className="text-xs text-slate-500 mt-1">Please wait</div>
                </div>
              </div>
            </div>
          </div>
        );
      }

      const cardSelectedTab =
        selectedTab === 'all' ? item._category || 'unknown' : selectedTab;

      return (
        <div style={style} className="px-4 py-2">
          <ApprovalCard
            key={item._id}
            selectedTab={cardSelectedTab}
            data={item}
            setShowSidebar={setShowSidebar}
            setSidebarData={setSidebarData}
            setSidebarDataType={setSidebarDataType}
            setShowEmailModal={setShowEmailModal}
            setDataForMail={setDataForMail}
            isSelected={selectedCards.has(item._id)}
            onSelect={onCardSelect}
          />
        </div>
      );
    });

    Row.displayName = 'ApprovalRow';

    return (
      <InfiniteLoader
        isItemLoaded={isItemLoaded}
        itemCount={itemCount}
        loadMoreItems={loadNextPage}
      >
        {({ onItemsRendered, ref }) => (
          <List
            ref={ref}
            height={listHeight}
            itemCount={itemCount}
            itemSize={ITEM_HEIGHT}
            onItemsRendered={onItemsRendered}
            overscanCount={5}
          >
            {Row}
          </List>
        )}
      </InfiniteLoader>
    );
  }
);

ApprovalList.displayName = 'ApprovalList';

export default ApprovalList;

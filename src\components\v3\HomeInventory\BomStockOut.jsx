import { Input, Button, Modal, Table, Space, Spin, Card } from 'antd';
import { useState } from 'react';
import { toast } from 'react-toastify';
import { customConfirm } from '../../../utils/customConfirm';
import {
  useGetBomsForWoOptionsQuery,
  useLazyGetItemsForPoPlanningQuery,
} from '../../../slices/assemblyBomApiSlice';
import { useGetAllStoresForOptionsQuery } from '../../../slices/storeApiSlice';
import { useBomStockOutMutation } from '../../../slices/outPageApiSlice';
import { SaveOutlined } from '@ant-design/icons';
import SelectV2 from '../../../components/global/components/SelectV2';
import MultiSelect from '../../global/components/MultiSelect';

const BomStockOut = ({ showBomStockOut, setShowBomStockOut }) => {
  const { data: bomOptions } = useGetBomsForWoOptionsQuery();
  const { data: stores } = useGetAllStoresForOptionsQuery({
    userAccess: true,
  });
  const accessibleStores = stores?.stores || [];

  const [bomStockOut, { isLoading: isCreatingCart }] = useBomStockOutMutation();
  const [getItemsForBom, { isLoading: bomfetching }] =
    useLazyGetItemsForPoPlanningQuery();

  const [selectedBomId, setSelectedBomId] = useState(null);
  const [baseQty, setBaseQty] = useState(1);
  const [items, setItems] = useState([]);

  const onCloseModal = () => {
    setSelectedBomId(null);
    setBaseQty(1);
    setItems([]);
    setShowBomStockOut(false);
  };

  const fetchItems = async (val) => {
    if (!val || !baseQty) return;

    try {
      const res = await getItemsForBom({
        id: val,
        baseQuantity: baseQty,
        isForBom: true,
      }).unwrap();

      setSelectedBomId(val);
      setItems(
        res.map((item, idx) => ({
          key: idx,
          ...item,
          allocations: [], // {store, remainingQuantity, stockOutQty}
        }))
      );
    } catch (error) {
      console.error('Error fetching items for bom:', error); //eslint-disable-line
    }
  };

  const handleStoreSelect = (record, storeIds) => {
    const updated = items.map((item) => {
      if (item.key === record.key) {
        return {
          ...item,
          allocations: storeIds.map((id) => {
            const store = item.storeWiseQuantity.find(
              (s) => s.store?._id === id
            );
            return {
              key: `${item.key}-${id}`,
              parentKey: item.key,
              store: id,
              itemId: item.itemId,
              itemName: item.itemName,
              itemType: item.itemType,
              storeName: store?.store?.name,
              remainingQuantity: store?.remainingQuantity || 0,
              stockOutQty: 0,
              isStockoutReady: false,
            };
          }),
        };
      }
      return item;
    });
    setItems(updated);
  };

  const handleAllocationChange = (
    record,
    parentKey,
    storeId,
    value,
    remainingQuantity
  ) => {
    if (Number(value) > remainingQuantity || Number(value) < 0) {
      toast.error(
        `Invalid quantity. Must be between 0 and ${remainingQuantity}`
      );
      return;
    }

    const parentItem = items.find((item) => item.key === parentKey);
    const prevAllocated = parentItem.allocations
      .filter((alloc) => alloc.store !== storeId)
      .reduce((acc, curr) => acc + curr.stockOutQty, 0);
    const totalRequired = parentItem.requiredQuantity;

    if (prevAllocated + Number(value) > totalRequired) {
      const remainingQty = totalRequired - prevAllocated;
      toast.error(
        `Exceeds required quantity. You can allocate up to ${remainingQty} more.`
      );
      return;
    }

    const updated = items.map((item) => {
      if (item.key === parentKey) {
        return {
          ...item,
          allocations: item.allocations.map((alloc) =>
            alloc.store === storeId
              ? {
                  ...alloc,
                  stockOutQty: Number(value),
                  isStockoutReady: value > 0 && value <= remainingQuantity,
                }
              : alloc
          ),
        };
      }
      return item;
    });
    setItems(updated);
  };

  const columns = [
    {
      title: 'Item',
      dataIndex: 'itemName',
      key: 'itemName',
    },
    {
      title: 'Type',
      dataIndex: 'itemType',
      key: 'itemType',
    },
    {
      title: 'Available Qty',
      dataIndex: 'availableQuantity',
      key: 'availableQuantity',
    },
    {
      title: 'Required Qty',
      dataIndex: 'requiredQuantity',
      key: 'requiredQuantity',
    },
    {
      title: 'Select Stores',
      key: 'stores',
      render: (_, record) => {
        let Stores =
          record.storeWiseQuantity ||
          []?.map((s) => s.store?.name && s?.store?._id) ||
          [];
        let filteredStores = [];
        if (accessibleStores) {
          filteredStores = Stores.filter((s) =>
            accessibleStores.some((as) => as._id === s?.store?._id)
          )?.map((s) => ({ value: s?.store?._id, label: s?.store?.name }));
        }

        return (
          <MultiSelect
            options={filteredStores}
            placeholder="Select store(s)"
            value={record.allocations?.map((a) => a.store) || []}
            onChange={(e) =>
              handleStoreSelect(
                record,
                e.target.value?.map((el) => el?.value)
              )
            }
            style={{ width: '100%' }}
          />
          // <Select
          //   mode="multiple"
          //   style={{ width: '100%' }}
          //   placeholder="Select store(s)"
          //   onChange={(val) => handleStoreSelect(record, val)}
          //   options={filteredStores}
          // ></Select>
        );
      },
    },
  ];

  const allocationColumns = [
    {
      title: 'Store',
      dataIndex: 'storeName',
      key: 'storeName',
    },
    {
      title: 'Available Qty',
      dataIndex: 'remainingQuantity',
      key: 'remainingQuantity',
    },
    {
      title: 'Stock Out Qty',
      key: 'stockOutQty',
      render: (_, record) => (
        <Input
          type="number"
          min={0}
          max={record.remainingQuantity}
          style={{ width: 100 }}
          value={record.stockOutQty}
          onChange={(e) =>
            handleAllocationChange(
              record,
              record.parentKey,
              record.store,
              e.target.value,
              record?.remainingQuantity || 0
            )
          }
        />
      ),
    },
  ];

  const handleSave = async () => {
    let finalListForStockOut = [];
    items.forEach((item) => {
      item.allocations.forEach((alloc) => {
        if (alloc.isStockoutReady) {
          finalListForStockOut.push({
            item: alloc.itemId,
            itemType: alloc?.itemType?.toLowerCase(),
            store: alloc.store,
            quantity: alloc.stockOutQty,
          });
        }
      });
    });

    if (finalListForStockOut.length === 0) {
      toast.error('No valid stock out entries to process.');
      return;
    }
    const confirm = await customConfirm(
      'Are you sure you want to proceed with stock out?',
      'success'
    );
    if (!confirm) {
      return;
    }
    try {
      await bomStockOut({
        items: finalListForStockOut,
        bomId: selectedBomId,
      }).unwrap();
      toast.success('BOM Stock Out successful');
      onCloseModal();
    } catch (error) {
      toast.error('Error during BOM Stock Out');
    }
  };

  return (
    <Modal
      title="BOM Stock Out"
      open={showBomStockOut}
      onCancel={() => onCloseModal()}
      width={1150}
      destroyOnClose={true}
      footer={[
        <Button key="cancel" onClick={() => onCloseModal()}>
          Cancel
        </Button>,
        <Button
          key="save"
          type="primary"
          icon={<SaveOutlined />}
          onClick={handleSave}
        >
          Checkout
        </Button>,
      ]}
      confirmLoading={isCreatingCart}
      styles={{
        body: {
          maxHeight: `calc(85vh - 150px)`,
          overflowY: 'auto',
        },
      }}
    >
      <Card className="shadow-md">
        <Space
          style={{ marginBottom: 16, marginTop: 16 }}
          align="center"
          className="w-full items-center justify-center"
        >
          <Input
            type="number"
            min={1}
            style={{ width: 120 }}
            value={baseQty}
            onChange={(e) => setBaseQty(Number(e.target.value))}
            placeholder="Quantity"
          />
          {
            /* BOM Selector */
            baseQty > 0 && (
              <SelectV2
                style={{ width: 300 }}
                placeholder="Select BOM"
                onChange={async (e) => await fetchItems(e?.target?.value)}
                value={selectedBomId}
                options={bomOptions?.map((bom) => ({
                  label: bom.name,
                  value: bom._id,
                }))}
              />
              // <Select
              //   style={{ width: 300 }}
              //   placeholder="Select BOM"
              //   onChange={async (val) => await fetchItems(val)}
              //   value={selectedBomId}
              //   options={bomOptions?.map((bom) => ({
              //     label: bom.name,
              //     value: bom._id,
              //   }))}
              // />
            )
          }
        </Space>

        {bomfetching ? (
          <Spin />
        ) : (
          <Table
            dataSource={items}
            columns={columns}
            pagination={false}
            bordered
            expandable={{
              expandedRowRender: (record) =>
                record.allocations.length > 0 && (
                  <Table
                    dataSource={record.allocations}
                    columns={allocationColumns}
                    pagination={false}
                    rowKey="key"
                  />
                ),
              rowExpandable: (record) => record.allocations.length > 0,
            }}
            rowKey="key"
          />
        )}
      </Card>
    </Modal>
  );
};

export default BomStockOut;

import { useContext, useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import {
  deliveryDateOptions,
  generateDateString,
  isObjectEmpty,
  transformAddressField,
} from '../../helperFunction';
import usePrefixIds from '../../hooks/usePrefixIds';
import { useLazyQueryTemplateByIdQuery } from '../../slices/dsahboardTemplateApiSlice';
import { useGetAllPurchaseOrderQuery } from '../../slices/purchaseOrderApiSlice';
import {
  useGetAllVendorsForOptionsQuery,
  useLazyGetVendorByIdQuery,
} from '../../slices/vendorApiSlice';
import { Store } from '../../store/Store';
import Input from '../global/components/Input';
import SelectV2 from '../global/components/SelectV2';
import MasterDetails from '../MasterDetails';
import { Label } from '../v2';

const GeneralInfo = ({
  paymentTermOptions,
  setShowAddNewModal,
  setAddNewVendor,
  isMobile,
  isTablet,
  changeHandler,
  formData,
  addressSelector,
  contactSelector,
  emailSelector,
  profileSelector,
  vendor,
  setVendor,
  setDefaults,
  companyDetails,
  companySelectedDetails,
  isCopy,
  indent,
  setAdditionalFields,
  additionalFields,
  Searchparams,
  isEdit,
  setItems,
  setFormData,
}) => {
  const { defaults } = useContext(Store);
  const { id } = useParams();
  const [businessDetails, setBusinessDetails] = useState({});
  const [getTemplates, { data: templatesData }] =
    useLazyQueryTemplateByIdQuery();
  const { data: allVendorOptions } = useGetAllVendorsForOptionsQuery();
  const [DateValue, setDateValue] = useState('');
  const [selectedTemplate, setSelectedTemplate] = useState(null);
  const { data: allPOs } = useGetAllPurchaseOrderQuery();
  const [getVendorById] = useLazyGetVendorByIdQuery();
  const [vendorLoading, setVendorLoading] = useState(false);

  const { IdGenComp, idCompData, taskId } = usePrefixIds({
    idFor: 'poId',
    templateIndex:
      additionalFields?.idIndex > -1 ? additionalFields?.idIndex : null,
    setIdData: setFormData,
  });

  useEffect(() => {
    const getCols = async () => {
      const path = '/purchase/po';
      getTemplates({ path });
    };
    getCols();
  }, [getTemplates]);

  useEffect(() => {
    const address = companyDetails?.address?.find(
      (i) => i._id === companySelectedDetails?.selectedDetails?.address
    );
    const contact = companyDetails?.contactNumber?.find(
      (i) => i._id === companySelectedDetails?.selectedDetails?.contact
    );
    const email = companyDetails?.emailAddress?.find(
      (i) => i._id === companySelectedDetails?.selectedDetails?.email
    );
    let businessDetail = {
      companyName: companyDetails?.name,
      address: address,
      contact: contact?.number,
      gstNumber: companyDetails?.gstNumber,
      email: email?.mail,
    };
    setBusinessDetails(businessDetail);
  }, [
    companyDetails,
    companySelectedDetails?.selectedDetails?.address,
    companySelectedDetails?.selectedDetails?.contact,
    companySelectedDetails?.selectedDetails?.email,
  ]);

  useEffect(
    () => {
      const setIdFormatFunc = () => {
        if (allPOs?.length === 0) {
          if (templatesData && selectedTemplate === null) {
            const defaultTemplate = templatesData?.find((template) =>
              template.name.startsWith('Default')
            );
            setAdditionalFields(defaultTemplate);
            setSelectedTemplate(defaultTemplate);
          }
        } else {
          const templateParamsId =
            Searchparams.get('templateId') === 'undefined'
              ? null
              : Searchparams.get('templateId');
          if (allPOs) {
            const lastEntry = allPOs[allPOs?.length - 1];
            const templateToUse = templatesData?.find((template) => {
              return (
                template?._id ===
                (templateParamsId
                  ? templateParamsId
                  : lastEntry?.additionalFields?._id)
              );
            });
            if (!templateToUse) {
              const defaultTemplate = templatesData?.find((template) =>
                template.name.startsWith('Default')
              );
              setAdditionalFields(defaultTemplate);
              setSelectedTemplate(defaultTemplate);

              return;
            }
            setSelectedTemplate(templateToUse);
            setAdditionalFields(templateToUse);
          }
        }
      };
      if (isEdit && !isCopy) {
        setAdditionalFields(additionalFields);
        return;
      } else if (isCopy) {
        setIdFormatFunc();
      } else {
        setIdFormatFunc();
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [Searchparams, allPOs, isEdit, templatesData, isCopy, selectedTemplate]
  );
  const renderFormField = (label, children, htmlFor) => (
    <div className="space-y-2">
      <label
        htmlFor={htmlFor}
        className="block text-sm font-semibold text-gray-700"
      >
        {label}
      </label>
      {children}
    </div>
  );

  return (
    <div className="space-y-4 p-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center bg-white p-2 rounded-lg border border-gray-200">
        <div className="flex items-center gap-3">
          <span className="font-semibold text-gray-800">PO ID :</span>
          <span className="flex flex-wrap items-center mt-2 justify-start h-fit w-full md:w-1/4">
            {id === 'new' || isCopy ? (
              <IdGenComp {...idCompData} />
            ) : (
              <Input disabled value={formData?.poID} className="!w-full" />
            )}
          </span>
        </div>
        <div className="flex flex-col sm:flex-row gap-4 text-sm">
          <div className="flex items-center gap-2">
            <span className="font-semibold text-gray-800">Date:</span>
            <span className="text-gray-600 bg-gray-100 px-2 py-1 rounded">
              {new Date(Date.now()).toLocaleDateString('en-in', {
                month: 'long',
                year: 'numeric',
                day: 'numeric',
              })}
            </span>
          </div>
          <div className="flex items-center gap-2">
            <span className="font-semibold text-gray-800">Task ID:</span>
            <span className="text-gray-600 bg-gray-100 px-2 py-1 rounded">
              {taskId}
            </span>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Template Selection */}
        <div className="bg-white p-4 rounded-lg border border-gray-200">
          {renderFormField(
            'Choose Template',
            <SelectV2
              className="w-full"
              options={templatesData?.map((template) => ({
                value: template,
                name: template.name,
              }))}
              onChange={(e) => {
                const obj = e.target.value;
                setAdditionalFields({ ...obj });
                setSelectedTemplate(e.target.value);
                if (selectedTemplate?.idIndex === e.target.value.idIndex) {
                  return;
                }
              }}
              value={selectedTemplate}
            />
          )}
        </div>
        <div className="bg-white p-4 rounded-lg border border-gray-200">
          {renderFormField(
            'Delivery Date',
            DateValue === '+' ? (
              <Input
                type="date"
                className="w-full"
                id="deliveryDate"
                name="deliveryDate"
                placeholder="Delivery Date"
                value={
                  formData?.deliveryDate
                    ? formData?.deliveryDate?.split('/').reverse().join('-')
                    : ''
                }
                onChange={(e) => changeHandler('deliveryDate', e.target.value)}
                min={isEdit ? '' : new Date().toISOString().split('T')[0]}
              />
            ) : (
              <SelectV2
                options={deliveryDateOptions}
                className="w-full"
                placeholder={
                  formData?.deliveryDate
                    ? generateDateString(new Date(formData?.deliveryDate))
                    : 'Select'
                }
                value={
                  formData?.deliveryDate
                    ? generateDateString(new Date(formData?.deliveryDate))
                    : ''
                }
                onChange={(e) => {
                  if (e.target.value === '+') {
                    changeHandler('deliveryDate', e.target.value);
                    setDateValue(e.target.value);
                  } else {
                    const day = +e.target.value;
                    const deliveryDate = new Date();
                    deliveryDate.setDate(deliveryDate.getDate() + day);
                    setDateValue(deliveryDate);
                    changeHandler('deliveryDate', deliveryDate);
                  }
                }}
              />
            ),
            'deliveryDate'
          )}
        </div>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-white p-4 rounded-lg border border-gray-200">
          <div className="space-y-3">
            <label className="block text-sm font-semibold text-gray-700">
              Select Vendor
            </label>
            {Searchparams?.get('with_indent') === 'true' ? (
              <Input value={vendor?.name || ''} disabled={true} />
            ) : (
              <SelectV2
                className="w-full"
                value={vendor?._id}
                onChange={async (e) => {
                  const selectedValue = e.target.value;
                  if (e.target.value === '+') {
                    setAddNewVendor(true);
                  } else {
                    setVendorLoading(true);
                    const { data: fetchedVendor } =
                      await getVendorById(selectedValue);
                    setVendor(fetchedVendor);
                    setVendorLoading(false);
                    setItems((prev) =>
                      prev.map((el) => {
                        if (el?.type === 'PartVariant' || el?.type === 'Part') {
                          const hasSelectedVendor = el.vendor_details?.find(
                            (currElem) =>
                              currElem?.vendor?._id === fetchedVendor?._id
                          );

                          if (!isObjectEmpty(hasSelectedVendor)) {
                            return {
                              ...el,
                              rate: hasSelectedVendor?.rate,
                              discount: hasSelectedVendor?.discount,
                            };
                          }

                          return {
                            ...el,
                            rate: 0,
                            discount: 0,
                          };
                        } else {
                          return el;
                        }
                      })
                    );
                  }
                }}
                options={[
                  { label: '+ Add New Vendor', value: '+' },
                  ...(allVendorOptions?.map((vendor) => ({
                    label: vendor?.name,
                    value: vendor._id,
                  })) || []),
                ]}
              />
            )}
            <p id="only-on-print">{vendor?.name || 'Name'}</p>
            <MasterDetails
              isLoading={vendorLoading}
              isMobile={isMobile}
              isTablet={isTablet}
              className="mt-3 p-3 bg-gray-50 rounded border"
              details={vendor || {}}
              setDetails={setVendor}
              excludedFields={[
                'id',
                '_id',
                'logo',
                '__v',
                'profileId',
                'createdAt',
                'updatedAt',
                'idFormat',
                'isUsed',
                'isHidden',
                'lastUsed',
                'additionalFields',
                'name',
              ]}
            />
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg border border-gray-200">
          <div className="space-y-3">
            <h3 className="text-sm font-semibold text-gray-700">
              Business Details
            </h3>
            {profileSelector()}
            <MasterDetails
              isMobile={isMobile}
              isTablet={isTablet}
              className="p-3 bg-gray-50 rounded border"
              details={transformAddressField(businessDetails) || {}}
              excludedFields={[
                'id',
                '_id',
                'logo',
                '__v',
                'profileId',
                'additionalFields',
                'createdAt',
                'updatedAt',
                'lastUsed',
                'number',
              ]}
              companySelectors={{
                address: addressSelector,
                contact: contactSelector,
                email: emailSelector,
              }}
            />
          </div>
        </div>
      </div>

      {indent.length > 0 && (
        <div className="bg-white p-4 rounded-lg border border-gray-200">
          <div className="space-y-2">
            <label className="block text-sm font-semibold text-gray-700">
              Indent Link
            </label>
            <Input
              disabled
              className="w-full md:w-2/5"
              placeholder=" "
              value={indent?.map((item) =>
                item ? 'IND-' + item?.indent_no : ''
              )}
            />
          </div>
        </div>
      )}

      <div className="bg-white p-4 rounded-lg border border-gray-200">
        <div className="space-y-2">
          <label className="block text-sm font-semibold text-gray-700">
            Payment Term
          </label>
          <div className="w-full md:w-2/5">
            <SelectV2
              className="w-full"
              value={formData?.paymentTerm}
              options={[
                { label: '+ Add New PaymentTerm', value: '+' },
                ...(paymentTermOptions?.map((option) => {
                  return {
                    label: option,
                    value: option,
                  };
                }) || []),
              ]}
              onChange={(e) => {
                if (e.target.value === '+') {
                  setShowAddNewModal(true);
                } else {
                  changeHandler('paymentTerm', e.target.value);
                  setVendor((prev) => {
                    return { ...prev, paymentTerm: e.target.value };
                  });
                }
              }}
              id="ignore"
            />
            <p id="only-on-print">{formData?.paymentTerm || ''}</p>
            <div className="flex items-center gap-3 mt-2" id="ignore">
              <Input
                type="checkbox"
                id="ignore"
                onChange={() => {
                  if (
                    defaults?.purchaseOrder?.paymentTerm !==
                    formData?.paymentTerm
                  ) {
                    setDefaults((prev) => ({
                      ...prev,
                      purchaseOrder: {
                        ...prev.purchaseOrder,
                        paymentTerm: formData?.paymentTerm,
                      },
                    }));
                  }
                }}
              />
              <Label>Set as default</Label>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default GeneralInfo;

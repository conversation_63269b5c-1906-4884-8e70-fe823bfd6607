import { useState } from 'react';
import { useNavigate } from 'react-router-dom';

import { Button, Table, Tag } from 'antd';
import { IoArrowBackCircleSharp } from 'react-icons/io5';
import ViewSidebar from './ViewSidebar';

import { useHrmsContext } from '../utils/HrmsContext';

import { customConfirm } from '../../../utils/customConfirm';

import { toast } from 'react-toastify';
import { useRunPayrollMutation } from '../../../slices/HRMS/payrollApiSlice';
import { useGetHolidaysQuery } from '../../../slices/holidayManagementApi';

const Review = ({ payroll, setCurrent }) => {
  const navigate = useNavigate();
  const { data: holidays } = useGetHolidaysQuery();
  const [pageSize, setPageSize] = useState(5);

  const [openSidebar, setOpenSidebar] = useState(false);
  const [userId, setUserId] = useState('');

  const [runPayroll] = useRunPayrollMutation();

  const {
    userDataDuringPayrollRun,
    calculatePayrollStepOneSalary,
    calculatePayrollStepTwoSalary,
    calculatePayrollStepThreeSalary,
    calculatePayrollStepFourSalary,
  } = useHrmsContext();

  const handleSubmit = async () => {
    const res = await runPayroll({
      id: payroll?._id,
      data: userDataDuringPayrollRun,
    });

    if (!res?.error) {
      toast.success('Payroll successfully ran.');
      navigate('/hrms/payroll');
    }
  };

  const columns = [
    {
      title: 'Name',
      key: 'name',
      render: (_, record) => (
        <p
          className="text-blue-500 underline cursor-pointer hover:text-blue-400"
          onClick={() => {
            setOpenSidebar(true);
            setUserId(record?._id);
          }}
        >
          {record?.name}
        </p>
      ),
    },
    {
      title: 'Gender',
      key: 'gender',
      render: (_, record) => <p>{record?.gender}</p>,
    },
    {
      title: 'Email',
      key: 'email',
      render: (_, record) => <Tag color="blue">{record?.email}</Tag>,
    },
    {
      title: 'Contact',
      key: 'contactNumber',
      render: (_, record) => <p>{record?.contactNumber}</p>,
    },
    {
      title: 'Fixed Salary',
      key: 'fixedSalary',
      render: (_, record) => <Tag color="green">₹ {record?.fixedSalary}</Tag>,
    },
    {
      title: 'Work Hours (per Day)',
      key: 'workingHours',
      render: (_, record) => <p>{record?.workingHours}</p>,
    },
    {
      title: 'Leave Reviews Pay',
      key: 'stepOnePay',
      render: (_, record) => (
        <Tag color="orange">
          ₹{' '}
          {Math.ceil(
            calculatePayrollStepOneSalary(
              record?._id,
              payroll?.startDate,
              payroll?.endDate,
              holidays
            )
          )}
        </Tag>
      ),
    },
    {
      title: 'Auxillary Calculations Pay',
      key: 'stepTwoPay',
      render: (_, record) => (
        <Tag color="orange">
          ₹{' '}
          {Math.ceil(
            calculatePayrollStepTwoSalary(
              record?._id,
              payroll?.startDate,
              payroll?.endDate,
              holidays
            )
          )}
        </Tag>
      ),
    },
    {
      title: 'Reimbursements Pay',
      key: 'stepThreePay',
      render: (_, record) => (
        <Tag color="orange">
          ₹{' '}
          {Math.ceil(
            calculatePayrollStepThreeSalary(
              record?._id,
              payroll?.startDate,
              payroll?.endDate,
              holidays
            )
          )}
        </Tag>
      ),
    },
    {
      title: 'Salary Components Pay',
      key: 'stepFourPay',
      render: (_, record) => (
        <Tag color="orange">
          ₹{' '}
          {Math.ceil(
            calculatePayrollStepFourSalary(
              record?._id,
              payroll?.startDate,
              payroll?.endDate,
              holidays
            )
          )}
        </Tag>
      ),
    },
  ];

  const handlePaginationSizeChange = (_, pageSize) => {
    setPageSize(pageSize);
  };

  return (
    <>
      <ViewSidebar
        openSideBar={openSidebar}
        setOpenSideBar={setOpenSidebar}
        data={userDataDuringPayrollRun?.[userId]}
        startDate={payroll?.startDate}
        endDate={payroll?.endDate}
      />
      <div className="w-full">
        <div className="bg-white rounded-[10px] pr-2 py-2 mx-2 w-full flex justify-between">
          <div className="flex items-center gap-2">
            <IoArrowBackCircleSharp
              className="text-4xl text-blue-400 cursor-pointer"
              onClick={async () => {
                const confirm = await customConfirm(
                  'All unsaved progress will be lost. Are you sure you want to go back?',
                  'Delete'
                );
                if (!confirm) return;
                navigate('/hrms/payroll');
              }}
            />
            <h4 className="text-slate-500">Review Payroll</h4>
          </div>
          <div className="flex items-center gap-2">
            <Button
              onClick={() => {
                setCurrent((prev) => prev - 1);
              }}
            >
              Previous
            </Button>
            <Button
              variant="solid"
              color="green"
              type="submit"
              onClick={async () => {
                const confirm = await customConfirm(
                  'Complete the payroll?',
                  'success'
                );
                if (!confirm) return;
                handleSubmit();
              }}
            >
              Submit
            </Button>
          </div>
        </div>
        <div className="h-[60vh]">
          <Table
            columns={columns}
            loading={payroll?.users !== undefined ? false : true}
            dataSource={payroll?.users}
            rowKey={(_, index) => index}
            pagination={{
              position: ['bottomRight'],
              pageSize: pageSize,
              showSizeChanger: true,
              onShowSizeChange: handlePaginationSizeChange,
              pageSizeOptions: [5, 10, 20],
            }}
            size="middle"
            scroll={{ x: true }}
            locale={{ emptyText: 'No Employees added yet' }}
          />
        </div>
      </div>
    </>
  );
};

export default Review;

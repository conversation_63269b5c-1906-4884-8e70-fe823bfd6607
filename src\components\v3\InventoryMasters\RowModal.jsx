import { Button } from 'antd';
import { Fragment, useContext, useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { toast } from 'react-toastify';
import { ReactComponent as Briefcase } from '../../../assets/svgs/briefcase.svg';
import AddPaymentTerm from '../../../components/v3/InventoryMasters/AddPaymentTerm.jsx';
import { downloadMedia } from '../../../helperFunction';
import usePrefixIds from '../../../hooks/usePrefixIds';
import { useLazyGetCompanyFromGstQuery } from '../../../slices/companyProfileApiSlice.js';
import { useUpdateDefaultsMutation } from '../../../slices/defaultsApiSlice';
import {
  useAddDropdownMutation,
  useGetDropdownsQuery,
} from '../../../slices/dropdownApiSlice.js';
import { useAddStoresMutation } from '../../../slices/storeApiSlice.js';
import { useEditUserMutation } from '../../../slices/userApiSlice';
import { useAddVendorMutation } from '../../../slices/vendorApiSlice.js';
import { Store } from '../../../store/Store';
import { MAX_CHAR_ALLOWED } from '../../../utils/Constant';
import { getstrLen } from '../../../utils/Getstrlen';
import { customConfirm } from '../../../utils/customConfirm';
import Input from '../../global/components/Input';
import Modal from '../../global/components/Modal';
import Select from '../../global/components/Select';
import LeadAttachements from '../../leads/LeadAttachements';
import PreviewImgPdfFullscreen from '../../salesOrder/PreviewImgPdfFullscreen';
import { constants, tableTypes } from './inventoryConstants';

const RowModal = ({
  type,
  setPopup,
  isEdit,
  formData,
  setFormData,
  addRow,
  idField,
  editRow,
  isCopy,
  isSaveButtonLoading,
  setSelectedTab,
  navigatedFromParts,
  setNavigatedFromParts,
  setNavigatedFromStore,
  setNavigatedFromVendor,
}) => {
  const navigate = useNavigate();
  const [searchParam, setSearchParams] = useSearchParams();
  const { data: dropdowns } = useGetDropdownsQuery();
  const [addStores] = useAddStoresMutation();
  const [addDropDown] = useAddDropdownMutation();
  const [addVendor] = useAddVendorMutation();
  const [PaymentTermOptions, setPaymentTermOptions] = useState([]);
  const [paymentTerm, setPaymentTerm] = useState('');
  const [valueCount, setValueCount] = useState(1);
  const [confirmPassword, setConfirmPassword] = useState('');
  const [editUser] = useEditUserMutation();
  const [accessControl, setAccessControl] = useState([]);
  const [ShowAddNewModal, setShowAddNewModal] = useState(false);
  const [isOn, setIsOn] = useState(false);
  const [getVendorByGST] = useLazyGetCompanyFromGstQuery();
  const [verifiedGSTData, setVerifiedGSTData] = useState({});
  const [isVerified, setIsVerified] = useState(false);
  // const {data: company, isLoading: isFetching} = useGetCompanyFromGstQuery({ gstNumber: formData?.gstin?.[0] }, { skip: formData?.gstin?.[0]?.length !== 15 })
  const [mediaToPreview, setMediaToPreview] = useState(null);
  const [previewMedia, setPreviewMedia] = useState(false);
  const [allowedParentModalClose, setAllowedParentModalClose] =
    useState(!previewMedia);
  useEffect(() => {
    setAllowedParentModalClose(() => !previewMedia);
  }, [previewMedia]);

  const [updateDefaults] = useUpdateDefaultsMutation();
  const { IdGenComp, idCompData } = usePrefixIds({
    idFor: type === 'vendor' ? 'vendorId' : type === 'store' ? 'storeId' : '',
    setIdData: setFormData,
  });
  const {
    defaults: { defaultParam },
    setDefaultsRefresh,
  } = useContext(Store);

  useEffect(() => {
    if (isOn) {
      let ele = document.getElementById('topSection');
      ele.scrollIntoView({ behavior: 'smooth' });
    }
  }, [isOn]);

  const handleToggle = () => {
    setIsOn(!isOn);
  };

  useEffect(() => {
    let temp = [];
    if (isEdit && formData?.accessControl) {
      for (let el of formData?.accessControl) {
        for (let key of Object.keys(defaultParam?.accessControl)) {
          for (let elem of defaultParam?.accessControl?.[key]) {
            if (el === elem.name) {
              temp.push(elem);
            }
          }
        }
      }
      setAccessControl(temp);
    }
  }, [isEdit, defaultParam?.accessControl, formData?.accessControl]);

  const verifyGSTNumber = async (gstNumber) => {
    setVerifiedGSTData({});
    const res = await getVendorByGST({ gstNumber });
    setVerifiedGSTData(res?.data);
    if (res?.data?.data?.tradeNam !== undefined) {
      setIsVerified(true);
    }
    return res?.data?.data?.tradeNam !== undefined ? true : false;
  };

  const fillWithGST = () => {
    if (verifiedGSTData?.data?.pradr?.adr) {
      setFormData((prev) => ({
        ...prev,
        name: verifiedGSTData?.data?.tradeNam,
        address: [
          verifiedGSTData?.data?.pradr?.adr,
          ...prev?.address?.slice(1),
        ],
      }));
    }
  };

  useEffect(() => {
    if (formData?.gstin?.[0] !== verifiedGSTData?.data?.gstin) {
      setIsVerified(false);
    } else {
      setIsVerified(true);
    }
  }, [formData?.gstin?.[0], verifiedGSTData?.data]); //eslint-disable-line

  const handleInput = (e) => {
    setFormData((prev) => ({
      ...prev,
      additionalFields: {
        ...(prev?.additionalFields || {}),
        [e.target.name]: e.target.value,
      },
    }));
  };

  useEffect(() => {
    if (!dropdowns) return;
    const PaymentTerm = dropdowns?.dropdowns?.find((dropdown) => {
      return dropdown?.name === 'Payment Term';
    });
    setPaymentTermOptions(PaymentTerm?.values);
  }, [dropdowns]);

  const handleSubmitForm = async (e) => {
    if (e) {
      e.preventDefault();
    }
    try {
      if (isEdit) {
        const confirmation = await customConfirm(
          'Are you sure you want to save these changes?'
        );
        if (!confirmation) {
          setPopup('none');
          setFormData(null);
          return;
        }

        let temp = formData;

        if (type === tableTypes.VENDOR) {
          if (type === tableTypes.VENDOR) {
            let address1 = [];
            // check temp have an address or not
            if (temp?.address) address1 = [...(temp?.address || [])];
            address1.push(...(temp?.addressValues || []));
            temp.address = address1;

            let billingaddress = [];
            if (temp?.billingAddress)
              billingaddress = [...(temp?.billingAddress || [])];
            billingaddress.push(...(temp?.billingAddressValues || []));
            temp.billingAddress = billingaddress;

            let deliveryaddress = [];
            if (temp?.deliveryAddress)
              deliveryaddress = [...(temp?.deliveryAddress || [])];
            deliveryaddress.push(...(temp?.deliveryAddressValues || []));
            temp.deliveryAddress = deliveryaddress;

            let contact1 = [];
            if (temp?.contact) contact1 = [...(temp?.contact || [])];
            contact1.push(...(temp?.contactValues || []));
            temp.contact = contact1;

            let email1 = [];
            if (temp?.email) email1 = [...(temp?.email || [])];
            email1.push(...(temp?.emailValues || []));
            temp.email = email1;
          }
        }

        if (type === tableTypes.STORE) {
          temp = {
            ...temp,
            accessControl: accessControl,
          };
        }

        if (type === tableTypes.DROPDOWN) {
          temp = {
            ...temp,
            accessControl: accessControl,
          };
        }

        if (type === tableTypes.DEPARTMENT) {
          temp = {
            ...temp,
            accessControl: accessControl,
          };
        }
        if (type === tableTypes.CATEGORY) {
          const additionalFieldsData = formData?.additionalFields;
          editRow({
            body: {
              id: temp?.id,
              name: temp?.name,
              type: temp?.additionalFields?.type,
              ...(additionalFieldsData && {
                additionalFields: { ...additionalFieldsData },
              }),
            },
            id: temp?._id,
          })
            .unwrap()
            .then(() => {
              setPopup('none');
              setFormData({
                contact: [],
                email: [],
                address: [],
                additionalFields: {},
                accessControl: [],
                billingaddress: [],
                deliveryaddress: [],
                addressValues: [],
                billingAddressValues: [],
                deliveryAddressValues: [],
                contactValues: [],
                emailValues: [],
                gstin: [''],
                attachments: [],
              });
              showCreateToastMessage(
                type === 'vendor'
                  ? 'vendor'
                  : type === 'store'
                    ? 'store'
                    : type === 'category'
                      ? 'category'
                      : 'dropdown'
              );
            })
            .catch((err) => {
              // Handle the error for addNewVendor
              toast.error(
                err?.response?.data?.message ||
                  err?.data?.message ||
                  err.message,
                {
                  theme: 'colored',
                  position: 'top-right',
                  toastId: 'error',
                }
              );
            });
          return;
        }

        editRow({ data: temp, id: temp?._id })
          .unwrap()
          .then(async () => {
            let data = {
              name: formData?.name,
              email: formData?.email,
            };
            if (formData?.userId)
              await editUser({ data, id: formData?.userId }).unwrap();
            await updateDefaults(defaultParam).unwrap();

            setPopup('none');
            setFormData({
              contact: [],
              email: [],
              address: '',
              additionalFields: {},
              accessControl: [],
              billingaddress: [],
              deliveryaddress: [],
              addressValues: [],
              billingAddressValues: [],
              deliveryAddressValues: [],
              contactValues: [],
              emailValues: [],
              gstin: [''],
              attachments: [],
            });
            showUpdateToastMessage(`${type}`);
          })
          .catch((err) => {
            // Handle the error for updateVendor
            toast.error(
              err?.response?.data?.message || err?.data?.message || err.message,
              {
                theme: 'colored',
                position: 'top-right',
                toastId: 'error',
              }
            );
          });
      } else if (isCopy) {
        let temp = formData;
        let address1 = [];
        // check temp have an address or not
        if (temp?.address) address1 = [...(temp?.address || [])];
        address1.push(...(temp?.addressValues || []));
        temp.address = address1;

        let billingaddress = [];
        // check temp have an address or not
        if (temp?.billingAddress)
          billingaddress = [...(temp?.billingAddress || [])];
        billingaddress.push(...(temp?.billingAddressValues || []));
        temp.billingAddress = billingaddress;

        let deliveryaddress = [];
        // check temp have an address or not
        if (temp?.deliveryAddress)
          deliveryaddress = [...(temp?.deliveryAddress || [])];
        deliveryaddress.push(...(temp?.deliveryAddressValues || []));
        temp.deliveryAddress = deliveryaddress;

        let contact1 = [];
        if (temp?.contact) contact1 = [...(temp?.contact || [])];
        contact1.push(...(temp?.contactValues || []));
        temp.contact = contact1;

        let email1 = [];
        if (temp?.email) email1 = [...(temp?.email || [])];
        email1.push(...(temp?.emailValues || []));
        temp.email = email1;

        const filteredArr = Object.keys(temp)?.filter((key) => key !== '_id');
        const updatedRowData = filteredArr.reduce((acc, key) => {
          acc[key] = formData[key];
          return acc;
        }, {});
        if (type === 'store') {
          const store = await addStores(updatedRowData);
          if (store?.data) {
            toast.success('Store Copied');
            let obj = {};
            obj.storeColumns = defaultParam.storeColumns.map((item2) => {
              if (updatedRowData?.additionalFields?.[item2?.field]) {
                return { ...item2, isUsed: true };
              }
              return item2;
            });
            await updateDefaults({ ...defaultParam, ...obj });
          }
        } else if (type === 'dropdown') {
          const dropdown = await addDropDown(updatedRowData);
          if (dropdown?.data) {
            toast.success('Dropdown Copied');
          }
        } else if (type === 'vendor') {
          const vendor = await addVendor(updatedRowData);
          if (vendor?.data) {
            toast.success('Vendor Copied');
            setFormData({
              contact: [],
              email: [],
              address: [],
              additionalFields: {},
              accessControl: [],
              billingaddress: [],
              deliveryaddress: [],
              addressValues: [],
              billingAddressValues: [],
              deliveryAddressValues: [],
              contactValues: [],
              emailValues: [],
              gstin: [''],
              attachments: [],
            });
            let obj = {};
            obj.vendorColumns = defaultParam.vendorColumns.map((item) => {
              if (updatedRowData?.additionalFields?.[item?.field]) {
                return { ...item, isUsed: true };
              }
              return item;
            });
            await updateDefaults({ ...defaultParam, ...obj });
          }
        }

        setPopup('none');
      } else {
        if (formData?.gstin?.length > 15) {
          toast.error('Gst should be 15 characters long');
          return;
        }

        if (formData?.name === '') {
          toast.error('Vender name is required');
          return;
        }

        const values = [];

        let temp = formData;

        let address1 = [];
        // check temp have an address or not
        if (temp?.address) address1 = [...(temp?.address || [])];
        address1.push(...(temp?.addressValues || []));
        temp.address = address1;

        let billingaddress = [];
        // check temp have an address or not
        if (temp?.billingAddress)
          billingaddress = [...(temp?.billingAddress || [])];
        billingaddress.push(...(temp?.billingAddressValues || []));
        temp.billingAddress = billingaddress;

        let deliveryaddress = [];
        // check temp have an address or not
        if (temp?.deliveryAddress)
          deliveryaddress = [...(temp?.deliveryAddress || [])];
        deliveryaddress.push(...(temp?.deliveryAddressValues || []));
        temp.deliveryAddress = deliveryaddress;

        let contact1 = [];
        if (temp?.contact) contact1 = [...(temp?.contact || [])];
        contact1.push(...(temp?.contactValues || []));
        temp.contact = contact1;

        let email1 = [];
        if (temp?.email) email1 = [...(temp?.email || [])];
        email1.push(...(temp?.emailValues || []));
        temp.email = email1;

        if (type === tableTypes.DEPARTMENT) {
          temp = {
            ...temp,
            accessControl: accessControl,
          };
        }
        if (type === tableTypes.DROPDOWN) {
          let element;
          for (let i = 0; i < valueCount; i++) {
            element = document.querySelector(`input[name=value${i}]`)?.value;
            if (element !== '' && element !== undefined) values.push(element);
          }
          let value_len_exceeds = false;
          values?.forEach((each) => {
            if (getstrLen(each) > MAX_CHAR_ALLOWED) {
              value_len_exceeds = true;
              return;
            }
          });
          if (value_len_exceeds) {
            toast.error(`Value cannot exceeds ${MAX_CHAR_ALLOWED} characters`, {
              position: 'top-right',
              theme: 'colored',
              toastId: 'value len error',
            });
            return;
          }

          temp = {
            ...temp,
            values: values,
          };
        }
        if (type === tableTypes.CATEGORY) {
          const additionalFieldsData = formData?.additionalFields;
          addRow({
            body: {
              id: formData?.id,
              name: formData?.name,
              type: formData?.additionalFields?.type,
              ...(additionalFieldsData && {
                additionalFields: { ...additionalFieldsData },
              }),
            },
            id: formData?.id,
          })
            .unwrap()
            .then(async () => {
              let obj = {};
              obj.categoryColumns = defaultParam.categoryColumns.map(
                (item2) => {
                  if (temp?.additionalFields?.[item2?.field]) {
                    return { ...item2, isUsed: true };
                  }
                  return item2;
                }
              );
              await updateDefaults({ ...defaultParam, ...obj });
              setPopup('none');
              setFormData({
                contact: [],
                email: [],
                address: [],
                additionalFields: {},
                accessControl: [],
                billingaddress: [],
                deliveryaddress: [],
                addressValues: [],
                billingAddressValues: [],
                deliveryAddressValues: [],
                contactValues: [],
                emailValues: [],
                gstin: [''],
                attachments: [],
              });
              showCreateToastMessage(
                type === 'vendor'
                  ? 'vendor'
                  : type === 'store'
                    ? 'store'
                    : type === 'category'
                      ? 'category'
                      : 'dropdown'
              );
            });
        } else {
          addRow(temp)
            .unwrap()
            .then(async () => {
              let obj = {};
              if (type === 'vendor') {
                obj.vendorColumns = defaultParam.vendorColumns.map((item) => {
                  if (temp?.additionalFields?.[item?.field]) {
                    return { ...item, isUsed: true };
                  }
                  return item;
                });
              }
              if (type === 'store') {
                obj.storeColumns = defaultParam.storeColumns.map((item2) => {
                  if (temp?.additionalFields?.[item2?.field]) {
                    return { ...item2, isUsed: true };
                  }
                  return item2;
                });
              }
              if (type === 'category') {
                obj.categoryColumns = defaultParam.categoryColumns.map(
                  (item2) => {
                    if (temp?.additionalFields?.[item2?.field]) {
                      return { ...item2, isUsed: true };
                    }
                    return item2;
                  }
                );
              }
              await updateDefaults({ ...defaultParam, ...obj });
              if (type === tableTypes.DROPDOWN) {
                if (
                  defaultParam?.dropdownColumns?.length <
                  values?.length + 2
                ) {
                  const data = [{ field: 'name', title: 'Name' }];
                  for (let i = 0; i < values.length; i++) {
                    data.push({
                      field: `value${i + 1}`,
                      title: `Value ${i + 1}`,
                    });
                  }
                  await updateDefaults({
                    ...defaultParam,
                    ...obj,
                    dropdownColumns: data,
                  });
                } else {
                  const data = [{ field: 'name', title: 'Name' }];
                  await updateDefaults({
                    ...defaultParam,
                    ...obj,
                    dropdownColumns: data,
                  });
                }
              }
              setDefaultsRefresh();
              setPopup('none');
              setFormData({
                contact: [],
                email: [],
                address: [],
                additionalFields: {},
                accessControl: [],
                billingaddress: [],
                deliveryaddress: [],
                addressValues: [],
                billingAddressValues: [],
                deliveryAddressValues: [],
                contactValues: [],
                emailValues: [],
                gstin: [''],
                attachments: [],
              });
              showCreateToastMessage(
                type === 'vendor'
                  ? 'vendor'
                  : type === 'store'
                    ? 'store'
                    : 'dropdown'
              );
              if (searchParam.get('redirect')) {
                navigate('/purchase/indent?isVisblesidebar=true');
              }
              if (searchParam.get('navigate')) {
                navigate(searchParam.get('navigate'));
              }

              if (navigatedFromParts) {
                setSelectedTab('parts');
                setSearchParams(
                  (prev) => {
                    prev.set('openModal', true);
                    return prev;
                  },
                  {
                    replace: true,
                  }
                );
                setNavigatedFromStore(true);
                setNavigatedFromVendor(true);
              } else {
                setPopup(false);
              }
            })
            .catch((error) => {
              console.log(error); // eslint-disable-line
            });
        }
      }
    } catch (error) {
      console.log(error); // eslint-disable-line
    }
  };

  const showCreateToastMessage = (e) => {
    toast.success(`${e} added successfully`, {
      position: toast.POSITION.TOP_RIGHT,
      toastId: 'success',
    });
  };

  const showUpdateToastMessage = (e) => {
    toast.success(`${e} updated successfully`, {
      position: toast.POSITION.TOP_RIGHT,
      toastId: 'success',
    });
  };

  const renderDynamicFields = () => {
    if (type === tableTypes.DROPDOWN) {
      return (
        <>
          {Array.isArray(formData?.values) &&
            formData.values.map((value, i) => (
              <div className="flex flex-col my-5" key={i}>
                <label className="mb-1 font-semibold text-[#667085]">
                  Value {i + 1}:
                </label>
                <Input
                  type="text"
                  name={`value${i}`}
                  value={value}
                  onChange={(event) => {
                    const newValues = [...formData.values];
                    newValues[i] = event.target.value;
                    setFormData((prev) => ({
                      ...prev,
                      values: newValues,
                    }));
                  }}
                />
              </div>
            ))}
        </>
      );
    } else if (type === tableTypes.DEPARTMENT) {
      return (
        <>
          <div className="flex flex-col my-5">
            <label className="mb-1 font-semibold text-[#667085]">Email</label>
            <Input
              type="text"
              placeholder="enter email"
              value={formData?.email || ''}
              autoFocus
              required
              // className="border p-2 rounded text-xs w-8/4 pr-14"
              name="email"
              onChange={(e) => {
                setFormData((prev) => ({
                  ...prev,
                  email: e.target.value,
                }));
              }}
            />
          </div>
          {!isEdit && (
            <div className="flex flex-col my-5">
              <label className="mb-1 font-semibold text-[#667085]">
                Password
              </label>
              <Input
                type="password"
                placeholder="enter password"
                value={formData?.password || ''}
                autoFocus
                required
                // className="border p-2 rounded text-xs w-8/4 pr-14"
                name="password"
                onChange={(e) => {
                  setFormData((prev) => ({
                    ...prev,
                    password: e.target.value,
                  }));
                }}
              />
            </div>
          )}
          {!isEdit && (
            <div className="flex flex-col my-5">
              <label className="mb-1 font-semibold text-[#667085]">
                Confirm Password
              </label>
              <Input
                type="password"
                placeholder="enter password"
                value={confirmPassword || ''}
                autoFocus
                required
                // className="border p-2 rounded text-xs w-8/4 pr-14"
                name="password"
                onChange={(e) => {
                  setConfirmPassword(e.target.value);
                }}
              />
            </div>
          )}
        </>
      );
    } else {
      return (
        <>
          {defaultParam?.[constants?.[`${type}`]?.COLUMNS]?.map((e, eIdx) => {
            if (e.field === 'name') return null;
            if (type === 'vendor') {
              if (
                e.field === 'contact' ||
                e.field === 'address' ||
                e.field === 'gstin' ||
                e.field === 'deliveryAddress' ||
                e.field === 'billingAddress' ||
                e.field === 'paymentTerm' ||
                e.field === 'email'
              )
                return null;
            }
            // if (e.field === 'type') {
            //   return (
            //     <div className="flex flex-col my-5" key={eIdx}>
            //       <label className="mb-1 font-semibold text-[#667085]">
            //         {e.title}
            //       </label>
            //       <Select
            //         key={eIdx}
            //         options={[
            //           { value: 'general', label: 'General' },
            //           { value: 'inventory', label: 'Inventory' },
            //         ]}
            //         value={formData?.additionalFields?.type}
            //         onChange={(e) =>
            //           setFormData((prev) => {
            //             return {
            //               ...prev,
            //               additionalFields: {
            //                 ...prev.additionalFields,
            //                 type: e.target.value,
            //               },
            //             };
            //           })
            //         }
            //       />
            //     </div>
            //   );
            // }

            if (type === 'category') {
              if (e.field === 'type') {
                return null;
              }
            }

            return (
              <div className="flex flex-col my-5" key={eIdx}>
                <label className="mb-1 font-semibold text-[#667085]">
                  {e.title}
                </label>
                <Input
                  type="text"
                  placeholder={`enter ${e.field}`}
                  value={formData?.additionalFields?.[e?.field] || ''}
                  name={e.field}
                  onChange={handleInput}
                />
              </div>
            );
          })}
        </>
      );
    }
  };

  const renderIdField = () => {
    if (isEdit) {
      return (
        <Input readOnly value={formData?.id} disabled className="w-full" />
      );
    } else if (type === tableTypes.DROPDOWN) {
      return (
        <>
          <Input
            type="text"
            placeholder="enter id"
            autoFocus
            className="w-full"
            value={formData?.id}
            name="id"
            disabled={isEdit ? true : false}
            onChange={(e) => {
              setFormData((prev) => {
                return {
                  ...prev,
                  id: e.target.value,
                };
              });
            }}
          />
        </>
      );
    } else if (type === tableTypes.CATEGORY) {
      return (
        <>
          <Input
            type="text"
            placeholder="enter id"
            autoFocus
            className="w-full"
            value={formData?.id}
            name="id"
            // disabled={formData?.id ? true : false}
            onChange={(e) => {
              setFormData((prev) => {
                return {
                  ...prev,
                  id: e.target.value,
                };
              });
            }}
            disabled={formData?._id ? true : false}
          />
        </>
      );
    } else {
      return <IdGenComp {...idCompData} />;
    }
  };

  const nextHandler = async (step, setStep) => {
    if (step === 1) {
      if (!isEdit) {
        if (formData?.password !== confirmPassword) {
          toast.error('Passwords do not match', {
            theme: 'colored',
            position: 'top-right',
            toastId: 'Please fill the id',
          });
          return;
        }
      }
      if (!formData?.email) {
        toast.error('Please fill the email field', {
          theme: 'colored',
          position: 'top-right',
          toastId: 'Please fill the id',
        });
        return;
      }
      if (!formData?.password && !isEdit) {
        toast.error('Please fill the password field', {
          theme: 'colored',
          position: 'top-right',
          toastId: 'Please fill the select field',
        });
        return;
      }
      if (!formData?.name) {
        toast.error('Please fill the name field', {
          theme: 'colored',
          position: 'top-right',
          toastId: 'Please fill the select field',
        });
        return;
      }
      setStep((prev) => prev + 1);
    }
    if (step === 2) {
      setStep((prev) => prev + 1);
    }
  };

  const backHandler = async (step, setStep) => {
    if (
      !(await customConfirm(
        'Going back will reset current step data, Are you sure?',
        'delete'
      ))
    ) {
      return;
    }
    setStep((prev) => prev - 1);
  };

  const replaceFilenameWithVendorName = (name) => {
    const extension = name?.split('.')?.pop();
    const vendorName = formData?.name || '';

    return `${vendorName || ''}.${extension}`;
  };

  const pdfChangeHandler = (e) => {
    if (!formData?.name) {
      toast.warning('Please fill vendor name first');
      return;
    }
    for (let i in e) {
      let fileName = replaceFilenameWithVendorName(e[i].name);
      let fileType = e[i].type;

      const fr = new FileReader();
      if (i === 'length') return;
      fr.readAsDataURL(e[i]);
      fr.addEventListener('load', () => {
        const url = fr.result;
        let data = {
          name: fileName,
          type: fileType,
          data: url,
        };
        // // Check for duplicate files
        // const isDuplicate = formData?.attachments?.some(
        //   (existingFile) => existingFile?.name === fileName
        // );
        // if (isDuplicate) {
        //   toast.error('File with the same name already added');
        //   return;
        // }
        if (isEdit) {
          setFormData((prev) => ({
            ...prev,
            newAttachments: [data],
            attachments: [data],
          }));
        } else {
          setFormData((prev) => ({
            ...prev,
            attachments: [data],
          }));
        }
      });
    }
  };

  const removePdf = async (el) => {
    const confirmation = await customConfirm(
      'Are you sure you want to remove this file?',
      'delete'
    );

    if (!confirmation) {
      return;
    }

    const filtered = formData?.attachments?.filter(
      (item) => item?.name !== el?.name
    );

    if (isEdit) {
      let deleted = [];
      if (el?._id) {
        deleted.push(el?._id);
      }
      setFormData((prev) => {
        return {
          ...prev,
          attachments: filtered,
          newAttachments: filtered?.filter((item) => !item?._id),
          deletedAttachments: [...deleted],
        };
      });
    } else {
      setFormData((prev) => {
        return {
          ...prev,
          attachments: filtered,
        };
      });
    }
  };

  return (
    <>
      <Modal
        title={isEdit ? 'Edit Row' : 'Add Row'}
        description={`Add new ${type}`}
        svg={<Briefcase className="h-8 w-8" />}
        // only adding 1 to step because step indexing starts with 1 and not 0
        onNextClick={({ step, setStep }) => nextHandler(step + 1, setStep)}
        onBackClick={({ step, setStep }) => backHandler(step + 1, setStep)}
        onCloseModal={async () => {
          if (!allowedParentModalClose) return;
          if (!isEdit) {
            setPopup();
            setFormData({
              contact: [],
              email: [],
              address: [],
              additionalFields: {},
              accessControl: [],
              billingaddress: [],
              deliveryaddress: [],
              addressValues: [],
              billingAddressValues: [],
              deliveryAddressValues: [],
              contactValues: [],
              emailValues: [],
              gstin: [''],
            });
            setNavigatedFromParts(false);
            setSearchParams(
              (prev) => {
                prev.set('openModal', false);
                return prev;
              },
              { replace: true }
            );
            return;
          }
          const confirmation = await customConfirm(
            'Are you sure you want to go back? No data has been saved',
            'delete'
          );
          if (confirmation) {
            setPopup();
            setFormData({
              contact: [],
              email: [],
              address: [],
              additionalFields: {},
              accessControl: [],
              billingaddress: [],
              deliveryaddress: [],
              addressValues: [],
              billingAddressValues: [],
              deliveryAddressValues: [],
              contactValues: [],
              emailValues: [],
              gstin: [''],
            });
            setNavigatedFromParts(false);
          }

          setPopup();
          setFormData({
            contact: [],
            email: [],
            address: [],
            additionalFields: {},
            accessControl: [],
            billingaddress: [],
            deliveryaddress: [],
            addressValues: [],
            billingAddressValues: [],
            deliveryAddressValues: [],
            contactValues: [],
            emailValues: [],
            gstin: [''],
          });
        }}
        onSubmit={(e) => handleSubmitForm(e, false, false)}
        canAddValue={type === 'dropdown'}
        btnIsLoading={isSaveButtonLoading}
        setValueCount={setValueCount}
        canRemoveValue={type === 'dropdown'}
        setFormData={setFormData}
        formData={formData}
        zIndex={999}
      >
        {({ step: tempStep }) => {
          const step = tempStep + 1;
          return (
            <>
              {step === 1 && (
                // <div className="grid grid-rows-1 px-2 gap-x-7 grid-cols-1 md:grid-cols-2">
                <div className="flex-col">
                  {idField !== 'disabled' && (
                    <div className="flex flex-col my-5">
                      <label className="mb-1 font-semibold text-[#667085]">
                        {`${type} Id`}
                      </label>
                      <div className="flex items-center pr-3 w-full">
                        {renderIdField()}
                      </div>
                    </div>
                  )}
                  {type === 'vendor' && (
                    <div className="mb-4">
                      <label className="mb-1 font-semibold text-[#667085]">
                        GSTIN:
                      </label>
                      <div className="flex items-center gap-x-2">
                        <Input
                          type="text"
                          placeholder="Enter gstin"
                          value={formData?.gstin ? formData?.gstin[0] : ''}
                          className="w-full"
                          name="gstin"
                          onChange={(event) => {
                            const newValues = [...formData?.gstin];
                            newValues[0] = event.target.value;
                            setFormData((prev) => ({
                              ...prev,
                              gstin: newValues,
                            }));
                          }}
                        />
                        {!isVerified ? (
                          <Button
                            onClick={() => verifyGSTNumber(formData?.gstin[0])}
                            disabled={
                              formData?.gstin?.[0]?.length < 15 ? true : false
                            }
                          >
                            Verify
                          </Button>
                        ) : (
                          <Button
                            onClick={fillWithGST}
                            disabled={
                              formData?.gstin?.[0]?.length < 15 ? true : false
                            }
                          >
                            Auto-Fill
                          </Button>
                        )}
                      </div>
                      {isVerified && (
                        <>
                          {verifiedGSTData?.data?.tradeNam !== undefined ? (
                            <p className="text-green-500 text-[12px]">
                              GST Number has been verified.{' '}
                              {verifiedGSTData?.data?.tradeNam} company found.
                            </p>
                          ) : (
                            <p className="text-red-500 text-[12px]">
                              GST Number not found
                            </p>
                          )}
                        </>
                      )}
                    </div>
                  )}
                  {/* <div className="flex flex-col mb-2">
                    {Array.isArray(formData?.gstin) &&
                      formData?.gstin?.map((elem, i) => {
                        return (
                          <>
                            <div className="flex flex-col w-full">
                              <div className="items-center gap-x-2">
                                <div>
                                  <label className="mb-1 font-semibold text-[#667085]">
                                    GST {i + 1}
                                  </label>
                                  <div className="relative">
                                    <Input
                                      type="text"
                                      placeholder="Enter gstin"
                                      value={
                                        formData?.gstin
                                          ? formData?.gstin[i]
                                          : ''
                                      }
                                      className="w-full"
                                      name="gstin"
                                      onChange={(event) => {
                                        const newValues = [...formData?.gstin];
                                        newValues[i] = event.target.value;
                                        setFormData((prev) => ({
                                          ...prev,
                                          gstin: newValues,
                                        }));
                                      }}
                                    />
                                    {i !== 0 && (
                                      <p
                                        className="absolute top-[10px] right-[-0.4rem] mr-[-14px] text-sm px-[4px] py-[4px]   rounded-xl cursor-pointer"
                                        onClick={() => {
                                          setFormData((prev) => ({
                                            ...prev,
                                            gstin: [
                                              ...prev?.gstin?.slice(0, i),
                                              ...prev?.gstin?.slice(i + 1),
                                            ],
                                          }));
                                        }}
                                      >
                                        &nbsp;&nbsp;X
                                      </p>
                                    )}
                                  </div>
                                </div>
                              </div>
                            </div>
                          </>
                        );
                      })}
                    <div
                      onClick={() =>
                        setFormData((prev) => ({
                          ...prev,
                          gstin: [...(prev.gstin || []), ''],
                        }))
                      }
                      className=" text-blue-400 hover:text-blue-600 mt-1 cursor-pointer justify-end"
                    >
                      +Add GST
                    </div>
                  </div> */}
                  <div className="flex flex-col my-5 -mt-2">
                    <label className="mb-1 font-semibold text-[#667085]">
                      Name<span className="text-red-500 text-xl -mt-2">*</span>
                    </label>
                    <Input
                      type="text"
                      placeholder="enter name"
                      value={formData?.name || ''}
                      autoFocus
                      name="name"
                      onChange={(e) => {
                        setFormData((prev) => ({
                          ...prev,
                          name: e.target.value,
                        }));
                      }}
                    />
                  </div>
                  {type === 'vendor' && (
                    <>
                      <div className="flex-col">
                        <div className="flex flex-col my-1">
                          <div>
                            <label className="mb-1 font-semibold text-[#667085]">
                              Contact 1
                            </label>
                            <Input
                              type="text"
                              placeholder="Enter contact 1"
                              value={
                                formData?.contact &&
                                formData?.contact?.length > 0
                                  ? formData?.contact[0]
                                  : ''
                              }
                              onChange={(e) => {
                                const updatedContact =
                                  formData.contact &&
                                  formData.contact.length > 0
                                    ? [...formData.contact]
                                    : [''];
                                updatedContact[0] = e.target.value;
                                setFormData((prev) => ({
                                  ...prev,
                                  contact: updatedContact.filter(
                                    (contact) =>
                                      contact !== '' && contact !== null
                                  ),
                                }));
                              }}
                            />
                          </div>
                        </div>
                      </div>
                      {/* Dynamic fields Contact Add */}
                      {Array.isArray(formData?.contactValues) &&
                        formData?.contactValues?.map((e, i) => {
                          return (
                            <div
                              className="flex items-center gap-1 mt-3"
                              key={i}
                            >
                              <div className="flex flex-col !min-w-[150px] w-[1000px]">
                                <label className="mb-1 font-semibold text-[#667085]">
                                  Contact {i + 1 + 1}
                                </label>
                                <Input
                                  type="text"
                                  // className="border p-2 text-xs rounded pr-14"
                                  name={`value${i}`}
                                  value={
                                    formData?.contactValues
                                      ? formData?.contactValues[i]
                                      : ''
                                  }
                                  onChange={(event) => {
                                    const newValues = [
                                      ...formData.contactValues,
                                    ];
                                    newValues[i] = event.target.value;
                                    setFormData((prev) => ({
                                      ...prev,
                                      contactValues: newValues,
                                    }));
                                  }}
                                />
                              </div>
                              <div
                                onClick={() =>
                                  setFormData((prev) => ({
                                    ...prev,
                                    contactValues: prev.contactValues.filter(
                                      (_, idx) => idx !== i
                                    ),
                                  }))
                                }
                                className="cursor-pointer mt-5 -mr-4 "
                              >
                                {' '}
                                &nbsp;&nbsp;x
                              </div>
                            </div>
                          );
                        })}
                      <div
                        onClick={() =>
                          setFormData((prev) => ({
                            ...prev,
                            contactValues: [...(prev.contactValues || []), ''],
                          }))
                        }
                        className=" text-blue-400 hover:text-blue-600 mt-1 cursor-pointer justify-end"
                      >
                        +Add Contact
                      </div>
                      {/* Email fields Add */}
                      <div>
                        <div className="flex flex-col mt-5">
                          <label className="mb-1 font-semibold text-[#667085]">
                            Email 1
                          </label>
                          <Input
                            type="email"
                            placeholder={`enter email`}
                            value={formData?.email || ''}
                            name={'email'}
                            onChange={(e) => {
                              const updatedEmail = [...(formData.email || [])];
                              updatedEmail[0] = e.target.value;
                              setFormData((prev) => ({
                                ...prev,
                                email: updatedEmail.filter(
                                  (email) => email !== '' && email !== null
                                ),
                              }));
                            }}
                          />
                        </div>
                      </div>
                      {/* Dynamic fields Email Add */}
                      {Array.isArray(formData?.emailValues) &&
                        formData?.emailValues?.map((e, i) => {
                          return (
                            <Fragment key={i}>
                              <div className="flex items-center gap-1 mt-2">
                                <div className="flex flex-col !min-w-[150px] w-[1000px]">
                                  <label className="mb-1 font-semibold text-[#667085]">
                                    Email {i + 1 + 1}
                                  </label>
                                  <Input
                                    type="text"
                                    // className="border p-2 text-xs rounded pr-14"
                                    name={`value${i}`}
                                    value={
                                      formData?.emailValues
                                        ? formData?.emailValues[i]
                                        : ''
                                    }
                                    onChange={(event) => {
                                      const newValues = [
                                        ...formData.emailValues,
                                      ];
                                      newValues[i] = event.target.value;
                                      setFormData((prev) => ({
                                        ...prev,
                                        emailValues: newValues,
                                      }));
                                    }}
                                  />
                                </div>
                                <div
                                  onClick={() =>
                                    setFormData((prev) => ({
                                      ...prev,
                                      emailValues: prev.emailValues.filter(
                                        (_, idx) => idx !== i
                                      ),
                                    }))
                                  }
                                  className="cursor-pointer mt-5 -mr-4 "
                                >
                                  {' '}
                                  &nbsp;&nbsp;x
                                </div>
                              </div>
                            </Fragment>
                          );
                        })}
                      <div
                        onClick={() =>
                          setFormData((prev) => ({
                            ...prev,
                            emailValues: [...(prev.emailValues || []), ''],
                          }))
                        }
                        className=" text-blue-400 hover:text-blue-600 mt-1 cursor-pointer justify-end"
                      >
                        +Add Email
                      </div>
                      <div className="flex flex-col">
                        <label className="mb-1 font-semibold text-[#667085]">
                          Address 1
                        </label>
                        <Input
                          type="text"
                          placeholder="enter billing address"
                          value={
                            formData?.address && formData?.address?.length > 0
                              ? formData?.address[0]
                              : ''
                          }
                          name="address"
                          onChange={(e) => {
                            const updatedAddress =
                              formData.address && formData.address.length > 0
                                ? [...formData.address]
                                : [''];
                            updatedAddress[0] = e.target.value;
                            setFormData((prev) => ({
                              ...prev,
                              address: updatedAddress?.filter(
                                (address) => address !== '' && address !== null
                              ),
                            }));
                          }}
                        />
                      </div>{' '}
                      {/* billing Address dynamic fields Add Do not remove this commented Code */}
                      {Array.isArray(formData?.addressValues) &&
                        formData?.addressValues?.map((e, i) => {
                          return (
                            <div
                              className="flex items-center gap-1 mt-3"
                              key={i}
                            >
                              <div className="flex flex-col !min-w-[150px] w-[1000px]">
                                <label className="mb-1 font-semibold text-[#667085]">
                                  Address {i + 1 + 1}
                                </label>
                                <Input
                                  type="text"
                                  // className="border p-2 text-xs rounded pr-14"
                                  name={`addressValues${i}`}
                                  value={
                                    formData?.addressValues
                                      ? formData?.addressValues[i]
                                      : ''
                                  }
                                  onChange={(event) => {
                                    const newValues = [
                                      ...formData?.addressValues,
                                    ];
                                    newValues[i] = event.target.value;
                                    setFormData((prev) => ({
                                      ...prev,
                                      addressValues: newValues,
                                    }));
                                  }}
                                />
                              </div>
                              <div
                                onClick={() =>
                                  setFormData((prev) => ({
                                    ...prev,
                                    addressValues: prev.addressValues.filter(
                                      (_, idx) => idx !== i
                                    ),
                                  }))
                                }
                                className="cursor-pointer mt-5 -mr-4 "
                              >
                                {' '}
                                &nbsp;&nbsp;x
                              </div>
                            </div>
                          );
                        })}
                      <div
                        onClick={() =>
                          setFormData((prev) => ({
                            ...prev,
                            addressValues: [...(prev.addressValues || []), ''],
                          }))
                        }
                        className=" text-blue-400 hover:text-blue-600 mt-1 cursor-pointer justify-end"
                      >
                        +Add Address
                      </div>
                      {/* add more fields */}
                      <div className="flex gap-2 justify-end items-center mt-4">
                        <span className="text-lg font-medium text-blue-500 text-center mt-[-7px]">
                          {isOn ? 'Show Less' : 'Add More'}
                        </span>

                        <div
                          className="flex items-center cursor-pointer justify-center"
                          onClick={handleToggle}
                        >
                          <div
                            className={`w-12 h-5 flex items-center rounded-full p-1 duration-300 ease-in-out mt-[-4px] ${
                              isOn ? 'bg-blue-500' : 'bg-gray-300'
                            }`}
                          >
                            <div
                              className={`bg-white w-4 h-4 rounded-full shadow-md transform duration-300 ease-in-out ${
                                isOn ? 'translate-x-7' : ''
                              }`}
                            ></div>
                          </div>
                        </div>
                      </div>
                      {isOn && (
                        <div id="topSection">
                          {defaultParam?.[constants?.[`${type}`]?.COLUMNS]?.map(
                            (e, eIdx) => {
                              if (e.field === 'name') return null;
                              if (type === 'vendor') {
                                if (
                                  e.field === 'contact' ||
                                  e.field === 'address' ||
                                  e.field === 'gstin' ||
                                  e.field === 'deliveryAddress' ||
                                  e.field === 'billingAddress' ||
                                  e.field === 'paymentTerm'
                                )
                                  return null;
                              }

                              if (e.field === 'type') {
                                return (
                                  <div
                                    className="flex flex-col my-5"
                                    key={eIdx}
                                  >
                                    <label className="mb-1 font-semibold text-[#667085]">
                                      {e.title}
                                    </label>
                                    <Select
                                      key={eIdx}
                                      options={[
                                        { value: 'general', label: 'General' },
                                        {
                                          value: 'inventory',
                                          label: 'Inventory',
                                        },
                                      ]}
                                      value={formData?.additionalFields?.type}
                                      onChange={(e) =>
                                        setFormData((prev) => {
                                          return {
                                            ...prev,
                                            additionalFields: {
                                              ...prev.additionalFields,
                                              type: e.target.value,
                                            },
                                          };
                                        })
                                      }
                                    />
                                  </div>
                                );
                              }
                            }
                          )}
                          <div className="flex flex-col my-5">
                            <label className="mb-1 font-semibold text-[#667085]">
                              Payment Term
                            </label>

                            <Select
                              value={
                                isEdit
                                  ? formData.paymentTerm
                                  : formData?.paymentTerm || paymentTerm
                              }
                              options={[
                                { label: '+ Add New PaymentTerm', value: '+' },
                                ...(PaymentTermOptions?.map((option) => {
                                  return {
                                    label: option,
                                    value: option,
                                  };
                                }) || []),
                              ]}
                              onChange={(e) => {
                                if (e.target.value === '+') {
                                  setShowAddNewModal(true);
                                } else {
                                  setPaymentTerm(e.target.value);
                                  setFormData((prev) => ({
                                    ...prev,
                                    paymentTerm: e.target.value,
                                  }));
                                }
                              }}
                              id="ignore"
                            />
                          </div>
                          <div>
                            <LeadAttachements
                              attachments={formData?.attachments}
                              pdfChangeHandler={pdfChangeHandler}
                              removePdf={removePdf}
                              setMediaToPreview={setMediaToPreview}
                              setPreviewMedia={setPreviewMedia}
                              downloadMedia={downloadMedia}
                              // isSelfContainedPreview={true}
                            />
                          </div>
                          {renderDynamicFields()}
                        </div>
                      )}
                    </>
                  )}
                  {type === 'dropdown' && (
                    <>
                      {Array.isArray(formData?.values) &&
                        formData.values.map((value, i) => (
                          <div className="flex flex-col my-5" key={i}>
                            <label className="mb-1 font-semibold text-[#667085]">
                              Value {i + 1}
                            </label>
                            <Input
                              type="text"
                              name={`value${i}`}
                              value={value}
                              onChange={(event) => {
                                const newValues = [...formData.values];
                                newValues[i] = event.target.value;
                                setFormData((prev) => ({
                                  ...prev,
                                  values: newValues,
                                }));
                              }}
                            />
                          </div>
                        ))}
                    </>
                  )}
                </div>
              )}
            </>
          );
        }}
      </Modal>

      {previewMedia && (
        <PreviewImgPdfFullscreen
          media={mediaToPreview}
          showPreview={previewMedia}
          setShowPreview={setPreviewMedia}
        />
      )}
      {ShowAddNewModal && (
        <AddPaymentTerm
          setShowModal={setShowAddNewModal}
          dropdowns={dropdowns}
          PaymentTermOptions={PaymentTermOptions}
        />
      )}
    </>
  );
};

export default RowModal;

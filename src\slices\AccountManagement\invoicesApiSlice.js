import { apiSlice } from '../apiSlice';
const baseRoute = '/v1/invoices';
export const invoicesApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    queryInvoices: builder.query({
      query: ({ page = 1, limit = 10, type = '', searchTerm = '' }) => ({
        url: baseRoute,
        params: {
          page,
          limit,
          ...(type && { type }),
          ...(searchTerm && { searchTerm }),
        },
      }),
      providesTags: ['invoice'],
    }),
    getInvoicesMetrics: builder.query({
      query: () => `${baseRoute}/metrics`,
      providesTags: ['invoiceMetrics'],
    }),
    getInvoiceById: builder.query({
      query: ({ id }) => ({
        url: `${baseRoute}/${id}`,
      }),
      providesTags: ['invoice'],
    }),
    createInvoice: builder.mutation({
      query: ({ data }) => ({
        url: baseRoute,
        method: 'POST',
        body: data,
      }),
      invalidatesTags: ['invoice PrefixIds'],
    }),
    editInvoice: builder.mutation({
      query: ({ id, data }) => ({
        url: `${baseRoute}/${id}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: ['invoice PrefixIds'],
    }),

    updateInvoiceStatus: builder.mutation({
      query: ({ id, status }) => ({
        url: `${baseRoute}/${id}/status`,
        method: 'PATCH',
        body: { status },
      }),
      invalidatesTags: ['invoice'],
    }),

    deleteInvoice: builder.mutation({
      query: ({ id }) => ({
        url: `${baseRoute}/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['invoice', 'invoiceMetrics'],
    }),

    bulkUpdateInvoices: builder.mutation({
      query: ({ ids, updateData }) => ({
        url: `${baseRoute}/bulk-update`,
        method: 'PATCH',
        body: { ids, updateData },
      }),
      invalidatesTags: ['invoice', 'invoiceMetrics'],
    }),

    bulkDeleteInvoices: builder.mutation({
      query: ({ ids }) => ({
        url: `${baseRoute}/bulk-delete`,
        method: 'DELETE',
        body: { ids },
      }),
      invalidatesTags: ['invoice', 'invoiceMetrics'],
    }),
  }),
});

export const {
  useQueryInvoicesQuery,
  useGetInvoicesMetricsQuery,
  useGetInvoiceByIdQuery,
  useCreateInvoiceMutation,
  useEditInvoiceMutation,
  useUpdateInvoiceStatusMutation,
  useDeleteInvoiceMutation,
  useBulkUpdateInvoicesMutation,
  useBulkDeleteInvoicesMutation,
} = invoicesApiSlice;

import dayjs from 'dayjs';
import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { PAGINATION_LIMIT } from '../../utils/Constant';

import { Button, DatePicker, Table, Tag } from 'antd';
import Header from '../../components/global/components/Header';
import Pagination from '../../components/global/components/Pagination';
import { useGetHolidaysQuery } from '../../slices/holidayManagementApi';
import { useGetEmployeesForHRMSQuery } from '../../slices/userApiSlice';
import LeavesModal from './AttendanceComponents/LeavesModal';
import LeavesSidebar from './AttendanceComponents/LeavesSidebar';

const AttendanceManagement = () => {
  const [startDate, setStartDate] = useState(null);
  const [endDate, setEndDate] = useState(null);

  const { data: holidays } = useGetHolidaysQuery();

  const navigate = useNavigate();

  const [limit, setLimit] = useState(PAGINATION_LIMIT);
  const [page, setPage] = useState(1);
  const [type, setType] = useState('desc'); //eslint-disable-line
  const [field, setField] = useState('createdAt'); //eslint-disable-line
  const [SelectedHeading, setSelectedHeading] = useState(''); //eslint-disable-line
  const [SelectedHeadingValue, setSelectedHeadingValue] = useState(''); //eslint-disable-line

  const [editData, setEditData] = useState(null);

  const [openModal, setOpenModal] = useState(false);
  const [user, setUser] = useState('');
  const [leavesData, setLeavesData] = useState({});
  const [leaveSidebar, setLeaveSidebar] = useState(false);

  const {
    data: employees = {},
    isLoading: isLoadingQuery,
    // isFetching: isFetchingQuery,
    // refetch: refetchEmployees,
  } = useGetEmployeesForHRMSQuery(
    {
      page,
      limit,
      type,
      field,
      field_name: SelectedHeading,
      field_value: SelectedHeadingValue,
    },
    { skip: !page || !limit }
  );

  useEffect(() => {
    const today = new Date();
    const firstOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);

    setStartDate(firstOfMonth);
    setEndDate(today);
  }, []);

  function getHoursWorkedYesterday(attendance) {
    if (attendance !== undefined) {
      const yesterday = new Date();
      yesterday.setDate(yesterday.getDate() - 1);
      const yyyy = yesterday.getFullYear();
      const mm = String(yesterday.getMonth() + 1).padStart(2, '0');
      const dd = String(yesterday.getDate()).padStart(2, '0');
      const key = `${yyyy}-${mm}-${dd}`;

      const entry = attendance[key];

      // Ignore if entry doesn't exist, is empty, is marked as leave, or lacks login/logout
      if (
        !entry ||
        Object.keys(entry).length === 0 ||
        entry.isLeave === true ||
        !entry.login ||
        !entry.logout
      ) {
        return 0;
      }

      const login = new Date(entry.login);
      const logout = new Date(entry.logout);

      const diffMs = logout - login;
      const diffHours = diffMs / (1000 * 60 * 60);

      return diffHours;
    } else {
      return 0;
    }
  }

  // function getDaysWorkedThisMonth(attendance, startDate, endDate) {
  //   if (!attendance) return 0;

  //   const start = new Date(startDate);
  //   const end = new Date(endDate);
  //   let daysWorked = 0;
  //   let holidaysCount = 0;

  //   // Helper: check if a date is in a holiday range
  //   const isHoliday = (date) => {
  //     return holidays?.some((holiday) => {
  //       const hStart = new Date(holiday.startDate.$date || holiday.startDate);
  //       const hEnd = new Date(holiday.endDate.$date || holiday.endDate);
  //       return date >= hStart && date <= hEnd;
  //     });
  //   };

  //   // Iterate through each day in the range
  // for (
  //   let day = new Date(start);
  //   day <= end;
  //   day.setDate(day.getDate() + 1)
  // ) {
  //   const dateStr = day.toISOString().split('T')[0]; // YYYY-MM-DD
  //   const entry = attendance[dateStr];
  //   const hasWorked =
  //     entry &&
  //     Object.keys(entry).length > 0 &&
  //     entry.isLeave !== true &&
  //     entry.login &&
  //     entry.logout;

  //   if (hasWorked || isHoliday(day)) {
  //     daysWorked++;
  //   }
  // }

  //   return { daysWorked, holidaysCount };
  // }

  function getDaysWorkedThisMonth(attendance, startDate, endDate) {
    if (!attendance) return 0;

    const start = new Date(startDate);
    const end = new Date(endDate);
    let daysWorked = 0;
    let holidaysCount = 0;

    const isHoliday = (date) => {
      return holidays?.some((holiday) => {
        const hStart = new Date(holiday.startDate.$date || holiday.startDate);
        const hEnd = new Date(holiday.endDate.$date || holiday.endDate);
        return date >= hStart && date <= hEnd;
      });
    };

    for (
      let day = new Date(start);
      day <= end;
      day.setDate(day.getDate() + 1)
    ) {
      const dateStr = day.toISOString().split('T')[0]; // YYYY-MM-DD
      const entryDate = new Date(dateStr);
      const inRange = entryDate >= start && entryDate <= end;
      const entry = attendance[dateStr];
      if (
        inRange &&
        entry &&
        Object.keys(entry).length > 0 &&
        entry.isLeave !== true &&
        entry.login &&
        entry.logout
      ) {
        daysWorked++;
      } else if (isHoliday(day)) {
        // If it's a holiday and no worked hours recorded, count full day hours
        holidaysCount++;
      }
    }

    return { daysWorked, holidaysCount };
  }

  function getHoursWorkedThisMonth(
    attendance,
    startDate,
    endDate,
    workingHoursPerDay = 8
  ) {
    if (!attendance) return 0;

    const start = new Date(startDate);
    const end = new Date(endDate);
    let totalHours = 0;

    // Helper: check if date is a holiday
    const isHoliday = (date) => {
      return holidays?.some((holiday) => {
        const hStart = new Date(holiday.startDate.$date || holiday.startDate);
        const hEnd = new Date(holiday.endDate.$date || holiday.endDate);
        return date >= hStart && date <= hEnd;
      });
    };

    // Iterate through each day in the range
    for (
      let day = new Date(start);
      day <= end;
      day.setDate(day.getDate() + 1)
    ) {
      const dateStr = day.toISOString().split('T')[0]; // YYYY-MM-DD
      const entry = attendance[dateStr];

      if (
        entry &&
        Object.keys(entry).length > 0 &&
        entry.isLeave !== true &&
        entry.login &&
        entry.logout
      ) {
        const login = new Date(entry.login);
        const logout = new Date(entry.logout);
        const diffMs = logout - login;
        if (diffMs > 0) {
          totalHours += diffMs / (1000 * 60 * 60); // ms → hours
        }
      } else if (isHoliday(day)) {
        // If it's a holiday and no worked hours recorded, count full day hours
        totalHours += workingHoursPerDay;
      }
    }

    return totalHours;
  }

  // function getHoursWorkedThisMonth(attendance) {
  //   if (attendance === undefined) {
  //     const today = new Date();
  //     const currentMonth = today.getMonth(); // 0-indexed
  //     const currentYear = today.getFullYear();

  //     let totalHours = 0;

  //     for (const date in attendance) {
  //       const entryDate = new Date(date);
  //       const isSameMonth =
  //         entryDate.getMonth() === currentMonth &&
  //         entryDate.getFullYear() === currentYear;

  //       const entry = attendance[date];

  //       if (
  //         isSameMonth &&
  //         entry &&
  //         Object.keys(entry).length > 0 &&
  //         entry.isLeave !== true &&
  //         entry.login &&
  //         entry.logout
  //       ) {
  //         const login = new Date(entry.login);
  //         const logout = new Date(entry.logout);
  //         const diffMs = logout - login;

  //         if (diffMs > 0) {
  //           totalHours += diffMs / (1000 * 60 * 60); // ms to hours
  //         }
  //       }
  //     }

  //     return totalHours;
  //   }
  //   return 0;
  // }

  function getLeavesTaken(record) {
    let attendance = record?.attendance || {};
    const start = new Date(startDate);
    const end = new Date(endDate);

    let leavesTaken = [];

    Object.entries(attendance).forEach(([dateStr, record]) => {
      const date = new Date(dateStr);

      if (date >= start && date <= end && record.isLeave) {
        leavesTaken.push({
          date: dateStr,
          reason: record.leaveReason || 'N/A',
          approvedBy: record.leaveApprovedBy?.$oid || null,
        });
      }
    });

    return leavesTaken?.length;
  }

  const columns = [
    {
      title: 'Name',
      key: 'name',
      render: (_, record) => (
        <p
          className="text-blue-500 underline cursor-pointer hover:text-blue-400"
          onClick={() => {
            setLeaveSidebar(true);
            setEditData(record);
          }}
        >
          {record?.name}
        </p>
      ),
    },
    {
      title: 'Email',
      key: 'email',
      render: (_, record) => <Tag color="blue">{record?.email}</Tag>,
    },
    {
      title: 'Hours Worked Yesterday',
      key: 'hoursWorkedYesterday',
      render: (_, record) => (
        <p>{getHoursWorkedYesterday(record?.attendance)}</p>
      ),
    },
    {
      title: 'Days Worked this Month',
      key: 'daysWorkedThisMonth',
      render: (_, record) => (
        <p>
          {
            getDaysWorkedThisMonth(record?.attendance, startDate, endDate)
              ?.daysWorked
          }
          {`(+${getDaysWorkedThisMonth(record?.attendance, startDate, endDate)?.holidaysCount})`}
        </p>
      ),
    },
    {
      title: 'Hours Worked this Month',
      key: 'hoursWorkedThisMonth',
      render: (_, record) => (
        <p>
          {getHoursWorkedThisMonth(
            record?.attendance,
            startDate,
            endDate,
            record?.workingHours
          )}
        </p>
      ),
    },
    {
      title: 'Leaves Assigned',
      key: 'leavesAssigned',
      render: (_, record) => (
        <Tag color="green">{record?.leavesAllowed || 0}</Tag>
      ),
    },
    {
      title: 'Leaves Taken',
      key: 'leavesTaken',
      render: (_, record) => <Tag color="red">{getLeavesTaken(record)}</Tag>,
    },
    {
      title: 'Work Hours (per Day)',
      key: 'workingHours',
      render: (_, record) => <p>{record?.workingHours}</p>,
    },
    {
      title: '',
      key: 'actions',
      render: (_, record) => (
        <div className="flex items-center gap-2">
          <Button
            className="bg-purple-500 hover:bg-purple-600 text-white"
            onClick={() => {
              setLeavesData({
                leavesAllowed: record?.leavesAllowed,
                leavesTaken: record?.leavesTaken,
                leaves: record?.attendance,
              });
              setOpenModal(true);
              setUser(record?._id);
            }}
          >
            Leaves
          </Button>
        </div>
      ),
    },
  ];

  return (
    <>
      <LeavesModal
        openModal={openModal}
        setOpenModal={setOpenModal}
        leavesData={leavesData}
        userId={user}
      />
      <LeavesSidebar
        openSideBar={leaveSidebar}
        setOpenSideBar={setLeaveSidebar}
        record={editData}
        getDaysWorkedThisMonth={getDaysWorkedThisMonth}
        getHoursWorkedThisMonth={getHoursWorkedThisMonth}
        getHoursWorkedYesterday={getHoursWorkedYesterday}
        startDate={startDate}
        endDate={endDate}
      />
      <div className="flex gap-[5px] w-full items-center justify-between">
        <Header
          title="Attendance Management"
          description="Manage User Attendance and Leaves for HR Purposes"
          infoTitle="Welcome to the Downtime Page"
          infoDesc="Get a comprehensive overview of machine events and performance on our Analytics Downtime Page. The Analytics Downtime Page provides a comprehensive overview of
                            machine events, offering insights into start and stop times,
                            pauses, errors, downtime, uptime, idle periods, and cycle times.  It empowers users with valuable data for improved
                            decision-making and optimizing overall operational efficiency."
          paras={['']}
        />
        <Button
          onClick={() => {
            navigate('/hrms/attendance/holidays');
          }}
        >
          Holidays
        </Button>
      </div>
      <div className="w-full">
        <div className="flex items-center justify-end gap-2 mb-2 w-full">
          <div className="min-w-[18rem]">
            <p className="font-semibold text-slate-500">Start Date</p>
            {/* <Input
              type="date"
              className="w-full"
              value={startDate}
              max={formatDate(new Date())}
              onChange={(e) => setStartDate(e.target.value)}
            /> */}
            <DatePicker
              className="w-full rounded-lg"
              format="DD-MM-YYYY"
              maxDate={dayjs(new Date())}
              onChange={(date) => setStartDate(date.format('YYYY-MM-DD'))}
              value={dayjs(startDate)}
              placeholder="Start Date"
              allowClear={false}
            />
          </div>
          <div className="min-w-[18rem]">
            <p className="font-semibold text-slate-500">End Date</p>
            {/* <Input
              type="date"
              className="w-full"
              value={endDate}
              max={formatDate(new Date())}
              onChange={(e) => setEndDate(e.target.value)}
            /> */}
            <DatePicker
              className="w-full rounded-lg"
              format="DD-MM-YYYY"
              value={dayjs(endDate)}
              maxDate={dayjs(new Date())}
              onChange={(date) => setEndDate(date.format('YYYY-MM-DD'))}
              placeholder="Start Date"
              allowClear={false}
            />
          </div>
        </div>
      </div>
      <div className="mt-4">
        <Table
          columns={columns}
          loading={isLoadingQuery}
          dataSource={
            Array.isArray(employees?.results)
              ? employees?.results?.filter((elem) => elem?.isHrms)
              : []
          }
          rowKey={(_, index) => index}
          pagination={false}
          size="middle"
          scroll={{ x: true }}
          locale={{ emptyText: 'No Employees added yet' }}
        />
        <Pagination
          limit={limit}
          page={page}
          totalPages={employees?.totalPages}
          totalResults={employees?.totalResults}
          setPage={setPage}
          setLimit={setLimit}
          className={`w-full`}
        />
      </div>
    </>
  );
};

export default AttendanceManagement;

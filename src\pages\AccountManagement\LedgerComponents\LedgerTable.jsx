import { useMediaQuery } from 'react-responsive';
import { mobileWidth } from '../../../helperFunction';

import { toast } from 'react-toastify';
import Spinner from '../../../components/global/components/Spinner';
import { customConfirm } from '../../../utils/customConfirm';

import { DeleteOutlined, EditOutlined } from '@ant-design/icons';
import { Table as AntTable, Button, Space, Tooltip } from 'antd';

import { useDeleteLedgerMutation } from '../../../slices/AccountManagement/ledgerApiSlice';

const LedgerTable = ({
  rows,
  isLoading,
  setEditData,
  setOpenModal,
  checkedRows,
  handleSelectAll,
  selectAll,
  handleCheckBoxChange,
  setOpenSidebar,
  setLedgerId,
}) => {
  const [deleteLedger] = useDeleteLedgerMutation();

  const isMobile = useMediaQuery({ query: mobileWidth });

  const handleDelete = async (accountId) => {
    const confirm = await customConfirm(
      'Are you sure you want to delete ?',
      'error'
    );
    if (!confirm) return;
    const res = await deleteLedger({ data: { id: accountId } });
    if (res.error) {
      toast.error('Failed to delete account. Please reload and try again.');
    } else {
      toast.success('Account deleted successfully');
    }
  };

  const columns = [
    !isMobile
      ? {
          title: (
            <div className="flex items-center">
              <input
                type="checkbox"
                className="mr-2"
                checked={selectAll}
                onChange={handleSelectAll}
              />
              Select All
            </div>
          ),
          dataIndex: 'checkbox',
          key: 'checkbox',
          render: (_, row) => (
            <input
              type="checkbox"
              onChange={(e) => {
                handleCheckBoxChange(e, row);
                e.stopPropagation();
              }}
              checked={checkedRows?.includes(row)}
            />
          ),
          width: 120,
        }
      : null,
    {
      title: 'Ledger Id',
      dataIndex: 'ledgerId',
      key: 'ledgerId',
      render: (text, row) => (
        <span
          className="text-blue-500 cursor-pointer hover:text-blue-300 font-medium"
          onClick={() => {
            setOpenSidebar(true);
            setLedgerId(row._id);
          }}
        >
          {row?.ledgerId}
        </span>
      ),
    },
    {
      title: 'Ledger Name',
      dataIndex: 'name',
      key: 'name',
      render: (text, row) => (
        <span
          className="text-blue-500 cursor-pointer hover:text-blue-300 font-medium"
          onClick={() => {
            setOpenSidebar(true);
            setLedgerId(row._id);
          }}
        >
          {row?.name?.name || row?.name || 'Unnamed Ledger'}
        </span>
      ),
    },
    {
      title: 'Account',
      dataIndex: 'account',
      key: 'account',
      render: (_, row) => (
        <div>
          <div className="font-medium">
            {row?.account?.accountDescription?.name || '-'}
          </div>
          <div className="text-xs text-gray-500">
            {row?.account?.accountNumber}
          </div>
        </div>
      ),
    },
    {
      title: 'Customer/Vendor',
      dataIndex: 'customerVendor',
      key: 'customerVendor',
      render: (_, row) => row?.customer?.name || row?.vendor?.name || '-',
    },
    {
      title: 'Bank Details',
      dataIndex: 'bankName',
      key: 'bankDetails',
      render: (_, row) => (
        <div>
          <div className="font-medium">{row?.bankName || '-'}</div>
          {row?.IFSCCode && (
            <div className="text-xs text-gray-500">{row?.IFSCCode}</div>
          )}
        </div>
      ),
    },
    {
      title: 'Amount',
      dataIndex: 'amount',
      key: 'amount',
      render: (_, row) => (
        <div className="text-right">
          <div
            className={`font-medium ${row?.amountType === 'credit' ? 'text-green-600' : 'text-red-600'}`}
          >
            ₹{row?.amount?.toLocaleString('en-IN') || '0'}
          </div>
          <div className="text-xs text-gray-500 capitalize">
            {row?.amountType || 'N/A'}
          </div>
        </div>
      ),
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 150,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="Edit Ledger">
            <Button
              type="text"
              icon={<EditOutlined />}
              size="small"
              onClick={() => {
                setEditData(record);
                setOpenModal(true);
              }}
              style={{ color: '#52c41a' }}
            />
          </Tooltip>
          <Tooltip title="Delete Ledger">
            <Button
              type="text"
              icon={<DeleteOutlined />}
              size="small"
              danger
              onClick={() => handleDelete(record._id)}
            />
          </Tooltip>
        </Space>
      ),
    },
  ].filter(Boolean);

  return (
    <AntTable
      columns={columns}
      dataSource={rows}
      loading={{
        spinning: isLoading,
        indicator: <Spinner />,
      }}
      rowKey="_id"
      pagination={false}
      size="middle"
      scroll={{ x: true }}
    />
  );
};

export default LedgerTable;

import { ArrowLeftOutlined, SaveOutlined } from '@ant-design/icons';
import { useState } from 'react';
import { toast } from 'react-toastify';
import Input from '../../../components/global/components/Input';
import SelectV2 from '../../../components/global/components/SelectV2';
import Textarea from '../../../components/global/components/Textarea';
import CustomTypesModal from '../global/CustomTypesModal';

import { Button } from 'antd';
import { useGetCustomTypesQuery } from '../../../slices/AccountManagement/customTypesApiSlice';
import {
  useCreateAccountMutation,
  useEditAccountMutation,
} from '../../../slices/AccountManagement/masterAccountApiSlice';

const typeOptions = [
  { name: 'Assets', value: 'assets' },
  { name: 'Liabilities', value: 'liabilities' },
  { name: 'Equity', value: 'equity' },
  { name: 'Revenue', value: 'revenue' },
  { name: 'Expenses', value: 'expenses' },
];

const stateOptions = {
  assets: 'Balance Sheet',
  liabilities: 'Balance Sheet',
  equity: 'Balance Sheet',
  revenue: 'Income Statement',
  expenses: 'Income Statement',
};

const CreateMasterAccount = ({ setOpenModal, editData, setEditData }) => {
  const [editAccount, { isLoading: isUpdating }] = useEditAccountMutation();
  const [createAccount, { isLoading: isCreating }] = useCreateAccountMutation();
  const isEditing = !!editData?._id;
  const isSubmitting = isCreating || isUpdating;
  const { data: customTypesData, isLoading: isCustomTypesLoading } =
    useGetCustomTypesQuery({ type: 'accountType' });

  const [formData, setFormData] = useState(
    editData?.accountNumber
      ? {
          ...editData,
          accountDescription: editData?.accountDescription?._id || '',
        }
      : {
          accountNumber: '',
          accountDescription: '',
          type: '',
          remarks: '',
        }
  );
  const [openCustomTypeModal, setOpenCustomTypeModal] = useState(false);

  const changeHandler = (e) => {
    const { name, value } = e.target;
    if (name === 'accountDescription' && value === 'addType') {
      setOpenCustomTypeModal(true);
    } else {
      setFormData((prev) => ({
        ...prev,
        [name]: value,
      }));
    }
  };

  const formValidation = () => {
    if (
      formData?.accountNumber === '' ||
      formData?.accountNumber === undefined
    ) {
      return { validated: false, message: 'Account Number is mandatory' };
    }
    if (formData?.type === '' || formData?.type === undefined) {
      return { validated: false, message: 'Type is mandatory' };
    }
    if (
      formData?.accountDescription === '' ||
      formData?.accountDescription === undefined
    ) {
      return { validated: false, message: 'Account Description is mandatory' };
    }
    return { validated: true };
  };

  const handleSubmit = async () => {
    let obj = {
      ...formData,
      statement: stateOptions?.[formData?.type],
    };
    let res;
    if (editData?._id) {
      let validation = formValidation();
      if (validation?.validated) {
        res = await editAccount({
          data: { updateData: obj, id: editData?._id },
        });
      } else {
        toast.error(validation?.message);
        return;
      }
    } else {
      let validation = formValidation();
      if (validation?.validated) {
        res = await createAccount({
          data: { ...formData, statement: stateOptions?.[formData?.type] },
        });
      } else {
        toast.error(validation?.message);
        return;
      }
    }
    if (!res?.error) {
      setOpenModal(false);
      setFormData({
        accountNumber: '',
        accountDescription: '',
        type: '',
        remarks: '',
      });
      toast.success(
        `Master Account ${editData?._id ? 'Updated' : 'Created'} successfully`
      );
    } else {
      toast.error(
        'Faced an error while creating account, please reload and try again.'
      );
    }
  };

  return (
    <>
      {openCustomTypeModal && (
        <CustomTypesModal
          type="accountType"
          openModal={openCustomTypeModal}
          setOpenModal={setOpenCustomTypeModal}
        />
      )}
      <div className="bg-white border border-gray-100 shadow-sm rounded-lg overflow-hidden mx-auto max-w-6xl">
        {/* Header */}
        <div className="bg-gray-50 px-4 py-3 border-b border-gray-100">
          <div className="flex items-center gap-3">
            <Button
              icon={<ArrowLeftOutlined />}
              onClick={() => {
                setOpenModal(false);
                setEditData({});
              }}
              type="text"
              size="small"
              className="hover:bg-gray-200"
            />
            <div>
              <h2 className="text-lg font-semibold text-gray-900 mb-0">
                {isEditing ? 'Edit' : 'Create'} Master Account
              </h2>
              <p className="text-sm text-gray-600 mb-0">
                {isEditing
                  ? 'Update account information'
                  : 'Create a new master account'}
              </p>
            </div>
          </div>
        </div>

        {/* Form Content */}
        <div className="p-4 space-y-4">
          {/* Account Information */}
          <div className="bg-white border border-gray-200 rounded-lg p-3">
            <h3 className="text-sm font-medium text-gray-700 mb-3">
              Account Information
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
              <div className="space-y-1">
                <label className="text-sm font-medium text-gray-600">
                  Account Number
                </label>
                <Input
                  value={formData.accountNumber}
                  name="accountNumber"
                  placeholder="Enter account number"
                  onChange={(e) => changeHandler(e)}
                  className="text-sm"
                />
              </div>
              <div className="space-y-1">
                <label className="text-sm font-medium text-gray-600">
                  Account Description
                </label>
                <SelectV2
                  name="accountDescription"
                  value={formData.accountDescription}
                  placeholder="Select account description"
                  isLoading={isCustomTypesLoading}
                  options={[
                    { name: '+ Add Type', value: 'addType' },
                    ...(customTypesData?.map((item) => ({
                      name: item.name,
                      value: item._id,
                    })) || []),
                  ]}
                  onChange={(e) => changeHandler(e)}
                  className="text-sm"
                />
              </div>
              <div className="space-y-1">
                <label className="text-sm font-medium text-gray-600">
                  Account Type
                </label>
                <SelectV2
                  name="type"
                  value={formData.type}
                  onChange={(e) => changeHandler(e)}
                  placeholder="Select account type"
                  options={typeOptions}
                  className="text-sm"
                />
              </div>
            </div>
          </div>

          {/* Remarks */}
          <div className="bg-white border border-gray-200 rounded-lg p-3">
            <h3 className="text-sm font-medium text-gray-700 mb-3">Remarks</h3>
            <Textarea
              name="remarks"
              value={formData.remarks}
              onChange={(e) => changeHandler(e)}
              rows={3}
              placeholder="Enter additional remarks or comments"
              className="text-sm resize-none"
            />
          </div>
        </div>

        {/* Footer Actions */}
        <div className="bg-gray-50 px-4 py-3 border-t border-gray-100">
          <div className="flex items-center justify-end gap-2">
            <Button
              onClick={() => {
                setOpenModal(false);
                setEditData({});
              }}
              size="small"
              className="text-sm px-4 py-1 h-8"
            >
              Cancel
            </Button>
            <Button
              type="primary"
              icon={<SaveOutlined />}
              onClick={handleSubmit}
              loading={isSubmitting}
              size="small"
              className="text-sm px-4 py-1 h-8"
            >
              {isEditing ? 'Update' : 'Create'} Master Account
            </Button>
          </div>
        </div>
      </div>
    </>
  );
};

export default CreateMasterAccount;

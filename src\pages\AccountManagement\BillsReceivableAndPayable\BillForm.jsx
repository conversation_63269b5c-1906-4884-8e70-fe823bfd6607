import {
  ArrowLeftOutlined,
  LoadingOutlined,
  SaveOutlined,
} from '@ant-design/icons';
import { <PERSON><PERSON>, But<PERSON>, DatePicker, Form, InputNumber, Spin } from 'antd';
import dayjs from 'dayjs';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useNavigate, useParams, useSearchParams } from 'react-router-dom';
import { toast } from 'react-toastify';
import SelectV2 from '../../../components/global/components/SelectV2';
import Textarea from '../../../components/global/components/Textarea';
import StaticProductTable from '../../../components/ProductFormats/StaticProductTable';
import UploadButton from '../../../components/UploadButton';
import { renderFieldsBasedOnType } from '../../../helperFunction';
import usePrefixIds from '../../../hooks/usePrefixIds';
import {
  useCreateBillMutation,
  useEditBillMutation,
  useGetBillByIdQuery,
} from '../../../slices/AccountManagement/billsApiSlice';
import { useGetAllcustomerQuery } from '../../../slices/customerDataSlice';
import { useGetDropdownsQuery } from '../../../slices/dropdownApiSlice';
import { useLazyQueryTemplateByIdQuery } from '../../../slices/dsahboardTemplateApiSlice';
import { useGetFormatsQuery } from '../../../slices/productFormatsApiSlice';
import { useGetAllVendorsForOptionsQuery } from '../../../slices/vendorApiSlice';

const BillForm = () => {
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { id: billId, type = 'payable' } = useParams();
  const returnTab = searchParams.get('returnTab') || 'receivable';
  const [additionalFields, setAdditionalFields] = useState(null);
  const [templateDropDownModal, setTemplateDropDownModal] = useState(false);
  const [newOptionStatus, setNewOptionStatus] = useState(false);
  const [dropdownIdx, setDropdownIdx] = useState(null);
  const [selectedTemplate, setSelectedTemplate] = useState(null);
  const [getTemplates, { data: templatesData }] =
    useLazyQueryTemplateByIdQuery();
  const [formData, setFormData] = useState({});

  const { IdGenComp, idCompData } = usePrefixIds({
    idFor: type === 'payable' ? 'payableBillId' : 'receivableBillId',
    templateIndex:
      additionalFields?.idIndex > -1 ? additionalFields?.idIndex : null,
    setIdData: setFormData,
  });

  const isEditing = !!billId;
  const {
    data: billData,
    isLoading: isFetchingBill,
    error: fetchError,
  } = useGetBillByIdQuery({ id: billId }, { skip: !isEditing });

  const [createBill, { isLoading: isCreating }] = useCreateBillMutation();
  const [editBill, { isLoading: isUpdating }] = useEditBillMutation();
  const { data: vendorsData } = useGetAllVendorsForOptionsQuery();
  const { data: customersData } = useGetAllcustomerQuery();
  const { data: formatsData } = useGetFormatsQuery();

  const [formError, setFormError] = useState(null);
  const [productInput, setProductInput] = useState([
    {
      key: Date.now() + Math.random(),
      itemId: '',
      productName: '',
      uom: '',
      hsn: '',
      quantity: '',
      rate: '',
      discount: '',
      amount: 0,
      cgst: '',
      sgst: '',
      cgstAmount: 0,
      sgstAmount: 0,
      igst: '',
      igstAmount: 0,
      totalAmount: 0,
      color: '#FFFFFF',
    },
  ]);
  const [productCharges, setProductCharges] = useState({});
  const [attachments, setAttachments] = useState([]);
  const { data: dropdownsData } = useGetDropdownsQuery();
  const isSubmitting = isCreating || isUpdating;

  const handleFormChange = useCallback(() => setFormError(null), []);

  useEffect(() => {
    const path = `/accountmanagement/bill/${type === 'payable' ? 'payable' : 'receivable'}Bill`;
    getTemplates({ path });
  }, [getTemplates, type]);

  useEffect(() => {
    if (isEditing && billData?.additionalFields) {
      setAdditionalFields(billData.additionalFields);
      setSelectedTemplate(billData.additionalFields);
    } else if (templatesData && Array.isArray(templatesData)) {
      const defaultTemplate =
        templatesData.find((t) => t?.name?.startsWith('Default')) ||
        templatesData?.[0];
      if (defaultTemplate) {
        setAdditionalFields(defaultTemplate);
        setSelectedTemplate(defaultTemplate);
      }
    }
  }, [isEditing, billData?.additionalFields, templatesData]);

  const handleInputChange = (
    fieldValue,
    fieldName,
    idx,
    colIndex,
    tableRowIndex
  ) => {
    if (tableRowIndex !== undefined && tableRowIndex !== null) {
      setAdditionalFields((prev) => {
        const updatedTemplateData = [...prev.templateData];
        const fieldWithTableIndex = idx;
        if (fieldWithTableIndex === -1) return prev;

        const updatedTableOptions = {
          ...updatedTemplateData[fieldWithTableIndex]?.tableOptions,
        };

        if (!updatedTableOptions.column) {
          updatedTableOptions.column = [];
        } else {
          updatedTableOptions.column = [...updatedTableOptions.column];
        }

        if (!updatedTableOptions.column[colIndex].selectedOptions) {
          updatedTableOptions.column[colIndex] = {
            columnName: updatedTableOptions.column[colIndex].columnName,
            columnType: updatedTableOptions.column[colIndex].columnType,
            dropdownOptions:
              updatedTableOptions.column[colIndex].dropdownOptions,
            selectedOptions: [],
          };
        }
        const updatedSelectedOptions = [
          ...updatedTableOptions.column[colIndex].selectedOptions,
        ];
        updatedSelectedOptions[tableRowIndex] = fieldValue;

        updatedTableOptions.column[colIndex] = {
          ...updatedTableOptions.column[colIndex],
          selectedOptions: updatedSelectedOptions,
        };

        updatedTemplateData[fieldWithTableIndex] = {
          ...updatedTemplateData[fieldWithTableIndex],
          tableOptions: updatedTableOptions,
        };

        return {
          ...prev,
          templateData: updatedTemplateData,
        };
      });
      return;
    }

    if (fieldValue === '+') {
      setDropdownIdx(idx);
      setTemplateDropDownModal(true);
    } else {
      const updatedAdditionalFields = additionalFields?.templateData?.map(
        (field) => {
          if (field?.fieldName === fieldName) {
            return {
              ...field,
              fieldValue,
            };
          } else {
            return field;
          }
        }
      );
      setAdditionalFields((prev) => ({
        ...prev,
        templateData: updatedAdditionalFields,
      }));
    }
  };

  useEffect(() => {
    if (isEditing && billData) {
      const formValues = {
        ...billData,
        vendor: billData.vendor?._id,
        customer: billData.customer?._id,
        date: billData.date ? dayjs(billData.date) : dayjs(),
        dueOn: billData.dueOn ? dayjs(billData.dueOn) : null,
        paidDate: billData.paidDate ? dayjs(billData.paidDate) : null,
      };
      form.setFieldsValue(formValues);

      if (billData.productDetails) {
        setProductInput(billData.productDetails.products || []);
        setProductCharges(billData.productDetails.charges || {});
      }
      if (billData.attachments) {
        setAttachments(billData.attachments);
      }
    } else if (!isEditing) {
      form.setFieldsValue({
        date: dayjs(),
        dueOn: dayjs().add(30, 'days'),
        status: 'Pending',
        type: type,
      });
    }
  }, [isEditing, billData, form, type, formatsData]);

  const uomOptions = useMemo(() => {
    return (
      dropdownsData?.dropdowns
        ?.find((e) => e.name === 'uom')
        ?.values?.map((option) => ({
          label: option,
          value: option,
        })) || []
    );
  }, [dropdownsData]);
  const handleSubmit = async () => {
    try {
      setFormError(null);
      const values = await form.validateFields();

      const billPayload = {
        ...values,
        idData: idCompData?.dataToReturn,
        date: values.date.format('DD-MM-YYYY'),
        dueOn: values.dueOn?.format('DD-MM-YYYY'),
        paidDate: values.paidDate?.format('DD-MM-YYYY'),
        type,
        totalAmount: parseFloat(values.totalAmount || calculateTotalAmount()),
        additionalFields:
          additionalFields || formData?.additionalFields || null,
        productDetails: {
          products: productInput,
          charges: productCharges,
        },
        attachments,
      };

      const action = isEditing
        ? editBill({ id: billId, data: billPayload })
        : createBill({ data: billPayload });
      await action.unwrap();

      toast.success(
        `${type} Bill ${isEditing ? 'Updated' : 'Created'} successfully!`
      );
      navigate(`/accountmanagement/bills?tab=${returnTab}`);
    } catch (error) {
      const message =
        error.data?.message || error.message || 'Failed to save bill';
      setFormError(message);
      toast.error(message);
    }
  };

  const handleBack = () =>
    navigate(`/accountmanagement/bills?tab=${returnTab}`);

  const getEntityOptions = () => {
    const data = type === 'payable' ? vendorsData : customersData?.customers;
    return (
      data?.map((item) => ({
        label: item.name || item.label || '',
        value: item._id || item.value || '',
      })) || []
    );
  };

  const calculateTotalAmount = useCallback(() => {
    let total = 0;
    productInput.forEach((item) => {
      total += parseFloat(item.amount || item.totalAmount || 0);
    });
    Object.values(productCharges).forEach((charge) => {
      if (!isNaN(charge)) total += parseFloat(charge);
    });
    return total;
  }, [productInput, productCharges]);

  useEffect(() => {
    const total = calculateTotalAmount();
    if (total > 0) form.setFieldValue('totalAmount', total);
  }, [productInput, productCharges, form, calculateTotalAmount]);

  if (isFetchingBill) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Spin size="large" indicator={<LoadingOutlined spin />} />
      </div>
    );
  }

  if (fetchError && isEditing) {
    return (
      <div className="flex items-center justify-center min-h-screen p-4">
        <div className="text-center p-6 max-w-md">
          <p className="text-red-500 mb-4">
            {fetchError.data?.message || 'Failed to load bill'}
          </p>
          <Button onClick={() => navigate(-1)}>Go Back</Button>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white border border-gray-100 shadow-sm rounded-lg overflow-hidden mx-auto max-w-6xl">
      {/* Header */}
      <div className="bg-gray-50 px-4 py-3 border-b border-gray-100">
        <div className="flex items-center gap-3">
          <Button
            icon={<ArrowLeftOutlined />}
            onClick={handleBack}
            type="text"
            size="small"
            className="hover:bg-gray-200"
          />
          <div>
            <h2 className="text-lg font-semibold text-gray-900 mb-0">
              {isEditing ? 'Edit' : 'Create'} {type} Bill
            </h2>
            <p className="text-sm text-gray-600 mb-0">
              {isEditing
                ? 'Update bill information'
                : 'Create a new bill entry'}
            </p>
          </div>
        </div>
      </div>

      {/* Form Content */}
      <div className="p-4 space-y-4">
        {formError && (
          <Alert
            message={formError}
            type="error"
            closable
            onClose={() => setFormError(null)}
            className="mb-4"
          />
        )}

        <Form
          form={form}
          layout="vertical"
          onValuesChange={handleFormChange}
          className="space-y-4"
        >
          {/* Basic Information */}
          <div className="bg-white border border-gray-200 rounded-lg p-3">
            <h3 className="text-sm font-medium text-gray-700 mb-3">
              Basic Information
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
              {isEditing ? (
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-600">
                    {type === 'payable'
                      ? 'Payable Bill ID'
                      : 'Receivable Bill ID'}
                  </label>
                  <input
                    className="w-full px-3 py-2 text-sm bg-gray-50 border border-gray-300 rounded-md"
                    disabled
                    value={
                      billData?.[
                        type === 'payable'
                          ? 'payableBillId'
                          : 'receivableBillId'
                      ] || 'N/A'
                    }
                  />
                </div>
              ) : (
                <Form.Item
                  name={
                    type === 'payable' ? 'payableBillID' : 'receivableBillID'
                  }
                  label={
                    <span className="text-sm font-medium text-gray-600">
                      Bill ID
                    </span>
                  }
                  className="mb-3"
                >
                  <IdGenComp {...idCompData} />
                </Form.Item>
              )}
              <div className="space-y-1">
                <label className="block mb-1 text-sm text-gray-500 font-medium">
                  Choose Template
                </label>
                <SelectV2
                  options={templatesData?.map((template) => ({
                    value: template?._id,
                    name: template?.name,
                  }))}
                  value={selectedTemplate?._id}
                  onChange={(e) => {
                    const template = templatesData.find(
                      (t) => t._id === e.target.value
                    );
                    if (selectedTemplate?._id === e.target.value) {
                      return;
                    }
                    setAdditionalFields(template);
                    setSelectedTemplate(template);
                  }}
                />
              </div>
              <Form.Item
                name="date"
                label={
                  <span className="text-sm font-medium text-gray-600">
                    Bill Date
                  </span>
                }
                rules={[{ required: true }]}
                className="mb-3"
              >
                <DatePicker className="w-full text-sm" format={'DD-MM-YYYY'} />
              </Form.Item>
              <Form.Item
                name="dueOn"
                label={
                  <span className="text-sm font-medium text-gray-600">
                    Due Date
                  </span>
                }
                className="mb-3"
              >
                <DatePicker className="w-full text-sm" format={'DD-MM-YYYY'} />
              </Form.Item>
              <Form.Item
                name="paidDate"
                label={
                  <span className="text-sm font-medium text-gray-600">
                    Paid Date
                  </span>
                }
                className="mb-3"
              >
                <DatePicker className="w-full text-sm" format={'DD-MM-YYYY'} />
              </Form.Item>
            </div>
          </div>

          {/* Entity & Amount */}
          <div className="bg-white border border-gray-200 rounded-lg p-3">
            <h3 className="text-sm font-medium text-gray-700 mb-3">
              {type === 'payable' ? 'Vendor' : 'Customer'} & Amount
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
              <Form.Item
                name={type === 'payable' ? 'vendor' : 'customer'}
                label={
                  <span className="text-sm font-medium text-gray-600">
                    {type === 'payable' ? 'Vendor' : 'Customer'}
                  </span>
                }
                rules={[{ required: true }]}
                className="mb-3"
              >
                <SelectV2
                  placeholder="Select"
                  options={getEntityOptions()}
                  className="text-sm"
                />
              </Form.Item>
              <Form.Item
                name="totalAmount"
                label={
                  <span className="text-sm font-medium text-gray-600">
                    Amount
                  </span>
                }
                className="mb-3"
              >
                <InputNumber
                  className="w-full text-sm"
                  min={0}
                  precision={2}
                  formatter={(v) =>
                    `₹ ${v}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
                  }
                  parser={(v) => v.replace(/₹\s?|(,*)/g, '')}
                />
              </Form.Item>
              <Form.Item
                name="status"
                label={
                  <span className="text-sm font-medium text-gray-600">
                    Status
                  </span>
                }
                rules={[{ required: true }]}
                className="mb-3"
              >
                <SelectV2
                  placeholder="Select status"
                  options={[
                    { value: 'Pending', label: 'Pending' },
                    { value: 'Paid', label: 'Paid' },
                    { value: 'Overdue', label: 'Overdue' },
                    { value: 'Cancelled', label: 'Cancelled' },
                  ]}
                  className="text-sm"
                />
              </Form.Item>
            </div>
          </div>

          {/* Attachments */}
          <div className="bg-white border border-gray-200 rounded-lg p-3">
            <h3 className="text-sm font-medium text-gray-700 mb-3">
              Attachments
            </h3>
            <Form.Item className="mb-0">
              <UploadButton
                onChange={setAttachments}
                multiple
                accept="image/*,.pdf,.doc,.docx"
                width="w-full"
              />
            </Form.Item>
          </div>

          {/* Product Details */}
          <div className="bg-white border border-gray-200 rounded-lg p-3">
            <h3 className="text-sm font-medium text-gray-700 mb-3">
              Product Details
            </h3>
            <StaticProductTable
              input={productInput}
              setInput={setProductInput}
              charges={productCharges}
              setCharges={setProductCharges}
              uomOptions={uomOptions}
            />
          </div>

          {/* Additional Fields */}
          {additionalFields?.templateData?.length > 0 && (
            <div className="bg-white border border-gray-200 rounded-lg p-3">
              <h3 className="text-sm font-medium text-gray-700 mb-3">
                Additional Fields
              </h3>
              <section className="w-full">
                {renderFieldsBasedOnType(
                  additionalFields,
                  handleInputChange,
                  templateDropDownModal,
                  setTemplateDropDownModal,
                  setAdditionalFields,
                  newOptionStatus,
                  setNewOptionStatus,
                  dropdownIdx,
                  setDropdownIdx
                )}
              </section>
            </div>
          )}

          {/* Description */}
          <div className="bg-white border border-gray-200 rounded-lg p-3">
            <h3 className="text-sm font-medium text-gray-700 mb-3">
              Description
            </h3>
            <Form.Item name="description" className="mb-0">
              <Textarea
                rows={3}
                placeholder="Enter additional description or comments"
                className="text-sm resize-none"
              />
            </Form.Item>
          </div>
        </Form>
      </div>

      {/* Footer Actions */}
      <div className="bg-gray-50 px-4 py-3 border-t border-gray-100">
        <div className="flex items-center justify-end gap-2">
          <Button
            onClick={handleBack}
            size="small"
            className="text-sm px-4 py-1 h-8"
          >
            Cancel
          </Button>
          <Button
            type="primary"
            icon={<SaveOutlined />}
            onClick={handleSubmit}
            loading={isSubmitting}
            size="small"
            className="text-sm px-4 py-1 h-8"
          >
            {isEditing ? 'Update' : 'Create'} Bill
          </Button>
        </div>
      </div>
    </div>
  );
};

export default BillForm;

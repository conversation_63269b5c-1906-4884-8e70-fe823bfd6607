import { Typography } from 'antd';
import { Edit, Trash } from 'lucide-react';
import { FaNoteSticky, FaRegCalendar } from 'react-icons/fa6';
import { HiMiniIdentification } from 'react-icons/hi2';
import { PiFilePdfDuotone } from 'react-icons/pi';
import { TbCategory } from 'react-icons/tb';
import RightSidebar from '../../../components/global/components/RightSidebar';
import Table from '../../../components/global/components/Table';
import Tooltip from '../../../components/global/components/ToolTip';
import {
  handlePdf,
  renderAdditionalFieldsBasedOnType,
} from '../../../helperFunction';
import { useGetJournalByIdQuery } from '../../../slices/AccountManagement/journalApiSlice';
import { useLazyGetPdfQuery } from '../../../slices/pdfApiSlice';
const { Title, Text } = Typography;

const JournalSidebar = ({ openSidebar, setOpenSidebar, journalId }) => {
  const { data: journal, isLoading: isJournalLoading } = useGetJournalByIdQuery(
    { id: journalId },
    { skip: journalId === undefined }
  );
  const [getPdf, { isFetching: isFetchingPdf }] = useLazyGetPdfQuery();
  const closeSidebar = () => {
    setOpenSidebar(false);
  };

  const calculateDebit = () => {
    let sum = 0;
    journal?.accounts.forEach((elem) => (sum = sum + parseInt(elem?.debit)));
    return sum;
  };

  const calculateCredit = () => {
    let sum = 0;
    journal?.accounts.forEach((elem) => (sum = sum + parseInt(elem?.credit)));
    return sum;
  };

  const totalDifference = () => {
    return calculateDebit() - calculateCredit();
  };
  const handlePdfDownload = () => {
    handlePdf(getPdf, journal?._id, 'journal');
  };
  const ActionButton = ({
    onClick,
    disabled,
    icon,
    tooltip,
    color = 'text-gray-700',
  }) => {
    const Icon = icon;

    return (
      <button
        onClick={onClick}
        disabled={disabled}
        className={`p-2 rounded-full transition-all ${
          disabled
            ? 'opacity-50 cursor-not-allowed'
            : 'hover:bg-gray-100 active:bg-gray-200'
        } group relative`}
      >
        <Icon className={`w-5 h-5 ${color}`} />
        {tooltip && (
          <span className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 hidden group-hover:block bg-gray-800 text-white text-xs px-2 py-1 rounded whitespace-nowrap z-10">
            {tooltip}
          </span>
        )}
      </button>
    );
  };
  const LoadingSpinner = () => (
    <div className="w-5 h-5 rounded-full border-2 border-gray-300 border-t-blue-500 animate-spin" />
  );
  return (
    <RightSidebar
      title="Journal Details"
      onClose={closeSidebar}
      scale={736}
      openSideBar={openSidebar}
      setOpenSideBar={setOpenSidebar}
      isLoading={isJournalLoading}
    >
      <div className="flex items-center justify-between mb-4">
        <h4>{journal?.journalId}</h4>
        <div className="flex items-center gap-2">
          <ActionButton icon={Edit} tooltip="Edit" onClick={() => {}} />
          <ActionButton
            icon={Trash}
            tooltip="Delete"
            color="text-red-500"
            onClick={() => {}}
          />
          <ActionButton
            icon={isFetchingPdf ? LoadingSpinner : PiFilePdfDuotone}
            tooltip="Download PDF"
            color="text-yellow-600"
            onClick={handlePdfDownload}
            disabled={isFetchingPdf}
          />
        </div>
      </div>
      <hr />
      <div className="mt-4">
        <Title level={5} className="text-lg text-gray-600 mt-5">
          General Details
        </Title>
        <div className="grid gap-6 border rounded-lg p-4">
          <div className="flex items-center justify-between border-b pb-2">
            <Text className="flex items-center gap-3 text-gray-600">
              <HiMiniIdentification className="text-green-500 text-xl" />
              <span className="font-medium">REFERENCE ID</span>
            </Text>
            <Text className="text-gray-800 font-medium">
              {journal?.referenceId}
            </Text>
          </div>
          <div className="flex items-center justify-between border-b pb-2">
            <Text className="flex items-center gap-3 text-gray-600">
              <FaRegCalendar className="text-indigo-500 text-xl" />
              <span className="font-medium">DATE</span>
            </Text>
            <Text className="text-gray-800 font-medium">
              {journal?.date !== undefined &&
                new Date(journal?.date)?.toISOString()?.split('T')[0]}
            </Text>
          </div>
          <div className="flex items-center justify-between border-b pb-2">
            <Text className="flex items-center gap-3 text-gray-600">
              <TbCategory className="text-purple-500 text-xl" />
              <span className="font-medium">TYPE</span>
            </Text>
            <Text className="text-gray-800 font-medium">
              {journal?.type?.name}
            </Text>
          </div>
          <div className="flex items-center justify-between border-b pb-2">
            <Text className="flex items-center gap-3 text-gray-600">
              <FaNoteSticky className="text-blue-500 text-xl" />
              <span className="font-medium">NOTE</span>
            </Text>
            {journal?.note?.length > 15 ? (
              <Tooltip
                text={journal?.note}
                maxWidth={'!max-w-[500px]'}
                minWidth={'!min-w-[250px]'}
              >
                {journal?.note?.slice(0, 15) + '...'}
              </Tooltip>
            ) : (
              <Text className="font-medium text-gray-800">
                {journal?.note || '-'}
              </Text>
            )}
          </div>
        </div>
      </div>
      <div className="mt-4">
        <Title level={5} className="text-lg text-gray-600 mt-5 mb-3">
          Account Details
        </Title>
        <div className="overflow-x-auto border border-gray-200 rounded-lg">
          <Table className="w-full border-collapse">
            <Table.Head>
              <Table.Row className="bg-gray-50">
                <Table.Th className="text-sm font-medium text-gray-600 px-3 py-2 border-r border-gray-200">
                  Account
                </Table.Th>
                <Table.Th className="text-sm font-medium text-gray-600 px-3 py-2 border-r border-gray-200">
                  Description
                </Table.Th>
                <Table.Th className="text-sm font-medium text-gray-600 px-3 py-2 border-r border-gray-200">
                  Party
                </Table.Th>
                <Table.Th className="text-sm font-medium text-gray-600 px-3 py-2 border-r border-gray-200">
                  Debit
                </Table.Th>
                <Table.Th className="text-sm font-medium text-gray-600 px-3 py-2 border-r border-gray-200">
                  Credit
                </Table.Th>
              </Table.Row>
            </Table.Head>
            <Table.Body>
              {journal?.accounts?.map((elem, index) => (
                <Table.Row
                  key={elem?._id}
                  className={`${index % 2 === 0 ? 'bg-white' : 'bg-gray-25'} hover:bg-blue-50 transition-colors border-b border-gray-100`}
                >
                  <Table.Td className="px-3 py-2 border-r border-gray-200">
                    <div className="font-medium">
                      {elem?.account?.accountDescription?.name || '-'}
                    </div>
                  </Table.Td>
                  <Table.Td className="px-3 py-2 border-r border-gray-200">
                    {elem?.description?.length > 20 ? (
                      <Tooltip
                        text={elem?.description}
                        maxWidth={'!max-w-[300px]'}
                        minWidth={'!min-w-[200px]'}
                      >
                        <span className="cursor-help">
                          {elem?.description?.slice(0, 20) + '...'}
                        </span>
                      </Tooltip>
                    ) : (
                      <span>{elem?.description || '-'}</span>
                    )}
                  </Table.Td>
                  <Table.Td className="px-3 py-2 border-r border-gray-200">
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                      {elem?.vendor?.name || elem?.customer?.name || '-'}
                    </span>
                  </Table.Td>
                  <Table.Td className="px-3 py-2 border-r border-gray-200">
                    <span
                      className={`${elem?.credit > 0 ? 'text-green-600' : 'text-gray-400'}`}
                    >
                      ₹ {elem?.credit || '0.00'}
                    </span>
                  </Table.Td>
                  <Table.Td className="px-3 py-2 border-r border-gray-200">
                    <span
                      className={`${elem?.debit > 0 ? 'text-red-600' : 'text-gray-400'}`}
                    >
                      ₹ {elem?.debit || '0.00'}
                    </span>
                  </Table.Td>
                </Table.Row>
              ))}
            </Table.Body>
          </Table>
        </div>

        {/* Summary Section */}
        <div className="mt-3 pt-3 border-t border-gray-200 flex justify-end gap-4">
          <div className="bg-gray-50 rounded-lg p-3 space-y-2">
            <div className="flex justify-between items-center text-sm">
              <span className="text-gray-600 min-w-[10rem]">Total Debit:</span>
              <span className="font-medium text-red-600">
                ₹ {calculateDebit() || 0}
              </span>
            </div>
            <div className="flex justify-between items-center text-sm">
              <span className="text-gray-600 min-w-[10rem]">Total Credit:</span>
              <span className="font-medium text-green-600">
                ₹ {calculateCredit() || 0}
              </span>
            </div>
            <hr className="border-gray-300" />
            <div className="flex justify-between items-center text-sm font-semibold">
              <span className="text-gray-700">Difference:</span>
              <span
                className={`${totalDifference() === 0 ? 'text-green-600' : 'text-red-600'}`}
              >
                ₹ {totalDifference() || 0}
              </span>
            </div>
          </div>
        </div>
      </div>
      {journal?.additionalFields?.templateData?.length > 0 && (
        <div className="mt-6">
          <Title level={5} className="text-lg text-gray-600 mb-4">
            Template Details
          </Title>
          <div className="border rounded-lg p-4 space-y-4">
            {journal?.additionalFields?.templateData?.map((data) => (
              <div key={data?._id} className="flex flex-col gap-2">
                <div className="flex items-center justify-between pb-2">
                  <Text className="text-gray-600 font-medium">
                    {data?.fieldName}
                  </Text>
                  <Text className="text-gray-800">
                    {renderAdditionalFieldsBasedOnType(
                      data?.fieldType,
                      data?.fieldValue,
                      data
                    )}
                  </Text>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </RightSidebar>
  );
};

export default JournalSidebar;

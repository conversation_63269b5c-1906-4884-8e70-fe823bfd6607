import {
  CalendarOutlined,
  ContactsOutlined,
  CreditCardOutlined,
  DatabaseOutlined,
  FieldNumberOutlined,
  FileDoneOutlined,
  HomeOutlined,
  IdcardOutlined,
  MailOutlined,
} from '@ant-design/icons';
import { <PERSON><PERSON>, Card, Collapse, Modal, Tooltip, Typography } from 'antd';
import { Copy, Edit, History, Trash } from 'lucide-react';
import { useContext, useEffect, useState } from 'react';
import { FaWhatsapp } from 'react-icons/fa';
import { PiFilePdfDuotone } from 'react-icons/pi';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import {
  FormatDate,
  checkApprovalAccess,
  compareTwoObj,
  getDecodedHTML,
  getLocalDateTime,
  handlePdf,
} from '../../helperFunction';
import { useLazyQueryHistoryQuery } from '../../slices/editHistoryApiSlice';
import { useLazyGetPdfQuery } from '../../slices/pdfApiSlice';
import {
  useGetPoForSidebarQuery,
  useUpdatePurchaseOrderStatusMutation,
} from '../../slices/purchaseOrderApiSlice';
import { Store } from '../../store/Store';
import { DEFAULT_PO_PRODUCT_DETAILS_HEADER } from '../../utils/Constant';
import { customConfirm } from '../../utils/customConfirm';
import { toCapitalize } from '../../utils/toCapitalize';
import FinancialDetails from '../FinancialDetails';
import SidebarProductDetailsTable from '../global/components/SidebarProductDetailsTable';
import Spinner from '../global/components/Spinner';
import Table from '../global/components/Table';
import Textarea from '../global/components/Textarea';
import MediaDetails from '../v3/global/components/MediaDetails';
import ShowTemplateValues from '../v3/global/components/ShowTemplateValues';
import PoGrnDetails from './poGrnDetails';
import PoStatusTimeline from './PoStatusTimeline';
const { Title, Text, Paragraph } = Typography;
const { Panel } = Collapse;

const colors = {
  PENDING: 'bg-yellow-200 text-yellow-600',
  REJECTED: 'bg-red-200 text-red-600',
  APPROVED: 'bg-[#DCF0DD] text-[#0F6A2E]',
  COMPLETED: 'bg-green-200 text-green-600',
  'EXCESS MATERIAL': 'bg-indigo-200 text-indigo-600',
  'PARTIALLY COMPLETED': 'bg-orange-200 text-orange-700',
};
export default function PODashboardSidebar({
  selectedPo,
  setShowEmailModal,
  handleDelete,
  setShowSidebar,
  handleApprovePO,
  fromKanban = false,
  setOpenShareModal,
  setReadMore,
  showTable,
  setShowTable,
  showTimeline,
  setShowTimeline,
}) {
  const {
    defaults,
    state: { user },
  } = useContext(Store);

  const [TemplateInfo, setTemplateInfo] = useState({});
  const [grnSidebarData, setGrnSidebarData] = useState([]);
  const [vendorData, setVendorData] = useState([]);
  const [showGrnSidebar, setShowGrnSidebar] = useState(false);

  const handleNavigate = useNavigate();
  const [getHistory, { data: historyData }] = useLazyQueryHistoryQuery();
  const [showVersionDetails, setShowVersionDetails] = useState(null);
  const [currentVersionData, setCurrentVersionData] = useState(null);
  const [openStatusModal, setOpenStatusModal] = useState(false);
  const [rejectionReason, setRejectionReason] = useState('');
  const [updatePurchaseOrderStatus] = useUpdatePurchaseOrderStatusMutation();
  const { data: poData, isFetching: isFetchingPoData } =
    useGetPoForSidebarQuery(
      { id: selectedPo?._id },
      { skip: !selectedPo?._id, refetchOnMountOrArgChange: true }
    );

  const [getPdf, { isFetching: isFetchingPdf }] = useLazyGetPdfQuery();

  useEffect(() => {
    if (poData) {
      setGrnSidebarData(poData?.grn || []);
      setTemplateInfo(poData?.additionalFields);
      setVendorData(() => [poData?.vendor]);
      setShowGrnSidebar(poData);
    }
  }, [poData]);

  const getStatusColor = (status) => {
    if (colors[status]) {
      return colors[status];
    }
    return 'bg-fuchsia-200 text-fuchsia-600';
  };
  const calculateTaxAmount = (amount, percentage) =>
    parseFloat(((amount * (percentage || 0)) / 100)?.toFixed(2));

  const calcAmount = {
    subTotal: 0,
    igst: 0,
    cgst: 0,
    sgst: 0,
    totalTaxAmount: 0,
  };

  // const pdf = files;
  useEffect(() => {
    if (selectedPo) {
      getHistory({
        id: selectedPo._id,
        populate: JSON.stringify([
          { path: 'versionData.data.files', model: 'Media' },
          {
            path: 'versionData.data.indentLink',
            model: 'purchaserequests',
          },
          {
            path: 'versionData.data.vendor',
            model: 'Vendor',
          },
        ]),
      });
    }
  }, [getHistory, selectedPo]);

  showVersionDetails?.data?.items?.forEach((i) => {
    const amt =
      i?.quantity *
        i?.rate *
        // (conversion ? parseFloat(conversion.conversionValue) : 1) * // No Need TO Multiple With Conversion Value For Now
        (1 - (i?.discount || 0) / 100) || 0;

    const cgst = +calculateTaxAmount(amt, i?.cgst || 0) || 0;
    const sgst = +calculateTaxAmount(amt, i?.sgst || 0) || 0;
    const igst = +calculateTaxAmount(amt, i?.igst || 0) || 0;
    calcAmount.subTotal += +amt;
    calcAmount.cgst += cgst;
    calcAmount.sgst += sgst;
    calcAmount.igst += igst;
    calcAmount.totalTaxAmount += cgst + sgst + igst;
  });

  const sTotal = +(showVersionDetails?.data?.subTotal?.length > 0 &&
  showVersionDetails?.data?.subTotal
    ? showVersionDetails?.data?.subTotal
    : calcAmount?.subTotal);

  const txAmount = calcAmount?.totalTaxAmount;

  const tAmount = sTotal + txAmount;

  useEffect(() => {
    if (historyData && historyData.versionData) {
      const lastVersionData =
        historyData.versionData[historyData.versionData.length - 1];
      setShowVersionDetails(lastVersionData);
    }
  }, [historyData]);

  const hasApprovalAccess = checkApprovalAccess('/purchase/po');

  const isEditDeleteAllowed = () => {
    if (
      defaults?.defaultParam?.projectDefaults?.disabledApprovalFor.includes(
        'purchaseOrder'
      )
    )
      return true;
    else {
      if (poData?.poStatus === 'pending') return true;
      else return false;
    }
  };
  // Check if edit operations are allowed
  const isEditAllowed = () => {
    if (!user?.canEditPurchaseOrder) {
      return false;
    }
    const findApproveStatus = poData?.statusTimeline?.find(
      (item) => item?.status?.toLowerCase() === 'approved'
    );
    if (findApproveStatus && user?.canEditApprovedPurchaseOrder) {
      return true;
    }
    return !findApproveStatus;
  };

  const isDeleteAllowed = () => {
    if (!user?.canDeletePurchaseOrder) {
      return false;
    } else {
      const findApproveStatus = poData?.statusTimeline?.find(
        (item) => item?.status?.toLowerCase() === 'approved'
      );
      return !findApproveStatus;
    }
  };

  const currentVersion = currentVersionData?.version;
  const comparableVersion = currentVersion - 1;
  let diffToShow = {};
  if (comparableVersion !== 0) {
    diffToShow = compareTwoObj(
      historyData?.versionData[comparableVersion - 1],
      historyData?.versionData[currentVersion - 1]
    );
  }
  const addedTermsAndCond = diffToShow?.added?.data?.terms;
  const isCommentsChange = diffToShow?.updated?.data?.comments ? true : false;
  const isPaymentTermChange = diffToShow?.updated?.data?.paymentTerm
    ? true
    : false;
  const isDeliveryAddressChange = diffToShow?.updated?.data?.deliveryAddress
    ? true
    : false;
  const isDeliveryDatChange = diffToShow?.updated?.data?.deliveryDate
    ? true
    : false;
  const isBillingAddressChange = diffToShow?.updated?.data?.billingAddress
    ? true
    : false;
  const isVendorChange = diffToShow?.updated?.data?.vendor ? true : false;
  const handleUpdatePurchaseOrderStatus = async () => {
    const confirm = await customConfirm(
      `
Are you sure you want to reject ${selectedPo?.poID}?`,
      'error'
    );
    if (!confirm) return;
    try {
      const res = await updatePurchaseOrderStatus({
        id: selectedPo?._id,
        poStatus: 'Rejected',
        rejectionReason,
      });
      if (res) {
        toast.success('PO Rejected', {
          theme: 'colored',
          position: 'top-right',
          toastId: 'PO Rejected',
        });
        setOpenStatusModal(false);
        setRejectionReason('');
      }
    } catch (err) {
      toast.error(err?.response?.data?.message || err.message, {
        theme: 'colored',
        position: 'top-right',
      });
    }
  };
  const renderIconButton = (
    onClick,
    tooltipText,
    IconComponent,
    additionalClass = ''
  ) => (
    <Tooltip title={tooltipText} placement="top">
      <Button
        type="text"
        onClick={onClick}
        className={`flex items-center justify-center ${additionalClass}`}
        icon={IconComponent}
      />
    </Tooltip>
  );

  if (isFetchingPoData) {
    return <Spinner />;
  }

  return (
    <>
      <div
        key={selectedPo?._id}
        className="text-xl font-bold flex justify-between"
      >
        <div className="flex gap-x-3">{selectedPo?.poID}</div>
        <div className="min-w-2/3 flex gap-x-3 justify-around items-center">
          {!fromKanban && (
            <>
              {/* share button */}
              {renderIconButton(
                () => {
                  setOpenShareModal(true), setShowSidebar(false);
                },
                'Share',
                <FaWhatsapp className="w-6 h-6 text-green-primary" />
              )}

              {/* history button */}

              {renderIconButton(
                () => setShowTable(!showTable),
                'History',
                <History className="w-6 h-6" />,
                ''
              )}

              {/* copy button */}
              {renderIconButton(
                () =>
                  handleNavigate(
                    `/purchase/po/createpurchaseorderv2/${selectedPo._id}?isCopy=true`
                  ),
                'Copy',
                <Copy className="w-5 h-5 text-blue-500" />
              )}
            </>
          )}
          {isEditAllowed() && !fromKanban && (
            <>
              {isEditAllowed() &&
                renderIconButton(
                  () => {
                    handleNavigate(
                      `/purchase/po/createpurchaseorderv2/${selectedPo._id}`
                    );
                  },
                  'Edit',
                  <Edit className="w-5 h-5" />
                )}
              {/* Delete Button */}
              {isDeleteAllowed() &&
                renderIconButton(
                  () => {
                    handleDelete(selectedPo?._id);
                  },
                  'Delete',
                  <Trash className="w-5 h-5 text-red-500" />,
                  '!text-red-500'
                )}
            </>
          )}

          {!fromKanban && (
            <>
              {/* PDF Generator */}
              {isFetchingPdf ? (
                <div className="cursor-not-allowed">
                  <Spinner size={5} />
                </div>
              ) : (
                renderIconButton(
                  () =>
                    handlePdf(
                      getPdf,
                      selectedPo?._id,
                      'purchaseOrder',
                      null,
                      localStorage.getItem('activePOProductDetailsHeader') ||
                        JSON.stringify(DEFAULT_PO_PRODUCT_DETAILS_HEADER)
                    ),
                  'PDF',
                  <PiFilePdfDuotone className="w-6 h-6 text-yellow-600" />
                )
              )}
            </>
          )}
        </div>
      </div>
      <div className={`${showTable ? '' : 'hidden'} mt-5`}>
        <div className="show-info text-sm flex gap-2 items-center justify-end my-2">
          <div className="w-2 h-2 bg-red-500 rounded-full"></div>
          <span>Indicates Edited Field</span>
        </div>
        <table
          style={{
            width: '100%',
            borderCollapse: 'collapse',
            fontFamily:
              'system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
          }}
        >
          <thead>
            <tr style={{ borderBottom: '1px solid #e5e7eb' }}>
              {['#', 'Version', 'Edited/Created At'].map((heading) => (
                <th
                  key={heading}
                  style={{
                    padding: '12px 8px',
                    textAlign: 'left',
                    color: '#6b7280',
                    fontSize: '14px',
                    fontWeight: '500',
                  }}
                >
                  {heading}
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {historyData?.versionData?.map((version, idx) => {
              const isLatest =
                version.version === historyData?.versionData?.length;
              const handleRowClick = () => {
                setShowVersionDetails(version);
                setCurrentVersionData(version);
              };

              return (
                <tr
                  key={version._id}
                  style={{
                    cursor: 'pointer',
                    backgroundColor:
                      showVersionDetails?._id === version._id
                        ? '#f3f4f6'
                        : 'transparent',
                    transition: 'background-color 200ms',
                  }}
                  onClick={handleRowClick}
                >
                  {[
                    idx,
                    isLatest ? 'Latest' : version.version,
                    getLocalDateTime(version.editedAt),
                  ].map((value, i) => (
                    <td
                      key={i}
                      style={{
                        padding: '12px 8px',
                        borderTop: '1px solid #e5e7eb',
                        color:
                          showVersionDetails?._id === version._id
                            ? '#111827'
                            : '#374151',
                      }}
                    >
                      {value}
                    </td>
                  ))}
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>
      <div className="mt-4">
        <section>
          {showVersionDetails?.data?.indentLink?.map((indentdata) => (
            <Table className="mb-6" key={indentdata?._id}>
              <Table.Head>
                <Table.Row>
                  <Table.Th>Indent: {indentdata?.indent_no}</Table.Th>
                  <Table.Th></Table.Th>
                </Table.Row>
              </Table.Head>
              <Table.Body>
                <Table.Row>
                  <Table.Td>Indent Date</Table.Td>
                  <Table.Td>{FormatDate(indentdata?.createdAt)}</Table.Td>
                </Table.Row>
                <Table.Row>
                  <Table.Td>Requested By</Table.Td>
                  <Table.Td>{indentdata?.request_by}</Table.Td>
                </Table.Row>
                <Table.Row>
                  <Table.Td>Department</Table.Td>
                  <Table.Td>{indentdata?.department}</Table.Td>
                </Table.Row>
                <Table.Row>
                  <Table.Td>{indentdata?.type} Name</Table.Td>
                  <Table.Td>{indentdata?.product_name}</Table.Td>
                </Table.Row>
                <Table.Row>
                  <Table.Td>UOM</Table.Td>
                  <Table.Td>{indentdata?.uom}</Table.Td>
                </Table.Row>
                <Table.Row>
                  <Table.Td>Delivery Date</Table.Td>
                  <Table.Td>{FormatDate(indentdata?.delivery_date)}</Table.Td>
                </Table.Row>
                <Table.Row>
                  <Table.Td>Vendor Name</Table.Td>
                  <Table.Td>{indentdata?.vendor_name}</Table.Td>
                </Table.Row>
                <Table.Row>
                  <Table.Td>Indent Status</Table.Td>
                  <Table.Td>{toCapitalize(indentdata?.status)}</Table.Td>
                </Table.Row>
                <Table.Row>
                  <Table.Td>PO Status</Table.Td>
                  <Table.Td>{toCapitalize(indentdata?.po)}</Table.Td>
                </Table.Row>
                <Table.Row>
                  <Table.Td>Quantity</Table.Td>
                  <Table.Td>{indentdata?.quantity ?? '-'}</Table.Td>
                </Table.Row>
              </Table.Body>
            </Table>
          ))}
        </section>
        <hr className="border-b-2 !w-full mt-1 !px-0" />
        <div className="w-full flex justify-end mt-3">
          <span
            className="text-sm text-sky-500 flex items-center cursor-pointer"
            onClick={() => setShowTimeline((prev) => !prev)}
          >
            {showTimeline ? (
              <>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-5 w-5 mr-1"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fillRule="evenodd"
                    d="M3.707 2.293a1 1 0 00-1.414 1.414l14 14a1 1 0 001.414-1.414l-1.473-1.473A10.014 10.014 0 0019.542 10C18.268 5.943 14.478 3 10 3a9.958 9.958 0 00-4.512 1.074l-1.78-1.781zm4.261 4.26l1.514 1.515a2.003 2.003 0 012.45 2.45l1.514 1.514a4 4 0 00-5.478-5.478z"
                    clipRule="evenodd"
                  />
                  <path d="M12.454 16.697L9.75 13.992a4 4 0 01-3.742-3.741L2.335 6.578A9.98 9.98 0 00.458 10c1.274 4.057 5.065 7 9.542 7 .847 0 1.669-.105 2.454-.303z" />
                </svg>
                Hide Timeline
              </>
            ) : (
              <>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-5 w-5 mr-1"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                  <path
                    fillRule="evenodd"
                    d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z"
                    clipRule="evenodd"
                  />
                </svg>
                Show Timeline
              </>
            )}
          </span>
        </div>
        {showTimeline ? (
          <PoStatusTimeline data={poData} />
        ) : (
          <section className="mt-5">
            {grnSidebarData?.length > 0 && (
              <PoGrnDetails
                grnSidebarData={grnSidebarData}
                showGrnSidebar={showGrnSidebar}
              />
            )}

            <section className="mt-7">
              <Title level={5} className="text-lg text-gray-600 mt-5">
                Purchase Order Details
              </Title>
              <div className="grid gap-6 border rounded-lg p-4">
                <div className="flex items-center justify-between border-b pb-2">
                  <Text className="flex items-center gap-3 text-gray-600">
                    <FileDoneOutlined className="text-green-500 text-xl" />
                    <span className="font-medium">Purchase Order Status</span>
                  </Text>
                  <Text
                    className={`${getStatusColor(
                      showVersionDetails?.data?.poStatus?.toUpperCase().trim()
                    )} px-3 py-1 rounded-full font-medium whitespace-nowrap`}
                  >
                    {toCapitalize(showVersionDetails?.data?.poStatus)}
                  </Text>
                </div>

                <div className="flex items-center justify-between border-b pb-2">
                  <Text className="flex items-center gap-3 text-gray-600">
                    <CalendarOutlined className="text-indigo-500 text-xl" />
                    <span className="font-medium">
                      Purchase Order Creation Date
                    </span>
                  </Text>
                  <Text className="text-gray-800 font-medium">
                    {new Date(
                      showVersionDetails?.data?.createdAt
                    ).toLocaleDateString('en-in')}
                  </Text>
                </div>

                <div className="flex items-center justify-between border-b pb-2">
                  <Text className="flex items-center gap-3 text-gray-600">
                    <IdcardOutlined className="text-purple-500 text-xl" />
                    <span className="font-medium">Purchase Order ID</span>
                  </Text>
                  <Text className="text-gray-800 font-medium">
                    {showVersionDetails?.data?.poID}
                  </Text>
                </div>

                <div className="flex items-center justify-between border-b pb-2">
                  <Text className="flex items-center gap-3 text-gray-600">
                    <HomeOutlined className="text-blue-500 text-xl" />
                    <span className="font-medium">Delivery Address</span>
                  </Text>
                  <Text
                    className={`text-gray-800 font-medium break-all max-w-[50%] ${isDeliveryAddressChange && showTable && 'text-red-500 font-semibold'}`}
                  >
                    {showVersionDetails?.data?.deliveryAddress || 'NA'}
                  </Text>
                </div>

                <div className="flex items-center justify-between border-b pb-2">
                  <Text className="flex items-center gap-3 text-gray-600">
                    <CalendarOutlined className="text-orange-500 text-xl" />
                    <span className="font-medium">Delivery Date</span>
                  </Text>
                  <Text
                    className={`text-gray-800 font-medium ${isDeliveryDatChange && showTable && 'text-red-500 font-semibold'}`}
                  >
                    {showVersionDetails?.data?.deliveryDate
                      ? new Date(
                          showVersionDetails?.data?.deliveryDate
                        ).toLocaleDateString('en-in')
                      : 'NA'}
                  </Text>
                </div>

                <div className="flex items-center justify-between border-b pb-2">
                  <Text className="flex items-center gap-3 text-gray-600">
                    <HomeOutlined className="text-blue-500 text-xl" />
                    <span className="font-medium">Delivery Address</span>
                  </Text>
                  <Text
                    copyable
                    className={`text-gray-800 font-medium break-all max-w-[50%] ${isBillingAddressChange && showTable && 'text-red-500 font-semibold'}`}
                  >
                    {showVersionDetails?.data?.billingAddress || 'NA'}
                  </Text>
                </div>

                <div className="flex items-center justify-between border-b pb-2">
                  <Text className="flex items-center gap-3 text-gray-600">
                    <CreditCardOutlined className="text-pink-500 text-xl" />
                    <span className="font-medium">Payment Term</span>
                  </Text>
                  <Text
                    className={`text-gray-800 font-medium ${isPaymentTermChange && showTable && 'text-red-500 font-semibold'}`}
                  >
                    {showVersionDetails?.data?.paymentTerm}
                  </Text>
                </div>

                <div className="flex items-center justify-between border-b pb-2">
                  <Text className="flex items-center gap-3 text-gray-600">
                    <IdcardOutlined className="text-purple-500 text-xl" />
                    <span className="font-medium">Vendor Id</span>
                  </Text>
                  <Text
                    className={`text-gray-800 font-medium ${isVendorChange && showTable && 'text-red-500 font-semibold'}`}
                  >
                    {vendorData?.[0]?.id}
                  </Text>
                </div>

                <div className="flex items-center justify-between border-b pb-2">
                  <Text className="flex items-center gap-3 text-gray-600">
                    <DatabaseOutlined className="text-blue-500 text-xl" />
                    <span className="font-medium">Vendor Name</span>
                  </Text>
                  <Text
                    className={`text-gray-800 font-medium ${isVendorChange && showTable && 'text-red-500 font-semibold'}`}
                  >
                    {vendorData?.[0]?.name}
                  </Text>
                </div>

                <div className="flex items-center justify-between border-b pb-2">
                  <Text className="flex items-center gap-3 text-gray-600">
                    <HomeOutlined className="text-blue-500 text-xl" />
                    <span className="font-medium"> Vendor Address</span>
                  </Text>
                  <Text
                    className={`text-gray-800 font-medium break-all max-w-[50%] ${isVendorChange && showTable && 'text-red-500 font-semibold'}`}
                  >
                    {/* using this because now user can not choice to select address  */}
                    {vendorData[0]?.address?.[0]}
                  </Text>
                </div>

                <div className="flex items-center justify-between border-b pb-2">
                  <Text className="flex items-center gap-3 text-gray-600">
                    <ContactsOutlined className="text-blue-500 text-xl" />
                    <span className="font-medium"> Vendor Contact</span>
                  </Text>
                  <Text
                    className={`text-gray-800 font-medium break-all max-w-[50%] ${isVendorChange && showTable && 'text-red-500 font-semibold'}`}
                  >
                    {/* using this because now user can not choice to select contact  */}
                    {vendorData?.[0]?.contact?.[0]}
                  </Text>
                </div>

                <div className="flex items-center justify-between border-b pb-2">
                  <Text className="flex items-center gap-3 text-gray-600">
                    <MailOutlined className="text-amber-950 text-xl" />
                    <span className="font-medium"> Vendor Email</span>
                  </Text>
                  <Text
                    className={`text-gray-800 font-medium break-all max-w-[50%] ${isVendorChange && showTable && 'text-red-500 font-semibold'}`}
                  >
                    {/* using this because now user can not choice to select email  */}
                    {vendorData?.[0]?.email?.[0]}
                  </Text>
                </div>

                <div className="flex items-center justify-between border-b pb-2">
                  <Text className="flex items-center gap-3 text-gray-600">
                    <FieldNumberOutlined className="text-orange-400 text-xl" />
                    <span className="font-medium"> Vendor GSTIN</span>
                  </Text>
                  <Text
                    copyable
                    className={`text-gray-800 font-medium break-all max-w-[50%] ${isVendorChange && showTable && 'text-red-500 font-semibold'}`}
                  >
                    {/* using this because now user can not choice to select email  */}
                    {vendorData?.[0]?.gstin}
                  </Text>
                </div>
              </div>
            </section>

            {/* Template data */}
            <section className="mt-2">
              <div className="mt-4">
                <div className="flex items-center gap-1 mb-2">
                  <Title level={5} className="text-lg text-gray-600">
                    Template Details
                  </Title>
                </div>
                <div className="mt-2">
                  <div className="flex items-center mt-2">
                    <p className="text-sm text-gray-500 min-w-[15rem]">
                      Template Name
                    </p>
                    <p className="text-gray-600 text-sm">
                      {TemplateInfo?.name}
                    </p>
                  </div>
                  <ShowTemplateValues
                    template={TemplateInfo?.templateData}
                    showTitle={false}
                  />
                </div>
              </div>
            </section>

            {/* Product Details */}

            <SidebarProductDetailsTable
              productDetail={showVersionDetails?.data?.items}
              showVersionDetails={showVersionDetails}
              historyData={historyData}
              compareTwoObj={compareTwoObj}
              customColumns={
                showVersionDetails?.data?.items[0]?.item?.customColumns
              }
              type={'purchaseOrder'}
              showtable={showTable}
            />

            <FinancialDetails
              subTotal={sTotal}
              totalTaxAmount={txAmount}
              totalAmountAfterTax={tAmount}
              cgst={calcAmount.cgst}
              sgst={calcAmount.sgst}
              igst={calcAmount.igst}
              showCgst={!showVersionDetails?.data?.showIgst}
              showSgst={!showVersionDetails?.data?.showIgst}
              showIgst={showVersionDetails?.data?.showIgst}
            />

            {showVersionDetails?.data?.terms?.length > 0 && (
              <section className="mt-2">
                <Title level={5} className="text-gray-600">
                  Terms & Conditions
                </Title>
                <Collapse
                  className="mt-2"
                  expandIconPosition="right"
                  defaultActiveKey={showVersionDetails?.data?.terms?.map(
                    (term, idx) => idx
                  )}
                >
                  {showVersionDetails?.data?.terms
                    ?.filter?.((elem) => elem)
                    ?.map((term, idx) => {
                      const currentTermChange = addedTermsAndCond?.[idx] || {};
                      const isChanged =
                        Object.keys(currentTermChange).length > 0 && showTable;

                      return (
                        <Panel
                          header={
                            <Text className="text-gray-600 font-medium">
                              {term?.value?.terms ||
                                term?.terms ||
                                'Untitled Term'}
                            </Text>
                          }
                          key={idx}
                          className={`${isChanged ? 'bg-red-50' : ''} rounded-lg`}
                        >
                          <div>
                            <Title level={5} className="!text-gray-500 !mb-2">
                              Description
                            </Title>
                            <div
                              className="!text-gray-600 break-words"
                              dangerouslySetInnerHTML={{
                                __html: getDecodedHTML(
                                  term?.value?.description ||
                                    term?.description ||
                                    '-'
                                ),
                              }}
                            ></div>
                          </div>
                        </Panel>
                      );
                    })}
                </Collapse>
              </section>
            )}
            <div className="mt-2">
              <Title level={5} className="text-gray-600">
                Additional Comments
              </Title>
              <Card
                bordered={false}
                className={`mt-2 ${isCommentsChange && showTable && '!bg-red-100'} bg-gray-50 py-2 text-gray-600 ${
                  fromKanban ? 'w-[46.5rem]' : 'w-full'
                }`}
              >
                <Paragraph className="mb-0 break-words">
                  {showVersionDetails?.data?.comments ||
                    'No comments provided.'}
                </Paragraph>
              </Card>
            </div>

            {/* Attachments */}
            {showVersionDetails?.data?.files.length > 0 && (
              <div>
                <MediaDetails
                  files={showVersionDetails?.data?.files}
                  setOpenSideBar={setShowSidebar}
                  setReadMore={setReadMore}
                />
              </div>
            )}

            <div className="buttons flex items-center gap-4 flex-wrap mt-4">
              {isEditDeleteAllowed() ? (
                <>
                  {!defaults?.defaultParam?.projectDefaults?.disabledApprovalFor.includes(
                    'purchaseOrder'
                  ) && (
                    <Button
                      disabled={!hasApprovalAccess}
                      className="!bg-green-500 !px-3 !py-2 !text-[14px] !rounded-md text-white"
                      onClick={() => {
                        handleApprovePO(selectedPo._id);
                      }}
                    >
                      Approve
                    </Button>
                  )}
                  {!defaults?.defaultParam?.projectDefaults?.disabledApprovalFor.includes(
                    'purchaseOrder'
                  ) && (
                    <Button
                      disabled={!hasApprovalAccess}
                      className="!bg-red-500 !px-3 !py-2 !text-[14px] !rounded-md text-white"
                      onClick={() => {
                        setOpenStatusModal(true);
                      }}
                    >
                      Reject
                    </Button>
                  )}
                  {defaults?.defaultParam?.projectDefaults?.disabledApprovalFor.includes(
                    'purchaseOrder'
                  ) && (
                    <Button
                      className="!bg-blue-500 !px-3 !py-2 !text-[14px] !rounded-md text-white"
                      onClick={() => {
                        setShowEmailModal(true);
                        setShowSidebar(false);
                        setShowGrnSidebar(false);
                      }}
                    >
                      Send Mail
                    </Button>
                  )}
                </>
              ) : (
                <>
                  <Button
                    className="!bg-blue-500 !px-3 !py-2 !text-[14px] !rounded-md text-white"
                    onClick={() => {
                      setShowEmailModal(true);
                      setShowSidebar(false);
                      setShowGrnSidebar(false);
                    }}
                  >
                    Send Mail
                  </Button>
                </>
              )}
            </div>
          </section>
        )}
      </div>

      <Modal
        open={openStatusModal}
        onCancel={() => setOpenStatusModal(false)}
        onOk={handleUpdatePurchaseOrderStatus}
        okText="Update"
      >
        <div className="bg-gray-50 p-4 rounded-lg">
          <h3 className="text-sm font-medium text-gray-700 mb-3">
            Rejection Reason (Optional)
          </h3>
          <Textarea
            value={rejectionReason}
            onChange={(e) => setRejectionReason(e.target.value)}
            placeholder="Enter reason for rejection..."
            rows={4}
            className="w-full"
          />
        </div>
      </Modal>
    </>
  );
}

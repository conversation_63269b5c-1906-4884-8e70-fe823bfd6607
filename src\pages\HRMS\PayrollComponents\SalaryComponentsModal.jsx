import { useState } from 'react';
import { useHrmsContext } from '../utils/HrmsContext';

import { Button, Input, Modal, Table } from 'antd';

import { TiDelete } from 'react-icons/ti';

import { toast } from 'react-toastify';

import { customConfirm } from '../../../utils/customConfirm';

const SalaryComponentsModal = ({
  openModal,
  setOpenModal,
  startDate,
  endDate,
  salaryComponents,
  holidays,
}) => {
  const user = JSON.parse(localStorage.getItem('user'))?.user;

  const [componentsData, setComponentsData] = useState([]);
  const {
    setUserDataDuringPayrollRun,
    setPayrollRunData,
    calculatePayrollStepThreeSalary,
  } = useHrmsContext();

  const closeModal = async () => {
    const confirm = await customConfirm(
      'All unsaved progress will be lost. Are you sure you want to close this window?',
      'Delete'
    );
    if (!confirm) return;
    setComponentsData([]);
    setOpenModal(false);
  };

  function generateUniqueId() {
    const timestamp = Date.now().toString().slice(-6); // Last 6 digits of timestamp
    const random = Math.floor(1000 + Math.random() * 9000); // 4 random digits
    return timestamp + random; // 6 + 4 = 10 digits
  }

  const changeHandler = (name, value, index) => {
    let pay = calculatePayrollStepThreeSalary(
      salaryComponents?.userId,
      startDate,
      endDate,
      holidays
    );
    if (name === 'percentage') {
      let amt = (value / 100) * pay;
      setComponentsData((prev) =>
        prev?.map((elem) => {
          if (elem?.uid !== index) return elem;
          else {
            return {
              ...elem,
              percentage: value,
              amount: amt.toFixed(2),
            };
          }
        })
      );
    } else if (name === 'amount') {
      let percentage = (value / pay) * 100;
      setComponentsData((prev) =>
        prev?.map((elem) => {
          if (elem?.uid !== index) return elem;
          else {
            return {
              ...elem,
              percentage: percentage.toFixed(2),
              amount: value,
            };
          }
        })
      );
    } else {
      setComponentsData((prev) =>
        prev?.map((elem) => {
          if (elem?.uid !== index) return elem;
          else {
            return {
              ...elem,
              [name]: value,
            };
          }
        })
      );
    }
  };

  const handleSubmit = async () => {
    const confirm = await customConfirm(
      'Submit the current information?',
      'Success'
    );
    if (!confirm) return;
    setPayrollRunData((prev) =>
      prev?.map((elem) => {
        if (elem?._id === salaryComponents?.userId) {
          return {
            ...elem,
            salaryComponents: componentsData,
          };
        } else return elem;
      })
    );
    setUserDataDuringPayrollRun((prev) => ({
      ...(prev || {}),
      [salaryComponents?.userId]: {
        ...(prev?.[salaryComponents?.userId] || {}),
        salaryComponents: componentsData,
      },
    }));
    toast.success('Salary Components Information Updated');
    setComponentsData([]);
    setOpenModal(false);
  };

  const columns = [
    {
      title: 'Component Name',
      key: 'componentName',
      render: (_, record) => (
        <Input
          placeholder="Enter Name"
          className="w-full"
          name="name"
          value={record?.name}
          onChange={(e) =>
            changeHandler(e.target.name, e.target.value, record?.uid)
          }
        />
      ),
    },
    {
      title: 'Percentage',
      key: 'percentage',
      render: (_, record) => (
        <Input
          placeholder="Enter Percentage"
          className="w-full"
          name="percentage"
          value={record?.percentage}
          onChange={(e) =>
            changeHandler(e.target.name, e.target.value, record?.uid)
          }
        />
      ),
    },
    {
      title: 'Amount',
      key: 'amount',
      render: (_, record) => (
        <Input
          placeholder="Enter Amount"
          className="w-full"
          name="amount"
          value={record?.amount}
          onChange={(e) =>
            changeHandler(e.target.name, e.target.value, record?.uid)
          }
        />
      ),
    },
    {
      title: 'Date',
      key: 'date',
      render: (_, record) => (
        <Input
          placeholder="Enter reimbursement Date"
          type="date"
          className="w-full"
          name="date"
          value={record?.date}
          min={new Date(startDate).toISOString().split('T')[0]}
          max={new Date(endDate).toISOString().split('T')[0]}
          onChange={(e) =>
            changeHandler(e.target.name, e.target.value, record?.uid)
          }
        />
      ),
    },
    {
      title: '',
      key: 'actions',
      render: (_, record) => (
        <TiDelete
          className="text-2xl text-red-500 ml-auto cursor-pointer"
          onClick={() =>
            setComponentsData((prev) =>
              prev?.filter((elem) => elem?.uid !== record?.uid)
            )
          }
        />
      ),
    },
  ];

  return (
    <Modal
      title="Add Salary Components"
      open={openModal}
      onOk={handleSubmit}
      onCancel={closeModal}
      width={900}
      footer={[
        <Button key="back" onClick={closeModal}>
          Cancel
        </Button>,
        <Button
          key="add"
          onClick={() => {
            setComponentsData((prev) => [
              ...prev,
              {
                amount: '',
                date: '',
                uid: generateUniqueId(),
                name: '',
                user: user?._id,
              },
            ]);
          }}
          className="bg-green-500 text-white"
        >
          Add
        </Button>,
        <Button
          key="submit"
          type="primary"
          // loading={loading}
          onClick={handleSubmit}
        >
          Submit
        </Button>,
      ]}
    >
      <div className="overflow-y-scroll h-[37vh]">
        <Table
          columns={columns}
          dataSource={componentsData}
          rowKey={(_, index) => index}
          pagination={false}
          size="middle"
          scroll={{ x: true }}
          locale={{ emptyText: 'No Data added yet' }}
        />
      </div>
    </Modal>
  );
};

export default SalaryComponentsModal;

import { CiMail, CiViewColumn } from 'react-icons/ci';
import {
  FaArrowDown,
  FaArrowUp,
  FaBalanceScale,
  FaBell,
  FaBorderAll,
  FaBoxOpen,
  FaBuilding,
  FaCalendarAlt,
  FaChartBar,
  FaChartPie,
  FaClipboardCheck,
  FaClipboardList,
  FaCog,
  FaCubes,
  FaDesktop,
  FaEnvelope,
  FaExchangeAlt,
  FaExclamationTriangle,
  FaFileAlt,
  FaFileContract,
  FaFileInvoice,
  FaFileInvoiceDollar,
  FaFunnelDollar,
  FaHandshake,
  FaHistory,
  FaIndustry,
  FaMailBulk,
  FaNetworkWired,
  FaProjectDiagram,
  FaReceipt,
  FaRedo,
  FaSatelliteDish,
  FaSearch,
  FaShoppingCart,
  FaSitemap,
  FaStopwatch,
  FaTasks,
  FaTicketAlt,
  FaTools,
  FaTruck,
  FaUser,
  FaUserCog,
  FaUserFriends,
  FaUsers,
  FaUserShield,
  FaUserTie,
  FaWarehouse,
  FaWpforms,
} from 'react-icons/fa';
import { GiBassetHoundHead } from 'react-icons/gi';
import {
  GrDocumentConfig,
  GrDocumentPerformance,
  GrDocumentTime,
} from 'react-icons/gr';
import { HiOutlineBuildingOffice2, HiRectangleStack } from 'react-icons/hi2';
import { IoTicketOutline } from 'react-icons/io5';
import {
  MdAutorenew,
  MdDashboardCustomize,
  MdDevicesOther,
  MdGridGoldenratio,
  MdOutlineCoPresent,
  MdOutlineHolidayVillage,
  MdOutlinePaid,
  MdOutlineViewKanban,
} from 'react-icons/md';
import { PiUsersThreeBold } from 'react-icons/pi';
import { RxDashboard } from 'react-icons/rx';
import { SiCivicrm } from 'react-icons/si';
import { SlCalender } from 'react-icons/sl';
import { TbReportAnalytics, TbReportSearch } from 'react-icons/tb';
const AllIcons = {
  highLights: FaBell,
  purchaseManagement: FaShoppingCart,
  orderSummary: FaBorderAll,
  Subscriptions: MdOutlinePaid,
  crm: SiCivicrm,
  companyProfile: HiOutlineBuildingOffice2,
  assets: GiBassetHoundHead,
  PO: FaFileInvoiceDollar,
  IndentDashBoard: MdGridGoldenratio,
  dispatchDashboard: FaTruck,
  primary: FaClipboardList,
  analytics: FaChartBar,
  attendance: FaCalendarAlt,
  jobs: FaTasks,
  dashboard: RxDashboard,
  departments: FaUserFriends,
  inventory: FaWarehouse,
  reports: TbReportSearch,
  salesOrderManagement: FaFileAlt,
  dispatchManagement: FaTruck,
  configuration: GrDocumentConfig,
  create: FaClipboardList,
  myOrganization: FaBuilding,
  setUp: FaCog,
  setUpInventory: FaWarehouse,
  Actions: FaTools,
  ActivityLogs: FaClipboardList,
  Admin: FaUserCog,
  aican: HiRectangleStack,
  RectangleStackIcon: HiRectangleStack,
  Assembly: FaIndustry,
  AttendanceDashboard: FaCalendarAlt,
  Calendar: SlCalender,
  Column: CiViewColumn,
  CreateJobs: FaTasks,
  CustomReporter: FaFileAlt,
  Defaults: FaCog,
  Devices: MdDevicesOther,
  Employee: FaUsers,
  ErrorCodes: FaExclamationTriangle,
  Forms: FaWpforms,
  JobPerformance: GrDocumentPerformance,
  JobPlanner: FaCalendarAlt,
  SetUpAnalytics: FaChartPie,
  StockIn: FaBoxOpen,
  StockOut: FaTruck,
  WorkOrder: FaClipboardCheck,
  Worker: FaUserCog,
  WorkOrderReport: TbReportAnalytics,
  JobTemplate: FaClipboardList,
  Kanban: MdOutlineViewKanban,
  Machines: FaIndustry,
  Mails: CiMail,
  ManagementDepartment: FaSitemap,
  ManageJobs: FaTasks,
  Master: FaCog,
  OrderForms: FaWpforms,
  Process: FaIndustry,
  SatelliteView: FaSatelliteDish,
  Production: FaIndustry,
  ProductionLine: FaIndustry,
  ProductionFlow: FaIndustry,
  Products: FaCubes,
  RFQ: FaFileInvoiceDollar,
  RealTime: GrDocumentTime,
  Quotation: FaFileInvoiceDollar,
  SalesOrder: FaFileInvoiceDollar,
  maintainanceManagement: FaTools,
  renewalsmanagement: MdAutorenew,
  ticketsManagement: IoTicketOutline,
  hrms: PiUsersThreeBold,
  leave: MdOutlineHolidayVillage,
  attendanceManagement: MdOutlineCoPresent,
  salesDashboard: MdDashboardCustomize,
  subscriptions: FaUser,
  admin: FaUserShield,
  errorcodes: FaExclamationTriangle,
  activityLogs: FaHistory,
  defaults: FaCog,
  orderForms: FaFileContract,
  customer: FaHandshake,
  mails: FaEnvelope,
  managementDepartment: FaUsers,
  column: CiViewColumn,
  process: FaProjectDiagram,
  calendar: FaCalendarAlt,
  devices: FaDesktop,
  machines: FaIndustry,
  productionFlow: FaProjectDiagram,
  jobTemplate: FaFileAlt,
  forms: FaWpforms,
  emailTemplate: FaMailBulk,
  production: FaIndustry,
  worker: FaUserTie,
  downtime: FaStopwatch,
  satelliteView: FaSatelliteDish,
  productionLine: FaIndustry,
  jobPlanner: FaClipboardList,
  assembly: FaCubes,
  workOrder: FaClipboardCheck,
  createJobs: FaTasks,
  manageJobs: FaTasks,
  controlunit: FaNetworkWired,
  customReporter: FaFileAlt,
  stockIn: FaArrowDown,
  stockOut: FaArrowUp,
  realTime: FaChartPie,
  internalStockTransfer: FaExchangeAlt,
  transactions: FaReceipt,
  master: FaCog,
  products: FaBoxOpen,
  rfq: FaSearch,
  quotation: FaFileInvoice,
  salesOrder: FaShoppingCart,
  attendanceDashboard: FaChartBar,
  indentdashboard: FaClipboardList,
  po: FaFileInvoiceDollar,
  leads: FaFunnelDollar,
  pipeline: FaSitemap,
  renewals: FaRedo,
  tickets: FaTicketAlt,
  accountManagement: FaUserTie,
  masterAccount: FaUserShield,
  invoice: FaFileInvoice,
  bills: FaReceipt,
  voucher: FaFileContract,
  journal: FaClipboardList,
  ledger: FaFileAlt,
  trialbalance: FaBalanceScale,
  financialreport: FaFileAlt,
  purchaseplanning: SlCalender,
};

export default AllIcons;

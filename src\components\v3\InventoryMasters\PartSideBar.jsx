import {
  AppstoreOutlined,
  BranchesOutlined,
  CopyOutlined,
  DownloadOutlined,
  EditOutlined,
  EyeOutlined,
  FileImageOutlined,
  InfoCircleOutlined,
  ShopOutlined,
  TagsOutlined,
  TeamOutlined,
} from '@ant-design/icons';
import {
  Button,
  Empty,
  Input,
  Modal,
  Spin,
  Table,
  Tag,
  Tooltip,
  Typography,
} from 'antd';
import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import { downloadMedia, getLocalDateTime } from '../../../helperFunction';
import { useLazyGetFormByIdQuery } from '../../../slices/createFormapiSlice';
import { useLazyGetMediaByIdQuery } from '../../../slices/mediaSlice';
import { useCopyPartMutation } from '../../../slices/partApiSlice';
import { customConfirm } from '../../../utils/customConfirm';
import Carousel from '../../global/components/Carousel';
import RightSidebar from '../../global/components/RightSidebar';
import PreviewImgPdfFullscreen from '../../salesOrder/PreviewImgPdfFullscreen';
import PartVariantsSideBar from './PartVariantsSideBar';
import { ViewInspectionFormPreview } from './view-inspection-form-preview';

const { Title } = Typography;

const PartSideBar = ({
  rightBarData,
  openSideBar,
  setOpenSideBar,
  defaults,
}) => {
  const [allMediaFiles, setAllMediaFiles] = useState([]);
  const [allThumbNails, setAllThumbNails] = useState([]);
  const [isMediaLoading, setIsMediaLoading] = useState(false);
  const navigate = useNavigate();
  const [mediaToPreview, setMediaToPreview] = useState(null);
  const [previewMedia, setPreviewMedia] = useState(false);
  const [selectedInspectionForm, setSelectedInspectionForm] = useState(null);
  const [newNameModal, setNewNameModal] = useState(false);
  const [copyPart] = useCopyPartMutation();
  const [newName, setNewName] = useState('');

  const [getMediaById] = useLazyGetMediaByIdQuery();
  const [getFormById] = useLazyGetFormByIdQuery();

  // Fetch media files
  useEffect(() => {
    if (rightBarData?.media?.length > 0) {
      (async () => {
        setIsMediaLoading(true);
        setAllMediaFiles([]);

        try {
          for (let i = 0; i < rightBarData?.media?.length; i++) {
            const el = rightBarData?.media[i];
            const response = await getMediaById({ id: el }).unwrap();
            setAllMediaFiles((prev) => [...prev, response?.media]);
          }
        } catch (error) {
          toast.error('Failed to load media files');
        } finally {
          setIsMediaLoading(false);
        }
      })();
    }
  }, [rightBarData?.media, getMediaById]);

  // Set thumbnails
  useEffect(() => {
    if (rightBarData?.thumbNails?.length > 0) {
      setAllThumbNails(rightBarData?.thumbNails);
    } else {
      setAllThumbNails([]);
    }
  }, [rightBarData?.thumbNails, rightBarData]);

  // Function to view inspection form
  const handleViewInspectionForm = async () => {
    let formId = rightBarData?.inspectionForm || rightBarData?.createForm;

    if (formId) {
      try {
        const form = await getFormById({ id: formId }).unwrap();
        if (form) {
          setSelectedInspectionForm(form);
        } else {
          toast.error('Form not found');
        }
      } catch (error) {
        toast.error('Error loading inspection form');
      }
    } else {
      toast.info('No inspection form available');
    }
  };

  const renderThreshold = (text, record) => {
    if (record.property === 'Quantity Threshold') {
      const othersThreshold =
        rightBarData?.othersThreshold?.map((threshold) => (
          <Tag color={threshold.color} key={threshold.tag} className="px-3">
            <span className="font-semibold">{threshold.value}</span>
            <span className="mx-1">•</span>
            <span>{threshold.tag}</span>
          </Tag>
        )) || [];

      return (
        <div className="flex flex-wrap items-center gap-2">
          <Tag color="blue" className="px-3">
            {rightBarData?.quantityThreshold}
          </Tag>
          {othersThreshold.length > 0 && othersThreshold}
        </div>
      );
    }

    if (record.property === 'Inspection Form') {
      return (
        <Button
          type="primary"
          size="small"
          onClick={handleViewInspectionForm}
          icon={<EyeOutlined />}
        >
          View
        </Button>
      );
    }

    return text || '-';
  };

  // Part details table columns
  const partDetailsColumns = [
    {
      title: 'Property',
      dataIndex: 'property',
      key: 'property',
      width: '40%',
    },
    {
      title: 'Value',
      dataIndex: 'value',
      key: 'value',
      width: '60%',
      render: renderThreshold,
    },
  ];

  // Part details data
  const partDetailsData = [
    { key: '1', property: 'Part Id', value: rightBarData?.id },
    { key: '2', property: 'Part Name', value: rightBarData?.name },
    { key: '3', property: 'HSN/SAC Code', value: rightBarData?.hsn_sacCode },
    {
      key: '4',
      property: 'Date',
      value: getLocalDateTime(rightBarData?.createdAt),
    },
    { key: '5', property: 'Description', value: rightBarData?.description },
    { key: '6', property: 'UOM', value: rightBarData?.uom },
    {
      key: '7',
      property: 'Quantity Threshold',
      value: rightBarData?.quantityThreshold,
    },
    { key: '8', property: 'Valuation', value: rightBarData?.valuation },
    { key: '9', property: 'Category', value: rightBarData?.category?.name },
    { key: '10', property: 'Inspection Form', value: null },
  ];

  // Raw materials columns
  const rawMaterialsColumns = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: 'Quantity',
      dataIndex: 'quantityRequired',
      key: 'quantityRequired',
    },
  ];

  // Additional UOMs columns
  const additionalUomsColumns = [
    {
      title: '#',
      dataIndex: 'index',
      key: 'index',
      render: (_, __, index) => index + 1,
    },
    {
      title: 'Additional Unit',
      dataIndex: 'additionalUnit',
      key: 'additionalUnit',
      render: (text) => `1 ${text}`,
    },
    {
      title: 'Conversion Factor',
      dataIndex: 'conversionValue',
      key: 'conversionValue',
      render: (text, record) => `${text} ${record.conversionUnit}`,
    },
  ];

  // Stores columns
  const storesColumns = [
    {
      title: '#',
      dataIndex: 'index',
      key: 'index',
      render: (_, __, index) => index + 1,
    },
    {
      title: 'Store Name',
      dataIndex: 'name',
      key: 'name',
    },
  ];

  // Vendors columns
  const vendorsColumns = [
    {
      title: '#',
      dataIndex: 'index',
      key: 'index',
      render: (_, __, index) => index + 1,
    },
    {
      title: 'Vendor Name',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: 'Rate',
      dataIndex: 'rate',
      key: 'rate',
      render: (_, record, index) =>
        rightBarData?.vendor_details?.[index]?.rate || '-',
    },
    {
      title: 'Discount',
      dataIndex: 'discount',
      key: 'discount',
      render: (_, record, index) =>
        rightBarData?.vendor_details?.[index]?.discount || '-',
    },
  ];

  // Store Areas columns
  const storeAreasColumns = [
    {
      title: '#',
      dataIndex: 'index',
      key: 'index',
      render: (_, __, index) => index + 1,
    },
    {
      title: 'Area',
      dataIndex: 'value',
      key: 'value',
    },
  ];

  // Media columns
  const mediaColumns = [
    {
      title: 'File Name',
      dataIndex: 'name',
      key: 'name',
      ellipsis: {
        showTitle: false,
      },
      render: (name) => (
        <Tooltip title={name || 'Unknown'}>
          <span>
            {name && name.length > 30
              ? `${name.slice(0, 30)}...`
              : name || 'Unknown'}
          </span>
        </Tooltip>
      ),
    },
    {
      title: 'Actions',
      key: 'actions',
      align: 'right',
      render: (_, record) => (
        <div className="flex justify-end space-x-2">
          {record?.type &&
            (record.type.includes('image') || record.type.includes('pdf')) && (
              <Button
                type="primary"
                size="small"
                icon={<EyeOutlined />}
                onClick={() => {
                  setMediaToPreview(record);
                  setPreviewMedia(true);
                }}
              >
                Preview
              </Button>
            )}
          <Button
            type="default"
            size="small"
            icon={<DownloadOutlined />}
            onClick={() => downloadMedia(record)}
          >
            Download
          </Button>
        </div>
      ),
    },
  ];

  // Section titles with icons
  const SectionTitle = ({ icon, title, color }) => (
    <div className="flex items-center mb-3 mt-6">
      {React.cloneElement(icon, {
        style: { color, fontSize: '20px', marginRight: '8px' },
      })}
      <Title level={5} style={{ margin: 0 }}>
        {title}
      </Title>
    </div>
  );

  return (
    <div>
      <Modal
        title="Copy Part"
        open={newNameModal}
        onCancel={() => {
          setNewNameModal(false);
          setNewName('');
        }}
        footer={null}
        width={600}
        forceRender
      >
        <div>
          <label className="mb-1 font-semibold text-[#667085]">
            Enter the New Name of the Part
          </label>
          <Input
            placeholder="Enter new name"
            value={newName}
            onChange={(e) => {
              setNewName(e.target.value);
            }}
          />
          <div className="flex justify-end gap-2 mt-4">
            <Button
              onClick={() => {
                setNewNameModal(false);
              }}
            >
              Cancel
            </Button>
            <Button
              type="primary"
              onClick={async () => {
                if (!newName.trim()) {
                  toast.error('Enter Valid Name');
                  return;
                }
                const confirm = await customConfirm(
                  'Are you sure you want to Copy the part?',
                  'success'
                );
                if (!confirm) return;
                const createdPart = await copyPart({
                  id: rightBarData._id,
                  name: newName,
                });
                if (!createdPart?.error) {
                  toast.success('Part Copied Successfully');
                  setNewNameModal(false);
                  setNewName('');
                  navigate(
                    `/settings/inventory/masters/parts/${createdPart?.data?._id}`
                  );
                }
              }}
            >
              Save
            </Button>
          </div>
        </div>
      </Modal>
      <ViewInspectionFormPreview
        data={selectedInspectionForm}
        setSelectedInspectionForm={setSelectedInspectionForm}
      />

      {previewMedia && (
        <PreviewImgPdfFullscreen
          media={mediaToPreview}
          showPreview={previewMedia}
          setShowPreview={setPreviewMedia}
        />
      )}

      <RightSidebar
        openSideBar={openSideBar}
        setOpenSideBar={(val) => {
          setAllMediaFiles([]);
          setOpenSideBar(val);
        }}
        scale={736}
      >
        {isMediaLoading ? (
          <div className="flex justify-center items-center h-full">
            <Spin size="large" tip="Loading part information..." />
          </div>
        ) : (
          <div className="p-2 overflow-y-auto">
            <div className="flex justify-end gap-3 mb-1">
              <Tooltip title="Copy">
                <CopyOutlined
                  className="text-lg"
                  onClick={() => {
                    setNewNameModal(true);
                    setOpenSideBar(false);
                  }}
                />
              </Tooltip>
              <Tooltip title="Edit">
                <EditOutlined
                  className="text-lg"
                  onClick={() => {
                    setOpenSideBar(false);
                    navigate(`parts/${rightBarData?._id}`);
                  }}
                />
              </Tooltip>
            </div>

            {allThumbNails?.length > 0 && (
              <div className="mb-6">
                <SectionTitle
                  icon={<FileImageOutlined />}
                  title="Part Images"
                  color="#1890ff"
                />
                <Carousel
                  images={allThumbNails?.map((thumbNail) => thumbNail?.data)}
                />
              </div>
            )}

            {/* Part Details Section */}
            <div className="mb-6">
              <SectionTitle
                icon={<InfoCircleOutlined />}
                title="Part Details"
                color="#52c41a"
              />
              <Table
                columns={partDetailsColumns}
                dataSource={partDetailsData}
                pagination={false}
                bordered
                size="small"
              />
            </div>

            {/* Raw Materials Section */}
            {rightBarData?.rawMaterials?.length > 0 && (
              <div className="mb-6">
                <SectionTitle
                  icon={<TagsOutlined />}
                  title="Raw Materials"
                  color="#faad14"
                />
                <Table
                  columns={rawMaterialsColumns}
                  dataSource={rightBarData?.rawMaterials}
                  pagination={false}
                  rowKey={(record, index) => `raw-material-${index}`}
                  bordered
                  size="small"
                />
              </div>
            )}

            {/* Additional UOMs Section */}
            <div className="mb-6">
              <SectionTitle
                icon={<AppstoreOutlined />}
                title="Additional UOMs"
                color="#722ed1"
              />
              <Table
                columns={additionalUomsColumns}
                dataSource={rightBarData?.additionalUoms}
                pagination={false}
                rowKey={(record, index) => `additional-uom-${index}`}
                bordered
                size="small"
              />
            </div>

            {/* Stores Section */}
            {defaults?.defaultParam?.projectDefaults?.enablePartStoreEdit && (
              <div className="mb-6">
                <SectionTitle
                  icon={<ShopOutlined />}
                  title="Stores"
                  color="#eb2f96"
                />
                <Table
                  columns={storesColumns}
                  dataSource={rightBarData?.stores}
                  pagination={false}
                  rowKey={(record, index) => `store-${index}`}
                  bordered
                  size="small"
                />
              </div>
            )}

            {/* Stores section (duplicated as in original code) */}
            <div className="mb-6">
              <SectionTitle
                icon={<ShopOutlined />}
                title="Stores"
                color="#13c2c2"
              />
              <Table
                columns={storesColumns}
                dataSource={rightBarData?.stores}
                pagination={false}
                rowKey={(record, index) => `store-general-${index}`}
                bordered
                size="small"
              />
            </div>

            {/* Vendors Section */}
            <div className="mb-6">
              <SectionTitle
                icon={<TeamOutlined />}
                title="Vendors"
                color="#fa541c"
              />
              <Table
                columns={vendorsColumns}
                dataSource={rightBarData?.vendors}
                pagination={false}
                rowKey={(record, index) => `vendor-${index}`}
                bordered
                size="small"
              />
            </div>

            {/* Store Areas Section */}
            <div className="mb-6">
              <SectionTitle
                icon={<ShopOutlined />}
                title="Store Areas"
                color="#2f54eb"
              />
              <Table
                columns={storeAreasColumns}
                dataSource={rightBarData?.storeArea}
                pagination={false}
                rowKey={(record, index) => `store-area-${index}`}
                bordered
                size="small"
              />
            </div>

            {/* Media Section */}
            <div className="mb-6">
              <SectionTitle
                icon={<FileImageOutlined />}
                title="Media"
                color="#f5222d"
              />
              {allMediaFiles.length > 0 ? (
                <Table
                  columns={mediaColumns}
                  dataSource={allMediaFiles}
                  pagination={false}
                  rowKey={(record, index) => `media-${index}`}
                  bordered
                  size="small"
                />
              ) : (
                <Empty description="No media files" />
              )}
            </div>

            {/* Variants Section */}
            <div className="mb-6">
              <SectionTitle
                icon={<BranchesOutlined />}
                title="Variants"
                color="#a0d911"
              />
              {rightBarData?.children?.length > 0 ? (
                <PartVariantsSideBar sideBarData={rightBarData} />
              ) : (
                <Empty description="No variants found" />
              )}
            </div>
          </div>
        )}
      </RightSidebar>
    </div>
  );
};

export default PartSideBar;

import { Button, Modal, Table, Tag } from 'antd';
import { useEffect, useState } from 'react';
import { FaEye } from 'react-icons/fa';
import { LuFiles } from 'react-icons/lu';
import { TiDelete } from 'react-icons/ti';
import { toast } from 'react-toastify';
import UploadButton from '../../../components/UploadButton';
import Input from '../../../components/global/components/Input';
import MediaViewer from '../../../components/global/components/MediaViewer';
import { useLazyGetMediaByIdQuery } from '../../../slices/mediaSlice';
import {
  useEditHrmsUserPayrollMutation,
  useLazyGetUserByIdQuery,
} from '../../../slices/userApiSlice';
import { customConfirm } from '../../../utils/customConfirm';
import { useHrmsContext } from '../utils/HrmsContext';

const UserPayrollModal = ({
  openModal,
  setOpenModal,
  payrollInfo,
  isRunningPayroll = false,
  startDate = '',
  endDate = '',
  payrollStep = -1,
}) => {
  const [tab, setTab] = useState(isRunningPayroll && payrollStep === 2 ? 1 : 0);
  const [getUserById] = useLazyGetUserByIdQuery();
  const [bonusData, setBonusData] = useState(null);
  const [deductionData, setDeductionData] = useState(null);
  const [advanceData, setAdvanceData] = useState(null);
  const [reimbursements, setReimbursements] = useState(null);
  const [adHokData, setAdHokData] = useState(null);
  const [adHocMedia, setAdHocMedia] = useState([]);
  const [showAdHocMedia, setShowAdHocMedia] = useState(false);
  const [selectedMedia, setSelectedMedia] = useState({ data: '', type: '' });
  const [selectedAdHoc, setSelectedAdHoc] = useState(-111);
  const [getMediaById] = useLazyGetMediaByIdQuery();
  const [submitLoading, setSubmitLoading] = useState(false);

  const [bonusPageSize, setBonusPageSize] = useState(5);
  const [deductionPageSize, setDeductionPageSize] = useState(5);
  const [advancePageSize, setAdvancePageSize] = useState(5);
  const [adHocPageSize, setAdHocPageSize] = useState(5);
  const [reimbursementsPageSize, setReimbursementsPageSize] = useState(5);

  const [isBonusThisMonth, setIsBonusThisMonth] = useState(false);
  const [isDeductionThisMonth, setIsDeductionThisMonth] = useState(false);
  const [isAdvanceThisMonth, setIsAdvanceThisMonth] = useState(false);
  const [isAdHocThisMonth, setIsAdHocThisMonth] = useState(false);
  const [isReimbursementsThisMonth, setIsReimbursementsThisMonth] =
    useState(false);

  const [editUser] = useEditHrmsUserPayrollMutation();

  const user = JSON.parse(localStorage.getItem('user'))?.user;

  const {
    setUserDataDuringPayrollRun,
    setPayrollRunData,
    userDataDuringPayrollRun,
  } = useHrmsContext();

  const handleBonusPaginationSizeChange = (_, pageSize) => {
    setBonusPageSize(pageSize);
  };

  const handleDeductionPaginationSizeChange = (_, pageSize) => {
    setDeductionPageSize(pageSize);
  };

  const handleAdvancePaginationSizeChange = (_, pageSize) => {
    setAdvancePageSize(pageSize);
  };

  const handleAdHocPaginationSizeChange = (_, pageSize) => {
    setAdHocPageSize(pageSize);
  };

  const handleReimbursementPaginationSizeChange = (_, pageSize) => {
    setReimbursementsPageSize(pageSize);
  };

  const closeModal = async () => {
    const confirm = await customConfirm(
      'All unsaved progress will be lost. Are you sure you want to close this window?',
      'Delete'
    );
    if (!confirm) return;
    setOpenModal(false);
    setBonusData(null);
    setDeductionData(null);
    setAdvanceData(null);
    setAdHokData(null);
  };

  const setPaymentsDataDuringPayrollRun = async (payrollInfo) => {
    let bonusTemp = [];
    let deductionTemp = [];
    let advanceTemp = [];
    let reimbursementTemp = [];
    let adHocTemp = [];
    if (payrollInfo?.bonusData?.length > 0) {
      for (let i of payrollInfo?.bonusData) {
        let user = await getUserById({
          id: i?.addedBy,
        });
        let lastEditedBy = '';
        if (i?.lastEditedBy) {
          lastEditedBy = await getUserById({
            id: i?.lastEditedBy,
          });
        }
        bonusTemp?.push({
          amount: i?.amount,
          date: new Date(i?.date)?.toISOString()?.split('T')?.[0],
          remark: i?.remark,
          addedAt: i?.addedAt,
          addedBy: i?.addedBy,
          payroll: i?.payroll,
          addedName: user?.data?.name,
          lastEditedBy: lastEditedBy?.data?.name,
          uid: i?.uid,
        });
      }
    }
    if (payrollInfo?.deductionData?.length > 0) {
      for (let i of payrollInfo?.deductionData) {
        let user = await getUserById({
          id: i?.addedBy,
        });
        let lastEditedBy = '';
        if (i?.lastEditedBy) {
          lastEditedBy = await getUserById({
            id: i?.lastEditedBy,
          });
        }
        deductionTemp?.push({
          amount: i?.amount,
          date: new Date(i?.date)?.toISOString()?.split('T')?.[0],
          remark: i?.remark,
          addedAt: i?.addedAt,
          addedBy: i?.addedBy,
          payroll: i?.payroll,
          addedName: user?.data?.name,
          lastEditedBy: lastEditedBy?.data?.name,
          uid: i?.uid,
        });
      }
    }
    if (payrollInfo?.advanceData?.length > 0) {
      for (let i of payrollInfo?.advanceData) {
        let user = await getUserById({
          id: i?.addedBy,
        });
        let lastEditedBy = '';
        if (i?.lastEditedBy) {
          lastEditedBy = await getUserById({
            id: i?.lastEditedBy,
          });
        }
        advanceTemp?.push({
          amount: i?.amount,
          date: new Date(i?.date)?.toISOString()?.split('T')?.[0],
          remark: i?.remark,
          addedAt: i?.addedAt,
          addedBy: i?.addedBy,
          payroll: i?.payroll,
          addedName: user?.data?.name,
          lastEditedBy: lastEditedBy?.data?.name,
          uid: i?.uid,
        });
      }
    }
    if (payrollInfo?.reimbursements?.length > 0) {
      for (let i of payrollInfo?.reimbursements) {
        let user = await getUserById({
          id: i?.addedBy,
        });
        let lastEditedBy = '';
        if (i?.lastEditedBy) {
          lastEditedBy = await getUserById({
            id: i?.lastEditedBy,
          });
        }
        reimbursementTemp?.push({
          amount: i?.amount,
          date: new Date(i?.date)?.toISOString()?.split('T')?.[0],
          remark: i?.remark,
          addedAt: i?.addedAt,
          addedBy: i?.addedBy,
          payroll: i?.payroll,
          addedName: user?.data?.name,
          lastEditedBy: lastEditedBy?.data?.name,
          uid: i?.uid,
        });
      }
    }
    if (payrollInfo?.adHocData?.length > 0) {
      for (let i of payrollInfo?.adHocData) {
        let user = await getUserById({
          id: i?.addedBy,
        });
        let lastEditedBy = '';
        if (i?.lastEditedBy) {
          lastEditedBy = await getUserById({
            id: i?.lastEditedBy,
          });
        }
        adHocTemp?.push({
          amount: i?.amount,
          date: new Date(i?.date)?.toISOString()?.split('T')?.[0],
          remark: i?.remark,
          addedAt: i?.addedAt,
          payroll: i?.payroll,
          addedBy: i?.addedBy,
          addedName: user?.data?.name,
          lastEditedBy: lastEditedBy?.data?.name,
          uid: i?.uid,
        });
      }
    }
    setBonusData(bonusTemp);
    setDeductionData(deductionTemp);
    setAdvanceData(advanceTemp);
    setReimbursements(reimbursementTemp);
    setAdHokData(adHocTemp);
  };

  const setPaymentsData = async (payrollInfo, userDataDuringPayrollRun) => {
    if (
      payrollInfo?.userId !== undefined &&
      userDataDuringPayrollRun?.[payrollInfo?.userId] !== undefined
    ) {
      let bonusTemp = [];
      let deductionTemp = [];
      let advanceTemp = [];
      let reimbursementTemp = [];
      let adHocTemp = [];
      let user = userDataDuringPayrollRun?.[payrollInfo?.userId];
      if (user?.bonusData?.length > 0) {
        for (let i of user?.bonusData) {
          let foundUser = await getUserById({
            id: i?.addedBy,
          });
          let lastEditedBy = '';
          if (i?.lastEditedBy) {
            lastEditedBy = await getUserById({
              id: i?.lastEditedBy,
            });
          }
          bonusTemp?.push({
            amount: i?.amount,
            date: new Date(i?.date)?.toISOString()?.split('T')?.[0],
            remark: i?.remark,
            addedAt: i?.addedAt,
            payroll: i?.payroll,
            addedBy: i?.addedBy,
            addedName: foundUser?.data?.name,
            lastEditedBy: lastEditedBy?.data?.name,
            uid: i?.uid,
          });
        }
      }
      if (user?.deductionData?.length > 0) {
        for (let i of user?.deductionData) {
          let foundUser = await getUserById({
            id: i?.addedBy,
          });
          let lastEditedBy = '';
          if (i?.lastEditedBy) {
            lastEditedBy = await getUserById({
              id: i?.lastEditedBy,
            });
          }
          deductionTemp?.push({
            amount: i?.amount,
            date: new Date(i?.date)?.toISOString()?.split('T')?.[0],
            remark: i?.remark,
            addedAt: i?.addedAt,
            payroll: i?.payroll,
            addedBy: i?.addedBy,
            addedName: foundUser?.data?.name,
            lastEditedBy: lastEditedBy?.data?.name,
            uid: i?.uid,
          });
        }
      }
      if (user?.reimbursements?.length > 0) {
        for (let i of user?.reimbursements) {
          let foundUser = await getUserById({
            id: i?.addedBy,
          });
          let lastEditedBy = '';
          if (i?.lastEditedBy) {
            lastEditedBy = await getUserById({
              id: i?.lastEditedBy,
            });
          }
          reimbursementTemp?.push({
            amount: i?.amount,
            date: new Date(i?.date)?.toISOString()?.split('T')?.[0],
            remark: i?.remark,
            addedAt: i?.addedAt,
            payroll: i?.payroll,
            addedBy: i?.addedBy,
            addedName: foundUser?.data?.name,
            lastEditedBy: lastEditedBy?.data?.name,
            uid: i?.uid,
          });
        }
      }
      if (user?.advanceData?.length > 0) {
        for (let i of user?.advanceData) {
          let foundUser = await getUserById({
            id: i?.addedBy,
          });
          let lastEditedBy = '';
          if (i?.lastEditedBy) {
            lastEditedBy = await getUserById({
              id: i?.lastEditedBy,
            });
          }
          advanceTemp?.push({
            amount: i?.amount,
            date: new Date(i?.date)?.toISOString()?.split('T')?.[0],
            remark: i?.remark,
            addedAt: i?.addedAt,
            addedBy: i?.addedBy,
            payroll: i?.payroll,
            addedName: foundUser?.data?.name,
            lastEditedBy: lastEditedBy?.data?.name,
            uid: i?.uid,
          });
        }
      }
      if (user?.adHocData?.length > 0) {
        for (let i of user?.adHocData) {
          let foundUser = await getUserById({
            id: i?.addedBy,
          });
          let lastEditedBy = '';
          if (i?.lastEditedBy) {
            lastEditedBy = await getUserById({
              id: i?.lastEditedBy,
            });
          }
          adHocTemp?.push({
            amount: i?.amount,
            date: new Date(i?.date)?.toISOString()?.split('T')?.[0],
            remark: i?.remark,
            addedAt: i?.addedAt,
            payroll: i?.payroll,
            addedBy: i?.addedBy,
            addedName: foundUser?.data?.name,
            lastEditedBy: lastEditedBy?.data?.name,
            uid: i?.uid,
          });
        }
      }
      setBonusData(bonusTemp);
      setDeductionData(deductionTemp);
      setAdvanceData(advanceTemp);
      setReimbursements(reimbursementTemp);
      setAdHokData(adHocTemp);
    }
  };

  useEffect(() => {
    if (!isRunningPayroll) {
      setPaymentsDataDuringPayrollRun(payrollInfo);
    } else {
      setPaymentsData(payrollInfo, userDataDuringPayrollRun);
    }
  }, [payrollInfo, isRunningPayroll]); //eslint-disable-line

  const handleSubmit = async () => {
    const confirm = await customConfirm(
      'Submit the current information?',
      'Success'
    );
    if (!confirm) return;
    setSubmitLoading(true);
    if (isRunningPayroll) {
      setPayrollRunData((prev) =>
        prev?.map((elem) => {
          if (elem?._id === payrollInfo?.userId) {
            if (isRunningPayroll && payrollStep === 1) {
              return {
                ...elem,
                bonusData,
                advanceData,
                deductionData,
              };
            }
            if (isRunningPayroll && payrollStep === 2) {
              return {
                ...elem,
                adHocData: adHokData,
                reimbursements,
              };
            }
          }
        })
      );
      if (isRunningPayroll && payrollStep === 1) {
        setUserDataDuringPayrollRun((prev) => ({
          ...(prev || {}),
          [payrollInfo?.userId]: {
            ...(prev?.[payrollInfo?.userId] || {}),
            bonusData: bonusData,
            deductionData: deductionData,
            advanceData: advanceData,
          },
        }));
      }
      if (isRunningPayroll && payrollStep === 2) {
        setUserDataDuringPayrollRun((prev) => ({
          ...(prev || {}),
          [payrollInfo?.userId]: {
            ...(prev?.[payrollInfo?.userId] || {}),
            adHocData: adHokData,
            reimbursements,
          },
        }));
      }
      toast.success('Payroll Information Updated');
      setBonusData([]);
      setDeductionData([]);
      setAdvanceData([]);
      setAdHokData([]);
      setOpenModal(false);
      setSubmitLoading(false);
    } else {
      let data = {
        bonusData,
        advanceData,
        deductionData,
        adHocData: adHokData,
        reimbursements,
      };
      const res = await editUser({ data: data, id: payrollInfo?.userId });
      setSubmitLoading(false);
      if (res?.data) {
        toast.success('Payroll Information Updated');
        setBonusData([]);
        setDeductionData([]);
        setAdvanceData([]);
        setAdHokData([]);
        setOpenModal(false);
      }
    }
  };

  const changeHandler = (tab, name, value, index) => {
    if (tab === 0) {
      setBonusData((prev) =>
        prev?.map((elem) => {
          if (elem?.uid !== index) return elem;
          else {
            return {
              ...elem,
              [name]: value,
              lastEditedBy: user?._id,
            };
          }
        })
      );
    } else if (tab === 1) {
      setDeductionData((prev) =>
        prev?.map((elem) => {
          if (elem?.uid !== index) return elem;
          else {
            return {
              ...elem,
              [name]: value,
              lastEditedBy: user?._id,
            };
          }
        })
      );
    } else if (tab === 2) {
      setAdvanceData((prev) =>
        prev?.map((elem) => {
          if (elem?.uid !== index) return elem;
          else {
            return {
              ...elem,
              [name]: value,
              lastEditedBy: user?._id,
            };
          }
        })
      );
    } else if (tab === 3) {
      setAdHokData((prev) =>
        prev?.map((elem) => {
          if (elem?.uid !== index) return elem;
          else {
            return {
              ...elem,
              [name]: value,
              lastEditedBy: user?._id,
            };
          }
        })
      );
    } else {
      setReimbursements((prev) =>
        prev?.map((elem) => {
          if (elem?.uid !== index) return elem;
          else {
            return {
              ...elem,
              [name]: value,
              lastEditedBy: user?._id,
            };
          }
        })
      );
    }
  };

  const bonusColumns = [
    {
      title: 'Amount',
      key: 'bonusAmount',
      render: (_, record) => (
        <Input
          placeholder="Enter Bonus"
          className="w-full"
          name="amount"
          type="number"
          value={record?.amount}
          onChange={(e) => {
            changeHandler(
              0,
              e.target.name,
              parseInt(e.target.value),
              record?.uid
            );
          }}
          disabled={record?.payroll !== undefined}
        />
      ),
    },
    {
      title: 'Date',
      key: 'bonusDate',
      render: (_, record) => (
        <Input
          placeholder="Enter Bonus Date"
          type="date"
          className="w-full"
          name="date"
          value={record?.date}
          // min={
          //   isRunningPayroll
          //     ? new Date(startDate).toISOString().split('T')[0]
          //     : ''
          // }
          // max={
          //   isRunningPayroll
          //     ? new Date(endDate).toISOString().split('T')[0]
          //     : ''
          // }
          onChange={(e) =>
            changeHandler(0, e.target.name, e.target.value, record?.uid)
          }
          disabled={record?.payroll !== undefined}
        />
      ),
    },
    {
      title: 'Remark',
      key: 'remark',
      render: (_, record) => (
        <>
          <Input
            placeholder="Enter Ad Hoc Amount"
            className="w-full"
            name="remark"
            value={record?.remark}
            onChange={(e) =>
              changeHandler(0, e.target.name, e.target.value, record?.uid)
            }
            disabled={record?.payroll !== undefined ? true : false}
          />
        </>
      ),
    },
    {
      title: 'Added By',
      key: 'addedBy',
      render: (_, record) => (
        <Tag color="blue">
          {user?._id === record?.addedBy ? user?.name : record?.addedBy}
        </Tag>
      ),
    },
    {
      title: 'Last Edited By',
      key: 'lastEditedBy',
      render: (_, record) => (
        <Tag color="blue">
          {user?._id === record?.lastEditedBy
            ? user?.name
            : record?.lastEditedBy}
        </Tag>
      ),
    },
    {
      title: 'Used in Payroll',
      key: 'usedInPayroll',
      render: (_, record) => (
        <Tag color="violet">
          {record?.payroll === undefined ? 'FALSE' : 'TRUE'}
        </Tag>
      ),
    },
    {
      title: '',
      key: 'actions',
      render: (_, record) => (
        <TiDelete
          className={`text-2xl ml-auto ${record?.payroll === undefined ? 'text-red-500 cursor-pointer' : 'text-gray-500'}`}
          onClick={() => {
            if (record?.payroll === undefined) {
              setBonusData((prev) =>
                prev?.filter((elem) => record?.uid !== elem?.uid)
              );
            }
          }}
        />
      ),
    },
  ];

  const deductionColumns = [
    {
      title: 'Amount',
      key: 'deductionAmount',
      render: (_, record) => (
        <Input
          placeholder="Enter Deduction"
          className="w-full"
          type="number"
          name="amount"
          value={record?.amount}
          onChange={(e) =>
            changeHandler(
              1,
              e.target.name,
              parseInt(e.target.value),
              record?.uid
            )
          }
          disabled={record?.payroll !== undefined}
        />
      ),
    },
    {
      title: 'Date',
      key: 'deductionDate',
      render: (_, record) => (
        <Input
          placeholder="Enter Deduction Date"
          type="date"
          className="w-full"
          name="date"
          value={record?.date}
          // min={
          //   isRunningPayroll
          //     ? new Date(startDate).toISOString().split('T')[0]
          //     : ''
          // }
          // max={
          //   isRunningPayroll
          //     ? new Date(endDate).toISOString().split('T')[0]
          //     : ''
          // }
          onChange={(e) =>
            changeHandler(1, e.target.name, e.target.value, record?.uid)
          }
          disabled={record?.payroll !== undefined}
        />
      ),
    },
    {
      title: 'Remark',
      key: 'remark',
      render: (_, record) => (
        <Input
          placeholder="Enter Ad Hoc Amount"
          className="w-full"
          name="remark"
          value={record?.remark}
          onChange={(e) =>
            changeHandler(1, e.target.name, e.target.value, record?.uid)
          }
          disabled={record?.payroll !== undefined}
        />
      ),
    },
    {
      title: 'Added By',
      key: 'addedBy',
      render: (_, record) => (
        <Tag color="blue">
          {user?._id === record?.addedBy ? user?.name : record?.addedBy}
        </Tag>
      ),
    },
    {
      title: 'Last Edited By',
      key: 'lastEditedBy',
      render: (_, record) => (
        <Tag color="blue">
          {user?._id === record?.lastEditedBy
            ? user?.name
            : record?.lastEditedBy}
        </Tag>
      ),
    },
    {
      title: 'Used in Payroll',
      key: 'usedInPayroll',
      render: (_, record) => (
        <Tag color="violet">
          {record?.payroll === undefined ? 'FALSE' : 'TRUE'}
        </Tag>
      ),
    },
    {
      title: '',
      key: 'actions',
      render: (_, record) => (
        <TiDelete
          className={`text-2xl ml-auto ${record?.payroll === undefined ? 'text-red-500 cursor-pointer' : 'text-gray-500'}`}
          onClick={() => {
            if (record?.payroll === undefined) {
              setDeductionData((prev) =>
                prev?.filter((elem) => record?.uid !== elem?.uid)
              );
            }
          }}
        />
      ),
    },
  ];

  const advanceColumns = [
    {
      title: 'Amount',
      key: 'advanceAmount',
      render: (_, record) => (
        <Input
          type="number"
          placeholder="Enter Advance"
          className="w-full"
          name="amount"
          value={record?.amount}
          onChange={(e) =>
            changeHandler(
              2,
              e.target.name,
              parseInt(e.target.value),
              record?.uid
            )
          }
          disabled={record?.payroll !== undefined}
        />
      ),
    },
    {
      title: 'Date',
      key: 'advanceDate',
      render: (_, record) => (
        <Input
          placeholder="Enter Advance Date"
          type="date"
          className="w-full"
          name="date"
          value={record?.date}
          // min={
          //   isRunningPayroll
          //     ? new Date(startDate).toISOString().split('T')[0]
          //     : ''
          // }
          // max={
          //   isRunningPayroll
          //     ? new Date(endDate).toISOString().split('T')[0]
          //     : ''
          // }
          onChange={(e) =>
            changeHandler(2, e.target.name, e.target.value, record?.uid)
          }
          disabled={record?.payroll !== undefined}
        />
      ),
    },
    {
      title: 'Remark',
      key: 'remark',
      render: (_, record) => (
        <Input
          placeholder="Enter Ad Hoc Amount"
          className="w-full"
          name="remark"
          value={record?.remark}
          onChange={(e) =>
            changeHandler(2, e.target.name, e.target.value, record?.uid)
          }
          disabled={record?.payroll !== undefined}
        />
      ),
    },
    {
      title: 'Added By',
      key: 'addedBy',
      render: (_, record) => (
        <Tag color="blue">
          {user?._id === record?.addedBy ? user?.name : record?.addedBy}
        </Tag>
      ),
    },
    {
      title: 'Last Edited By',
      key: 'lastEditedBy',
      render: (_, record) => (
        <Tag color="blue">
          {user?._id === record?.lastEditedBy
            ? user?.name
            : record?.lastEditedBy}
        </Tag>
      ),
    },
    {
      title: 'Used in Payroll',
      key: 'usedInPayroll',
      render: (_, record) => (
        <Tag color="violet">
          {record?.payroll === undefined ? 'FALSE' : 'TRUE'}
        </Tag>
      ),
    },
    {
      title: '',
      key: 'actions',
      render: (_, record) => (
        <TiDelete
          className={`text-2xl ml-auto ${record?.payroll !== undefined ? 'text-red-500 cursor-pointer' : 'text-gray-500'}`}
          onClick={() => {
            if (record?.payroll !== undefined) {
              setAdvanceData((prev) =>
                prev?.filter((elem) => record?.uid !== elem?.uid)
              );
            }
          }}
        />
      ),
    },
  ];

  const reimbursementsColumns = [
    {
      title: 'Amount',
      key: 'reimbursementAmount',
      render: (_, record) => (
        <Input
          type="number"
          placeholder="Enter Reimbursement"
          className="w-full"
          name="amount"
          value={record?.amount}
          onChange={(e) =>
            changeHandler(
              4,
              e.target.name,
              parseInt(e.target.value),
              record?.uid
            )
          }
          disabled={record?.payroll !== undefined}
        />
      ),
    },
    {
      title: 'Date',
      key: 'reimbursementDate',
      render: (_, record) => (
        <Input
          placeholder="Enter reimbursement Date"
          type="date"
          className="w-full"
          name="date"
          value={record?.date}
          // min={
          //   isRunningPayroll
          //     ? new Date(startDate).toISOString().split('T')[0]
          //     : ''
          // }
          // max={
          //   isRunningPayroll
          //     ? new Date(endDate).toISOString().split('T')[0]
          //     : ''
          // }
          onChange={(e) =>
            changeHandler(4, e.target.name, e.target.value, record?.uid)
          }
          disabled={record?.payroll !== undefined}
        />
      ),
    },
    {
      title: 'Remark',
      key: 'remark',
      render: (_, record) => (
        <Input
          placeholder="Enter Ad Hoc Amount"
          className="w-full"
          name="remark"
          value={record?.remark}
          onChange={(e) =>
            changeHandler(4, e.target.name, e.target.value, record?.uid)
          }
          disabled={record?.payroll !== undefined}
        />
      ),
    },
    {
      title: 'Added By',
      key: 'addedBy',
      render: (_, record) => (
        <Tag color="blue">
          {user?._id === record?.addedBy ? user?.name : record?.addedBy}
        </Tag>
      ),
    },
    {
      title: 'Last Edited By',
      key: 'lastEditedBy',
      render: (_, record) => (
        <Tag color="blue">
          {user?._id === record?.lastEditedBy
            ? user?.name
            : record?.lastEditedBy}
        </Tag>
      ),
    },
    {
      title: 'Used in Payroll',
      key: 'usedInPayroll',
      render: (_, record) => (
        <Tag color="violet">
          {record?.payroll === undefined ? 'FALSE' : 'TRUE'}
        </Tag>
      ),
    },
    {
      title: '',
      key: 'actions',
      render: (_, record) => (
        <TiDelete
          className={`text-2xl ml-auto ${record?.payroll === undefined ? 'text-red-500 cursor-pointer' : 'text-gray-500'}`}
          onClick={() => {
            if (record?.payroll === undefined) {
              setReimbursements((prev) =>
                prev?.filter((elem) => record?.uid !== elem?.uid)
              );
            }
          }}
        />
      ),
    },
  ];

  const adHocColumns = [
    {
      title: 'Amount',
      key: 'adHocAmount',
      render: (_, record) => (
        <Input
          type="number"
          placeholder="Enter Ad Hoc Amount"
          className="w-full"
          name="amount"
          value={record?.amount}
          onChange={(e) =>
            changeHandler(
              3,
              e.target.name,
              parseInt(e.target.value),
              record?.uid
            )
          }
          disabled={record?.payroll !== undefined}
        />
      ),
    },
    {
      title: 'Date',
      key: 'adHocDate',
      render: (_, record) => (
        <Input
          placeholder="Enter Ad Hoc Date"
          type="date"
          className="w-full"
          name="date"
          value={record?.date}
          // min={
          //   isRunningPayroll
          //     ? new Date(startDate).toISOString().split('T')[0]
          //     : ''
          // }
          // max={
          //   isRunningPayroll
          //     ? new Date(endDate).toISOString().split('T')[0]
          //     : ''
          // }
          onChange={(e) =>
            changeHandler(3, e.target.name, e.target.value, record?.uid)
          }
          disabled={record?.payroll !== undefined}
        />
      ),
    },
    {
      title: 'Remark',
      key: 'remark',
      render: (_, record) => (
        <Input
          placeholder="Enter Ad Hoc Amount"
          className="w-full"
          name="remark"
          value={record?.remark}
          onChange={(e) =>
            changeHandler(3, e.target.name, e.target.value, record?.uid)
          }
          disabled={record?.payroll !== undefined}
        />
      ),
    },
    {
      title: 'Media',
      key: 'adHocMedia',
      render: (_, record) => (
        <div className="flex items-center gap-[5px] w-full">
          <UploadButton
            accept=".jpg, .jpeg, .png, .pdf"
            multiple
            onChange={(e) => pdfChangeHandler(e, record?.uid)}
            className="text-[#667085] w-full"
            width="100%"
            size="sm"
            disabled={record?.payroll !== undefined}
          />
          <div
            className="p-[6px] rounded-[5px] bg-blue-500 hover:bg-blue-400 cursor-pointer relative"
            onClick={async () => {
              setShowAdHocMedia(true);
              let temp = [];
              for (let i of record?.media) {
                if (i?.data === undefined) {
                  let med = await getMediaById({ id: i });
                  temp?.push(med?.data?.media);
                } else {
                  temp?.push(i);
                }
              }
              setSelectedAdHoc(record?.uid);
              setAdHocMedia(temp);
            }}
          >
            <LuFiles className="text-lg text-white" />
            {record?.media?.length > 0 && (
              <span className="rounded-full py-[2px] px-[7px] text-[10px] text-white bg-red-500 absolute top-[-0.6rem] left-[1.2rem]">
                {record?.media?.length}
              </span>
            )}
          </div>
        </div>
      ),
    },
    {
      title: 'Added By',
      key: 'addedBy',
      render: (_, record) => (
        <Tag color="blue">
          {user?._id === record?.addedBy ? user?.name : record?.addedBy}
        </Tag>
      ),
    },
    {
      title: 'Last Edited By',
      key: 'lastEditedBy',
      render: (_, record) => (
        <Tag color="blue">
          {user?._id === record?.lastEditedBy
            ? user?.name
            : record?.lastEditedBy}
        </Tag>
      ),
    },
    {
      title: 'Used in Payroll',
      key: 'usedInPayroll',
      render: (_, record) => (
        <Tag color="violet">
          {record?.payroll === undefined ? 'FALSE' : 'TRUE'}
        </Tag>
      ),
    },
    {
      title: '',
      key: 'actions',
      render: (_, record) => (
        <TiDelete
          className={`text-2xl ml-auto ${record?.payroll === undefined ? 'text-red-500 cursor-pointer' : 'text-gray-500'}`}
          onClick={() => {
            if (record?.payroll === undefined) {
              setAdHokData((prev) =>
                prev?.filter((elem) => record?.uid !== elem?.uid)
              );
            }
          }}
        />
      ),
    },
  ];

  const adHocMediaColumns = [
    {
      title: 'Name',
      key: 'mediaName',
      render: (_, record) => <p>{record?.name}</p>,
    },
    {
      title: 'Type',
      key: 'mediaType',
      render: (_, record) => <Tag color="purple">{record?.type}</Tag>,
    },
    {
      title: '',
      key: 'actions',
      render: (_, record, index) => (
        <div className="flex items-center gap-2 justify-end">
          <FaEye
            onClick={() => {
              setSelectedMedia(record);
            }}
            className="text-2xl text-slate-300 hover:text-slate-400 cursor-pointer"
          />
          <TiDelete
            className="text-2xl text-red-500 hover:text-red-400 cursor-pointer"
            onClick={() => {
              setAdHocMedia((prev) =>
                prev?.filter((_, elemIndex) => elemIndex !== index)
              );
              setAdHokData((prev) =>
                prev?.map((elem) => {
                  if (elem?.uid === selectedAdHoc) {
                    return {
                      ...elem,
                      media: elem?.media?.filter(
                        (_, medIndex) => medIndex !== index
                      ),
                    };
                  } else {
                    return elem;
                  }
                })
              );
            }}
          />
        </div>
      ),
    },
  ];

  const pdfChangeHandler = (e, index) => {
    const files = Array.from(e || []);

    files.forEach((file) => {
      let fileName = file.name;
      let fileType = file.type;

      const fr = new FileReader();
      fr.readAsDataURL(file);
      fr.addEventListener('load', () => {
        const url = fr.result;
        let data = {
          name: fileName,
          type: fileType,
          data: url,
        };
        setAdHokData((prev) =>
          prev?.map((elem) => {
            if (elem?.uid !== index) return elem;
            else {
              return {
                ...elem,
                media: [...(elem?.media || []), data],
              };
            }
          })
        );
      });
    });
  };

  function filterBonusDataByDate(data, isShow = false) {
    if (isShow) {
      const start = new Date(startDate);
      const end = new Date(endDate);

      return data.filter((item) => {
        const itemDate = new Date(item.date);
        return itemDate >= start && itemDate <= end;
      });
    }
    return data;
  }

  const renderTabContent = () => {
    switch (tab) {
      case 0:
        return (
          <div className="overflow-y-scroll h-[58vh]">
            <div className="flex items-center justify-between mb-2">
              <h4>Bonus Amount</h4>
              {isRunningPayroll && (
                <Button
                  variant={`${isBonusThisMonth ? 'filled' : 'outlined'}`}
                  color={`${isBonusThisMonth ? 'blue' : ''}`}
                  onClick={() => {
                    setIsBonusThisMonth((prev) => !prev);
                  }}
                >
                  This Month
                </Button>
              )}
            </div>
            <Table
              columns={bonusColumns}
              loading={bonusData !== null ? false : true}
              dataSource={filterBonusDataByDate(bonusData, isBonusThisMonth)}
              rowKey={(_, index) => index}
              pagination={{
                position: ['bottomRight'],
                pageSize: bonusPageSize,
                showSizeChanger: true,
                onShowSizeChange: handleBonusPaginationSizeChange,
                pageSizeOptions: [5, 10, 20],
              }}
              size="middle"
              scroll={{ x: true }}
              locale={{ emptyText: 'No Data added yet' }}
            />
          </div>
        );
      case 1:
        return (
          <div className="overflow-y-scroll h-[58vh]">
            <div className="flex items-center justify-between mb-2">
              <h4>Deduction Amount</h4>
              {isRunningPayroll && (
                <Button
                  variant={`${isDeductionThisMonth ? 'filled' : 'outlined'}`}
                  color={`${isDeductionThisMonth ? 'blue' : ''}`}
                  onClick={() => {
                    setIsDeductionThisMonth((prev) => !prev);
                  }}
                >
                  This Month
                </Button>
              )}
            </div>
            <Table
              columns={deductionColumns}
              loading={deductionData !== null ? false : true}
              dataSource={filterBonusDataByDate(
                deductionData,
                isDeductionThisMonth
              )}
              rowKey={(_, index) => index}
              pagination={{
                position: ['bottomRight'],
                pageSize: deductionPageSize,
                showSizeChanger: true,
                onShowSizeChange: handleDeductionPaginationSizeChange,
                pageSizeOptions: [5, 10, 20],
              }}
              size="middle"
              scroll={{ x: true }}
              locale={{ emptyText: 'No Data added yet' }}
            />
          </div>
        );
      case 2:
        return (
          <div className="overflow-y-scroll h-[58vh]">
            <div className="flex items-center justify-between mb-2">
              <h4>Advance Amount</h4>
              {isRunningPayroll && (
                <Button
                  variant={`${isAdvanceThisMonth ? 'filled' : 'outlined'}`}
                  color={`${isAdvanceThisMonth ? 'blue' : ''}`}
                  onClick={() => {
                    setIsAdvanceThisMonth((prev) => !prev);
                  }}
                >
                  This Month
                </Button>
              )}
            </div>
            <Table
              columns={advanceColumns}
              loading={advanceData !== null ? false : true}
              dataSource={filterBonusDataByDate(
                advanceData,
                isAdvanceThisMonth
              )}
              rowKey={(_, index) => index}
              pagination={{
                position: ['bottomRight'],
                pageSize: advancePageSize,
                showSizeChanger: true,
                onShowSizeChange: handleAdvancePaginationSizeChange,
                pageSizeOptions: [5, 10, 20],
              }}
              size="middle"
              scroll={{ x: true }}
              locale={{ emptyText: 'No Data added yet' }}
            />
          </div>
        );
      case 3:
        return (
          <div className="overflow-y-scroll h-[58vh]">
            <div className="flex items-center justify-between mb-2">
              <h4>Ad Hoc Amount</h4>
              {isRunningPayroll && (
                <Button
                  variant={`${isAdHocThisMonth ? 'filled' : 'outlined'}`}
                  color={`${isAdHocThisMonth ? 'blue' : ''}`}
                  onClick={() => {
                    setIsAdHocThisMonth((prev) => !prev);
                  }}
                >
                  This Month
                </Button>
              )}
            </div>
            <Table
              columns={adHocColumns}
              loading={adHokData !== null ? false : true}
              dataSource={filterBonusDataByDate(adHokData, isAdHocThisMonth)}
              rowKey={(_, index) => index}
              pagination={{
                position: ['bottomRight'],
                pageSize: adHocPageSize,
                showSizeChanger: true,
                onShowSizeChange: handleAdHocPaginationSizeChange,
                pageSizeOptions: [5, 10, 20],
              }}
              size="middle"
              scroll={{ x: true }}
              locale={{ emptyText: 'No Data added yet' }}
            />
          </div>
        );
      case 4:
        return (
          <div className="overflow-y-scroll h-[58vh]">
            <div className="flex items-center justify-between mb-2">
              <h4>Reimbursements</h4>
              {isRunningPayroll && (
                <Button
                  variant={`${isReimbursementsThisMonth ? 'filled' : 'outlined'}`}
                  color={`${isReimbursementsThisMonth ? 'blue' : ''}`}
                  onClick={() => {
                    setIsReimbursementsThisMonth((prev) => !prev);
                  }}
                >
                  This Month
                </Button>
              )}
            </div>
            <Table
              columns={reimbursementsColumns}
              loading={reimbursements !== null ? false : true}
              dataSource={filterBonusDataByDate(
                reimbursements,
                isReimbursementsThisMonth
              )}
              rowKey={(_, index) => index}
              pagination={{
                position: ['bottomRight'],
                pageSize: reimbursementsPageSize,
                showSizeChanger: true,
                onShowSizeChange: handleReimbursementPaginationSizeChange,
                pageSizeOptions: [5, 10, 20],
              }}
              size="middle"
              scroll={{ x: true }}
              locale={{ emptyText: 'No Data added yet' }}
            />
          </div>
        );
    }
  };

  const closeMediaModal = () => {
    setShowAdHocMedia(false);
    setAdHocMedia([]);
  };

  function generateUniqueId() {
    const timestamp = Date.now().toString().slice(-6); // Last 6 digits of timestamp
    const random = Math.floor(1000 + Math.random() * 9000); // 4 random digits
    return timestamp + random; // 6 + 4 = 10 digits
  }

  return (
    <>
      {selectedMedia?.data?.length !== 0 && (
        <MediaViewer media={selectedMedia} setMedia={setSelectedMedia} />
      )}
      <Modal
        title="AdHoc Media"
        onCancel={closeMediaModal}
        open={showAdHocMedia}
        width={800}
        footer={[
          <Button key="back" onClick={closeMediaModal}>
            Done
          </Button>,
        ]}
      >
        <div className="h-[40vh] overflow-y-scroll">
          <Table
            columns={adHocMediaColumns}
            dataSource={adHocMedia}
            rowKey={(_, index) => index}
            pagination={false}
            size="middle"
            scroll={{ x: true }}
            locale={{ emptyText: 'No Data added yet' }}
          />
        </div>
      </Modal>
      <Modal
        title="Payroll Information"
        onCancel={closeModal}
        open={openModal}
        width={1600}
        footer={[
          <Button key="back" onClick={closeModal}>
            Cancel
          </Button>,
          <Button
            key="add"
            onClick={() => {
              if (tab === 0) {
                setBonusData((prev) => [
                  ...prev,
                  {
                    amount: '',
                    date: '',
                    uid: generateUniqueId(),
                    fromPayroll: isRunningPayroll,
                    addedBy: user?._id,
                    addedAt: new Date()
                      .toLocaleDateString('en-GB')
                      .split('/')
                      .join('-'),
                    remark: '',
                  },
                ]);
              }
              if (tab === 1) {
                setDeductionData((prev) => [
                  ...prev,
                  {
                    amount: '',
                    date: '',
                    uid: generateUniqueId(),
                    fromPayroll: isRunningPayroll,
                    addedBy: user?._id,
                    addedAt: new Date()
                      .toLocaleDateString('en-GB')
                      .split('/')
                      .join('-'),
                    remark: '',
                  },
                ]);
              }
              if (tab === 2) {
                setAdvanceData((prev) => [
                  ...prev,
                  {
                    amount: '',
                    date: '',
                    uid: generateUniqueId(),
                    fromPayroll: isRunningPayroll,
                    addedBy: user?._id,
                    addedAt: new Date()
                      .toLocaleDateString('en-GB')
                      .split('/')
                      .join('-'),
                    remark: '',
                  },
                ]);
              }
              if (tab === 3) {
                setAdHokData((prev) => [
                  ...prev,
                  {
                    amount: '',
                    date: '',
                    uid: generateUniqueId(),
                    fromPayroll: isRunningPayroll,
                    addedBy: user?._id,
                    addedAt: new Date()
                      .toLocaleDateString('en-GB')
                      .split('/')
                      .join('-'),
                    remark: '',
                  },
                ]);
              }
              if (tab === 4) {
                setReimbursements((prev) => [
                  ...prev,
                  {
                    amount: '',
                    date: '',
                    uid: generateUniqueId(),
                    fromPayroll: isRunningPayroll,
                    addedBy: user?._id,
                    addedAt: new Date()
                      .toLocaleDateString('en-GB')
                      .split('/')
                      .join('-'),
                    remark: '',
                  },
                ]);
              }
            }}
            className="bg-green-500 text-white"
          >
            Add
          </Button>,
          <Button
            key="submit"
            type="primary"
            loading={submitLoading}
            onClick={handleSubmit}
          >
            Submit
          </Button>,
        ]}
      >
        <div className="flex flex-row h-[63vh]">
          <div className="basis-1/4 border-r-[1px] border-solid border-slate-300">
            <div>
              {isRunningPayroll && payrollStep === 1 && (
                <>
                  <Button
                    className="w-[20vw] mb-2"
                    onClick={() => {
                      setTab(0);
                    }}
                    color={`${tab === 0 ? 'blue' : ''}`}
                    variant={`${tab === 0 ? 'filled' : 'outlined'}`}
                  >
                    Instant Bonus
                  </Button>
                  <Button
                    className="w-[20vw] mb-2"
                    onClick={() => {
                      setTab(3);
                    }}
                    color={`${tab === 3 ? 'blue' : ''}`}
                    variant={`${tab === 3 ? 'filled' : 'outlined'}`}
                  >
                    Ad-Hoc Payments
                  </Button>
                  <Button
                    className="w-[20vw]"
                    onClick={() => {
                      setTab(4);
                    }}
                    color={`${tab === 4 ? 'blue' : ''}`}
                    variant={`${tab === 4 ? 'filled' : 'outlined'}`}
                  >
                    Reimbursements
                  </Button>
                </>
              )}
              {isRunningPayroll && payrollStep === 2 && (
                <>
                  <Button
                    className="w-[20vw] mb-2"
                    onClick={() => {
                      setTab(1);
                    }}
                    color={`${tab === 1 ? 'blue' : ''}`}
                    variant={`${tab === 1 ? 'filled' : 'outlined'}`}
                  >
                    Instant Deduction
                  </Button>
                  <Button
                    className="w-[20vw] mb-2"
                    onClick={() => {
                      setTab(2);
                    }}
                    color={`${tab === 2 ? 'blue' : ''}`}
                    variant={`${tab === 2 ? 'filled' : 'outlined'}`}
                  >
                    Advance Payments
                  </Button>
                </>
              )}
              {!isRunningPayroll && (
                <>
                  <Button
                    className="w-[20vw] mb-2"
                    onClick={() => {
                      setTab(0);
                    }}
                    color={`${tab === 0 ? 'blue' : ''}`}
                    variant={`${tab === 0 ? 'filled' : 'outlined'}`}
                  >
                    Instant Bonus
                  </Button>
                  <Button
                    className="w-[20vw] mb-2"
                    onClick={() => {
                      setTab(1);
                    }}
                    color={`${tab === 1 ? 'blue' : ''}`}
                    variant={`${tab === 1 ? 'filled' : 'outlined'}`}
                  >
                    Instant Deduction
                  </Button>
                  <Button
                    className="w-[20vw] mb-2"
                    onClick={() => {
                      setTab(2);
                    }}
                    color={`${tab === 2 ? 'blue' : ''}`}
                    variant={`${tab === 2 ? 'filled' : 'outlined'}`}
                  >
                    Advance Payments
                  </Button>
                  <Button
                    className="w-[20vw] mb-2"
                    onClick={() => {
                      setTab(3);
                    }}
                    color={`${tab === 3 ? 'blue' : ''}`}
                    variant={`${tab === 3 ? 'filled' : 'outlined'}`}
                  >
                    Ad-Hoc Payments
                  </Button>
                  <Button
                    className="w-[20vw]"
                    onClick={() => {
                      setTab(4);
                    }}
                    color={`${tab === 4 ? 'blue' : ''}`}
                    variant={`${tab === 4 ? 'filled' : 'outlined'}`}
                  >
                    Reimbursements
                  </Button>
                </>
              )}
            </div>
          </div>
          <div className="basis-3/4 px-4">{renderTabContent()}</div>
        </div>
      </Modal>
    </>
  );
};

export default UserPayrollModal;

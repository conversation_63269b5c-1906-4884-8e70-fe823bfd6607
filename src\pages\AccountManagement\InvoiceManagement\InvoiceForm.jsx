import {
  ArrowLeftOutlined,
  LoadingOutlined,
  SaveOutlined,
} from '@ant-design/icons';
import {
  <PERSON><PERSON>,
  But<PERSON>,
  DatePicker,
  Form,
  Input,
  InputNumber,
  Spin,
} from 'antd';
import dayjs from 'dayjs';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useNavigate, useParams, useSearchParams } from 'react-router-dom';
import { toast } from 'react-toastify';
import MediaCardsPreview from '../../../components/global/components/MediaCardsPreview';
import SelectV2 from '../../../components/global/components/SelectV2';
import Textarea from '../../../components/global/components/Textarea';
import StaticProductTable from '../../../components/ProductFormats/StaticProductTable';
import UploadButton from '../../../components/UploadButton';
import { renderFieldsBasedOnType } from '../../../helperFunction';
import usePrefixIds from '../../../hooks/usePrefixIds';
import {
  useCreateInvoiceMutation,
  useEditInvoiceMutation,
  useGetInvoiceByIdQuery,
} from '../../../slices/AccountManagement/invoicesApiSlice';
import { useGetAllcustomerQuery } from '../../../slices/customerDataSlice';
import { useGetDropdownsQuery } from '../../../slices/dropdownApiSlice';
import { useLazyQueryTemplateByIdQuery } from '../../../slices/dsahboardTemplateApiSlice';
import {
  useGetApprovedPoForOptionsQuery,
  useLazyGetPurchaseOrderByIdQuery,
} from '../../../slices/purchaseOrderApiSlice';
import {
  useGetApprovedSoForOptionsQuery,
  useLazyGetSalesOrderByIdQuery,
} from '../../../slices/salesOrderSlices';
import { useGetAllVendorsForOptionsQuery } from '../../../slices/vendorApiSlice';

const InvoiceForm = () => {
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { id: invoiceId, type = 'sales' } = useParams();
  const returnTab = searchParams.get('returnTab') || 'sales';
  const [additionalFields, setAdditionalFields] = useState(null);
  const [templateDropDownModal, setTemplateDropDownModal] = useState(false);
  const [newOptionStatus, setNewOptionStatus] = useState(false);
  const [dropdownIdx, setDropdownIdx] = useState(null);
  const [selectedTemplate, setSelectedTemplate] = useState(null);
  const [getTemplates, { data: templatesData }] =
    useLazyQueryTemplateByIdQuery();
  const [formData, setFormData] = useState({});
  const isEditing = !!invoiceId;
  const {
    data: invoiceData,
    isLoading: isFetchingInvoice,
    error: fetchError,
  } = useGetInvoiceByIdQuery({ id: invoiceId }, { skip: !isEditing });

  const { IdGenComp, idCompData } = usePrefixIds({
    idFor: type === 'sales' ? 'salesInvoiceId' : 'purchaseInvoiceId',
    templateIndex:
      additionalFields?.idIndex > -1 ? additionalFields?.idIndex : null,
    setIdData: setFormData,
  });

  useEffect(() => {
    const path = `/accountmanagement/invoice/${type === 'sales' ? 'sales' : 'purchase'}Invoice`;
    getTemplates({ path });
  }, [getTemplates, type]);

  useEffect(() => {
    if (isEditing && invoiceData?.additionalFields) {
      setAdditionalFields(invoiceData.additionalFields);
      setSelectedTemplate(invoiceData.additionalFields);
    } else if (templatesData && Array.isArray(templatesData)) {
      const defaultTemplate =
        templatesData.find((t) => t?.name?.startsWith('Default')) ||
        templatesData?.[0];
      if (defaultTemplate) {
        setAdditionalFields(defaultTemplate);
        setSelectedTemplate(defaultTemplate);
      }
    }
  }, [isEditing, invoiceData?.additionalFields, templatesData]);

  const [createInvoice, { isLoading: isCreating }] = useCreateInvoiceMutation();
  const [editInvoice, { isLoading: isUpdating }] = useEditInvoiceMutation();
  const { data: vendorsData } = useGetAllVendorsForOptionsQuery();
  const { data: customersData } = useGetAllcustomerQuery();
  const { data: approvedPOs } = useGetApprovedPoForOptionsQuery();
  const { data: salesOrdersData } = useGetApprovedSoForOptionsQuery();
  const [getSalesOrderById] = useLazyGetSalesOrderByIdQuery();
  const [getPurchaseOrderById] = useLazyGetPurchaseOrderByIdQuery();
  const { data: dropdownsData } = useGetDropdownsQuery();

  const [formError, setFormError] = useState(null);
  const [productInput, setProductInput] = useState([
    {
      key: Date.now() + Math.random(),
      itemId: '',
      productName: '',
      uom: '',
      hsn: '',
      quantity: '',
      rate: '',
      discount: '',
      amount: 0,
      cgst: '',
      sgst: '',
      cgstAmount: 0,
      sgstAmount: 0,
      igst: '',
      igstAmount: 0,
      totalAmount: 0,
      color: '#FFFFFF',
    },
  ]);
  const [productCharges, setProductCharges] = useState({});
  const [attachments, setAttachments] = useState([]);
  const [selectedOrderData, setSelectedOrderData] = useState(null);

  const isSubmitting = isCreating || isUpdating;

  const handleFormChange = useCallback(() => setFormError(null), []);

  const handleInputChange = (
    fieldValue,
    fieldName,
    idx,
    colIndex,
    tableRowIndex
  ) => {
    if (tableRowIndex !== undefined && tableRowIndex !== null) {
      setAdditionalFields((prev) => {
        const updatedTemplateData = [...prev.templateData];
        const fieldWithTableIndex = idx;
        if (fieldWithTableIndex === -1) return prev;

        const updatedTableOptions = {
          ...updatedTemplateData[fieldWithTableIndex]?.tableOptions,
        };

        if (!updatedTableOptions.column) {
          updatedTableOptions.column = [];
        } else {
          updatedTableOptions.column = [...updatedTableOptions.column];
        }

        if (!updatedTableOptions.column[colIndex].selectedOptions) {
          updatedTableOptions.column[colIndex] = {
            columnName: updatedTableOptions.column[colIndex].columnName,
            columnType: updatedTableOptions.column[colIndex].columnType,
            dropdownOptions:
              updatedTableOptions.column[colIndex].dropdownOptions,
            selectedOptions: [],
          };
        }
        const updatedSelectedOptions = [
          ...updatedTableOptions.column[colIndex].selectedOptions,
        ];
        updatedSelectedOptions[tableRowIndex] = fieldValue;

        updatedTableOptions.column[colIndex] = {
          ...updatedTableOptions.column[colIndex],
          selectedOptions: updatedSelectedOptions,
        };

        updatedTemplateData[fieldWithTableIndex] = {
          ...updatedTemplateData[fieldWithTableIndex],
          tableOptions: updatedTableOptions,
        };

        return {
          ...prev,
          templateData: updatedTemplateData,
        };
      });
      return;
    }

    if (fieldValue === '+') {
      setDropdownIdx(idx);
      setTemplateDropDownModal(true);
    } else {
      const updatedAdditionalFields = additionalFields?.templateData?.map(
        (field) => {
          if (field?.fieldName === fieldName) {
            return {
              ...field,
              fieldValue,
            };
          } else {
            return field;
          }
        }
      );
      setAdditionalFields((prev) => ({
        ...prev,
        templateData: updatedAdditionalFields,
      }));
    }
  };

  useEffect(() => {
    if (isEditing && invoiceData) {
      const formValues = {
        ...invoiceData,
        salesInvoiceId: invoiceData.salesInvoiceId,
        purchaseInvoiceId: invoiceData.purchaseInvoiceId,
        vendor: invoiceData.vendor?._id || invoiceData.vendor,
        customer: invoiceData.customer?._id || invoiceData.customer,
        invoiceDate: invoiceData.invoiceDate
          ? dayjs(invoiceData.invoiceDate)
          : dayjs(),
        dueDate: invoiceData.dueDate ? dayjs(invoiceData.dueDate) : null,
        purchaseOrderRef: invoiceData.purchaseOrderRef?._id,
        salesOrderRef: invoiceData.salesOrderRef?._id,
        notes: invoiceData.notes || '',
        status: invoiceData.status || 'draft',
        totalAmount: invoiceData.totalAmount || 0,
      };

      form.setFieldsValue(formValues);

      // Set attachments
      if (invoiceData.attachments?.length > 0) {
        setAttachments(invoiceData.attachments);
      }

      // Prefill order reference and trigger order selection
      const orderRefId =
        invoiceData[type === 'purchase' ? 'purchaseOrderRef' : 'salesOrderRef']
          ?._id;
      if (orderRefId) {
        handleOrderSelect(orderRefId);
      } else {
        // Pre-fill product details for manual invoices
        if (invoiceData.productDetails?.products) {
          setProductInput(invoiceData.productDetails.products);
        }
        if (invoiceData.productDetails?.charges) {
          setProductCharges(invoiceData.productDetails.charges);
        }
      }
    } else if (!isEditing) {
      form.setFieldsValue({
        invoiceDate: dayjs(),
        dueDate: dayjs().add(30, 'days'),
        status: 'draft',
        type: type,
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isEditing, invoiceData, form, type]);

  useEffect(() => {
    const totalAmount = productInput?.reduce((acc, curr) => {
      return (
        acc + curr.amount + curr.cgstAmount + curr.sgstAmount + curr.igstAmount
      );
    }, 0);
    if (totalAmount === 0) {
      return;
    }
    form.setFieldsValue({
      totalAmount,
    });
  }, [form, productInput]);

  useEffect(() => {
    const salesOrderId = searchParams.get('salesOrderId');
    if (!salesOrderId) return;
    const extractDataFromSalesOrder = async (salesOrderId) => {
      try {
        const { data: orderData } = await getSalesOrderById({
          id: salesOrderId,
        });
        if (orderData) {
          setSelectedOrderData(orderData);

          // Handle products if they exist
          if (orderData.products?.length > 0) {
            const formattedProducts = orderData.products.map((product) => ({
              key: Date.now() + Math.random(),
              itemId: product.value || '',
              productName: product.details || '',
              uom: product.UOM || '',
              hsn: product.hsn || '',
              quantity: product.quantity || 0,
              rate: product.rate || 0,
              discount: product.discount || 0,
              amount: product.amount || 0,
              cgst: !orderData?.showIgst ? product.cgst || 0 : 0,
              sgst: !orderData?.showIgst ? product.sgst || 0 : 0,
              igst: orderData?.showIgst ? product.igst || 0 : 0,
              cgstAmount: !orderData?.showIgst
                ? (product.amount * (product.cgst || 0)) / 100
                : 0,
              sgstAmount: !orderData?.showIgst
                ? (product.amount * (product.sgst || 0)) / 100
                : 0,
              igstAmount: orderData?.showIgst
                ? (product.amount * (product.igst || 0)) / 100
                : 0,
              color: '#FFFFFF',
            }));
            setProductInput(formattedProducts);
          }

          const updateFields = {
            [type === 'purchase' ? 'vendor' : 'customer']:
              orderData.CustomerData?._id,
            totalAmount: orderData.charges?.total || orderData.subTotal || 0,
            billingAddress: orderData.billingAddress,
            deliveryAddress: orderData.deliveryAddress,
            salesOrderRef: salesOrderId,
          };

          form.setFieldsValue(updateFields);
        }
      } catch (error) {
        toast.error('Failed to load sales order data');
      }
    };

    if (salesOrderId) {
      extractDataFromSalesOrder(salesOrderId);
    }
  }, [searchParams, form, getSalesOrderById, type]);

  const uomOptions = useMemo(() => {
    return (
      dropdownsData?.dropdowns
        ?.find((e) => e.name === 'uom')
        ?.values?.map((option) => ({
          label: option,
          value: option,
        })) || []
    );
  }, [dropdownsData]);

  const handleMediaUpload = (e) => {
    for (let i in e) {
      if (i === 'length') return;

      const file = e[i];
      const fileName = file.name;
      const fileType = file.type;

      // Check for duplicate files
      const isDuplicate = attachments?.some(
        (existingFile) => existingFile?.name === fileName
      );
      if (isDuplicate) {
        toast.error('File with the same name already added');
        continue;
      }

      const fr = new FileReader();
      fr.readAsDataURL(file);
      fr.addEventListener('load', () => {
        const url = fr.result;
        const fileData = {
          name: fileName,
          type: fileType,
          data: url,
          size: file.size,
        };

        setAttachments((prev) => [...prev, fileData]);
      });
    }
  };

  const handleOrderSelect = async (orderId) => {
    if (!orderId) {
      setSelectedOrderData(null);
      setProductInput([]);
      setProductCharges({});
      return;
    }

    try {
      const orderType = type === 'purchase' ? 'purchase' : 'sales';
      let orderData;
      if (orderType === 'purchase') {
        const { data } = await getPurchaseOrderById({ id: orderId });
        orderData = data?.viewPurchaseOrder;
      } else {
        const { data } = await getSalesOrderById({ id: orderId });
        orderData = data;
      }

      if (orderData) {
        setSelectedOrderData(orderData);

        // Handle products if they exist
        if (orderData.products?.length > 0) {
          const formattedProducts = orderData.products.map((product) => ({
            key: Date.now() + Math.random(),
            itemId: product.value || '',
            productName: product.details || '',
            uom: product.UOM || '',
            hsn: product.hsn || '',
            quantity: product.quantity || 0,
            rate: product.rate || 0,
            discount: product.discount || 0,
            amount: product.amount || 0,
            cgst: !orderData?.showIgst ? product.cgst || 0 : 0,
            sgst: !orderData?.showIgst ? product.sgst || 0 : 0,
            igst: orderData?.showIgst ? product.igst || 0 : 0,
            cgstAmount: !orderData?.showIgst
              ? (product.amount * (product.cgst || 0)) / 100
              : 0,
            sgstAmount: !orderData?.showIgst
              ? (product.amount * (product.sgst || 0)) / 100
              : 0,
            igstAmount: orderData?.showIgst
              ? (product.amount * (product.igst || 0)) / 100
              : 0,
            color: '#FFFFFF',
          }));
          setProductInput(formattedProducts);
        }

        // Handle items if they exist
        if (orderData.items?.length > 0) {
          const formattedItems = orderData.items.map((itemData) => {
            const item = itemData.item || itemData;
            return {
              key: Date.now() + Math.random(),
              itemId: item.value || '',
              productName: item.name || '',
              uom: item.uom || '',
              hsn:
                itemData.hsn || item.hsn_sacCode || item.itemDetails?.hsn || '',
              quantity: itemData.quantity || item.quantity || 0,
              rate: itemData.rate || item.rate || item.itemDetails?.rate || 0,
              discount:
                itemData.discount ||
                item.discount ||
                item.itemDetails?.discount ||
                0,
              amount: item.amount || 0,
              cgst: !orderData?.showIgst
                ? itemData.cgst || item.cgst || item.itemDetails?.cgst || 0
                : 0,
              sgst: !orderData?.showIgst
                ? itemData.sgst || item.sgst || item.itemDetails?.sgst || 0
                : 0,
              igst: orderData?.showIgst ? itemData.igst || item.igst || 0 : 0,
              cgstAmount: !orderData?.showIgst
                ? (item.amount * (item.cgst || 0)) / 100
                : 0,
              sgstAmount: !orderData?.showIgst
                ? (item.amount * (item.sgst || 0)) / 100
                : 0,
              igstAmount: orderData?.showIgst
                ? (item.amount * (item.igst || 0)) / 100
                : 0,
              color: item.color || '#FFFFFF',
            };
          });
          setProductInput(formattedItems);
        }

        // Pre-fill form with order data
        const updateFields = {
          [type === 'purchase' ? 'vendor' : 'customer']:
            orderData.CustomerData?._id || orderData.vendor?._id,
          purchaseOrderRef: orderType === 'purchase' ? orderId : undefined,
          salesOrderRef: orderType === 'sales' ? orderId : undefined,
          totalAmount: orderData.charges?.total || orderData.subTotal || 0,
          billingAddress: orderData.billingAddress,
          deliveryAddress: orderData.deliveryAddress,
        };

        form.setFieldsValue(updateFields);
      }
    } catch (error) {
      toast.error('Failed to load order data');
    }
  };

  const handleSubmit = async () => {
    try {
      setFormError(null);
      const values = await form.validateFields();

      const invoicePayload = {
        ...values,
        salesInvoiceId: values.salesInvoiceId,
        purchaseInvoiceId: values.purchaseInvoiceId,
        idData: idCompData?.dataToReturn,
        invoiceDate: values.invoiceDate.format('DD-MM-YYYY'),
        dueDate: values.dueDate?.format('DD-MM-YYYY'),
        type,
        attachments,
        additionalFields:
          additionalFields || formData?.additionalFields || null,
        creationType: !selectedOrderData
          ? 'manual'
          : type === 'sales'
            ? 'so'
            : 'po',
        salesOrderRef: type === 'sales' ? selectedOrderData?._id : undefined,
        purchaseOrderRef:
          type === 'purchase' ? selectedOrderData?._id : undefined,
        productDetails: {
          products: productInput,
          charges: productCharges,
        },
      };

      const action = isEditing
        ? editInvoice({ id: invoiceId, data: invoicePayload })
        : createInvoice({ data: invoicePayload });
      await action.unwrap();

      toast.success(
        `${type} Invoice ${isEditing ? 'Updated' : 'Created'} successfully!`
      );
      navigate(`/accountmanagement/invoices?tab=${returnTab}`);
    } catch (error) {
      const message =
        error.data?.message || error.message || 'Failed to save invoice';
      setFormError(message);
      toast.error(message);
    }
  };

  const handleBack = () =>
    navigate(`/accountmanagement/invoices?tab=${returnTab}`);

  const handleRemoveAttachment = (fileToDelete) => {
    setAttachments((prev) =>
      prev.filter((file) => file.name !== fileToDelete.name)
    );
  };

  const getEntityOptions = () => {
    const data = type === 'purchase' ? vendorsData : customersData?.customers;
    return (
      data?.map((item) => ({
        label: item.name || item.label || '',
        value: item._id || item.value || '',
      })) || []
    );
  };
  const getOrderOptions = () => {
    const data = type === 'purchase' ? approvedPOs : salesOrdersData;
    const nonCreatedInvoiceData = data?.filter((el) => !el.invoice);
    return (
      nonCreatedInvoiceData?.map((el) => ({
        label: `${el.poID || el.salesOrderID}`,
        value: el._id,
      })) || []
    );
  };

  if (isFetchingInvoice) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Spin size="large" indicator={<LoadingOutlined spin />} />
      </div>
    );
  }

  if (fetchError && isEditing) {
    return (
      <div className="flex items-center justify-center min-h-screen p-4">
        <div className="text-center p-6 max-w-md">
          <p className="text-red-500 mb-4">
            {fetchError.data?.message || 'Failed to load invoice'}
          </p>
          <Button onClick={() => navigate(-1)}>Go Back</Button>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white border border-gray-100 shadow-sm rounded-lg overflow-hidden mx-auto max-w-6xl">
      {/* Header */}
      <div className="bg-gray-50 px-4 py-3 border-b border-gray-100">
        <div className="flex items-center gap-3">
          <Button
            icon={<ArrowLeftOutlined />}
            onClick={handleBack}
            type="text"
            size="small"
            className="hover:bg-gray-200"
          />
          <div>
            <h2 className="text-lg font-semibold text-gray-900 mb-0">
              {isEditing ? 'Edit' : 'Create'} {type} Invoice
            </h2>
            <p className="text-sm text-gray-600 mb-0">
              {isEditing
                ? 'Update invoice information'
                : 'Create a new invoice entry'}
            </p>
          </div>
        </div>
      </div>

      {/* Form Content */}
      <div className="p-4 space-y-4">
        {formError && (
          <Alert
            message={formError}
            type="error"
            closable
            onClose={() => setFormError(null)}
            className="mb-4"
          />
        )}

        <Form
          form={form}
          layout="vertical"
          onValuesChange={handleFormChange}
          className="space-y-4"
        >
          {/* Basic Information */}
          <div className="bg-white border border-gray-200 rounded-lg p-3">
            <h3 className="text-sm font-medium text-gray-700 mb-3">
              Basic Information
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
              {isEditing ? (
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-600">
                    {type === 'sales'
                      ? 'Sales Invoice ID'
                      : 'Purchase Invoice ID'}
                  </label>
                  <Input
                    className="text-sm bg-gray-50"
                    disabled
                    value={
                      invoiceData?.[
                        type === 'sales'
                          ? 'salesInvoiceId'
                          : 'purchaseInvoiceId'
                      ]
                    }
                  />
                </div>
              ) : (
                <Form.Item
                  name={
                    type === 'sales' ? 'salesInvoiceID' : 'purchaseInvoiceID'
                  }
                  label={
                    <span className="text-sm font-medium text-gray-600">
                      Invoice ID
                    </span>
                  }
                  className="mb-3"
                >
                  <IdGenComp {...idCompData} />
                </Form.Item>
              )}
              <div className="space-y-1">
                <label className="block mb-1 text-sm text-gray-500 font-medium">
                  Choose Template
                </label>
                <SelectV2
                  options={templatesData?.map((template) => ({
                    value: template?._id,
                    name: template?.name,
                  }))}
                  value={selectedTemplate?._id}
                  onChange={(e) => {
                    const template = templatesData.find(
                      (t) => t._id === e.target.value
                    );
                    if (selectedTemplate?._id === e.target.value) {
                      return;
                    }
                    setAdditionalFields(template);
                    setSelectedTemplate(template);
                  }}
                />
              </div>
              <Form.Item
                name="invoiceDate"
                label={
                  <span className="text-sm font-medium text-gray-600">
                    Invoice Date
                  </span>
                }
                rules={[{ required: true }]}
                className="mb-3"
              >
                <DatePicker className="w-full text-sm" format={'DD-MM-YYYY'} />
              </Form.Item>
              <Form.Item
                name="dueDate"
                label={
                  <span className="text-sm font-medium text-gray-600">
                    Due Date
                  </span>
                }
                className="mb-3"
              >
                <DatePicker className="w-full text-sm" format={'DD-MM-YYYY'} />
              </Form.Item>
            </div>
          </div>

          {/* Order Reference */}
          <div className="bg-white border border-gray-200 rounded-lg p-3">
            <h3 className="text-sm font-medium text-gray-700 mb-3">
              Order Reference
            </h3>
            <div className="space-y-1">
              <Form.Item
                name={
                  type === 'purchase' ? 'purchaseOrderRef' : 'salesOrderRef'
                }
                label={
                  <span className="text-sm font-medium text-gray-600">
                    {type === 'purchase' ? 'Purchase Order' : 'Sales Order'}{' '}
                    Reference
                  </span>
                }
                className="mb-3"
              >
                <SelectV2
                  placeholder={`Select ${type === 'purchase' ? 'PO' : 'SO'}`}
                  options={getOrderOptions()}
                  onChange={(e) => handleOrderSelect(e.target.value)}
                  allowClear
                  className="text-sm"
                />
              </Form.Item>
            </div>
          </div>

          {/* Entity & Amount */}
          <div className="bg-white border border-gray-200 rounded-lg p-3">
            <h3 className="text-sm font-medium text-gray-700 mb-3">
              {type === 'purchase' ? 'Vendor' : 'Customer'} & Amount
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
              <Form.Item
                name={type === 'purchase' ? 'vendor' : 'customer'}
                label={
                  <span className="text-sm font-medium text-gray-600">
                    {type === 'purchase' ? 'Vendor' : 'Customer'}
                  </span>
                }
                rules={[{ required: true }]}
                className="mb-3"
              >
                <SelectV2
                  placeholder="Select"
                  options={getEntityOptions()}
                  className="text-sm"
                />
              </Form.Item>
              <Form.Item
                name="totalAmount"
                label={
                  <span className="text-sm font-medium text-gray-600">
                    Total Amount
                  </span>
                }
                className="mb-3"
              >
                <InputNumber
                  className="w-full text-sm"
                  placeholder="0.00"
                  min={0}
                  precision={2}
                  formatter={(value) =>
                    `₹ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
                  }
                  parser={(value) => value.replace(/₹\s?|(,*)/g, '')}
                />
              </Form.Item>
              <Form.Item
                name="status"
                label={
                  <span className="text-sm font-medium text-gray-600">
                    Status
                  </span>
                }
                rules={[{ required: true }]}
                className="mb-3"
              >
                <SelectV2
                  placeholder="Select status"
                  options={[
                    { value: 'draft', label: 'Draft' },
                    { value: 'sent', label: 'Sent' },
                    { value: 'paid', label: 'Paid' },
                    { value: 'overdue', label: 'Overdue' },
                    { value: 'cancelled', label: 'Cancelled' },
                  ]}
                  className="text-sm"
                />
              </Form.Item>
            </div>
          </div>

          {/* Attachments */}
          <div className="bg-white border border-gray-200 rounded-lg p-3">
            <h3 className="text-sm font-medium text-gray-700 mb-3">
              Attachments
            </h3>
            <Form.Item className="mb-0">
              <div className="space-y-2">
                <UploadButton
                  onChange={handleMediaUpload}
                  multiple
                  accept="image/*,.pdf,.doc,.docx"
                  width="w-full"
                />
                <MediaCardsPreview
                  medias={attachments}
                  onDelete={handleRemoveAttachment}
                />
              </div>
            </Form.Item>
          </div>

          {/* Product Details */}
          <div className="bg-white border border-gray-200 rounded-lg p-3">
            <h3 className="text-sm font-medium text-gray-700 mb-3">
              Product Details
            </h3>
            <StaticProductTable
              input={productInput}
              setInput={setProductInput}
              charges={productCharges}
              setCharges={setProductCharges}
              uomOptions={uomOptions}
            />
          </div>

          {/* Additional Fields */}
          {additionalFields?.templateData?.length > 0 && (
            <div className="bg-white border border-gray-200 rounded-lg p-3">
              <h3 className="text-sm font-medium text-gray-700 mb-3">
                Additional Fields
              </h3>
              <section className="w-full">
                {renderFieldsBasedOnType(
                  additionalFields,
                  handleInputChange,
                  templateDropDownModal,
                  setTemplateDropDownModal,
                  setAdditionalFields,
                  newOptionStatus,
                  setNewOptionStatus,
                  dropdownIdx,
                  setDropdownIdx
                )}
              </section>
            </div>
          )}

          {/* Notes */}
          <div className="bg-white border border-gray-200 rounded-lg p-3">
            <h3 className="text-sm font-medium text-gray-700 mb-3">Notes</h3>
            <Form.Item name="notes" className="mb-0">
              <Textarea
                rows={3}
                placeholder="Enter additional notes or comments"
                className="text-sm resize-none"
              />
            </Form.Item>
          </div>
        </Form>
      </div>

      {/* Footer Actions */}
      <div className="bg-gray-50 px-4 py-3 border-t border-gray-100">
        <div className="flex items-center justify-end gap-2">
          <Button
            onClick={handleBack}
            size="small"
            className="text-sm px-4 py-1 h-8"
          >
            Cancel
          </Button>
          <Button
            type="primary"
            icon={<SaveOutlined />}
            onClick={handleSubmit}
            loading={isSubmitting}
            size="small"
            className="text-sm px-4 py-1 h-8"
          >
            {isEditing ? 'Update' : 'Create'} Invoice
          </Button>
        </div>
      </div>
    </div>
  );
};

export default InvoiceForm;

import { useState } from 'react';
import { useNavigate } from 'react-router-dom';

import { Button, Table, Tag } from 'antd';
import { IoArrowBackCircleSharp } from 'react-icons/io5';
import LeavesModal from '../AttendanceComponents/LeavesModal';

import { customConfirm } from '../../../utils/customConfirm';
import { useHrmsContext } from '../utils/HrmsContext';

import { useGetHolidaysQuery } from '../../../slices/holidayManagementApi';

const LeavesManagement = ({ payroll, setCurrent, current }) => {
  const navigate = useNavigate();
  const [openModal, setOpenModal] = useState(false);
  const [user, setUser] = useState('');
  const [leavesData, setLeavesData] = useState({});
  const [pageSize, setPageSize] = useState(5);

  const { data: holidays } = useGetHolidaysQuery();

  const {
    userDataDuringPayrollRun,
    calculatePayrollStepOneSalary,
    getHoursWorkedInRange,
  } = useHrmsContext();

  function getDaysWorkedInRange(attendance, startDate, endDate) {
    const start = new Date(startDate);
    const end = new Date(endDate);
    let daysWorked = 0;
    let holidaysCount = 0;

    const isHoliday = (date) => {
      return holidays?.some((holiday) => {
        const hStart = new Date(holiday.startDate.$date || holiday.startDate);
        const hEnd = new Date(holiday.endDate.$date || holiday.endDate);
        return date >= hStart && date <= hEnd;
      });
    };

    for (
      let day = new Date(start);
      day <= end;
      day.setDate(day.getDate() + 1)
    ) {
      const dateStr = day.toISOString().split('T')[0];
      const entry = attendance?.[dateStr];

      if (
        entry &&
        Object.keys(entry).length > 0 &&
        entry.isLeave !== true &&
        entry.login &&
        entry.logout
      ) {
        daysWorked++;
      } else if (isHoliday(day)) {
        holidaysCount++;
      }
    }

    return { daysWorked, holidaysCount };
  }

  function getLeaveAttendanceInRange(attendance, startDate, endDate) {
    const start = new Date(startDate);
    const end = new Date(endDate);
    const result = {};

    for (const date in attendance) {
      const current = new Date(date);
      const entry = attendance[date];

      if (
        current >= start &&
        current <= end &&
        entry &&
        entry.isLeave === true
      ) {
        result[date] = entry;
      }
    }
    let temp = [];
    if (result !== undefined) {
      let leavesKey = Object.keys(result);
      for (let i of leavesKey) {
        if (result?.[i]?.isLeave) {
          temp?.push({
            date: i,
            reason: result?.[i]?.leaveReason,
            approvedBy: result?.[i]?.leaveApprovedBy,
          });
        }
      }
    }
    return { result, temp };
  }

  const getFridaysInDateRange = (attendance) => {
    if (attendance !== undefined) {
      const attendanceDates = Object.keys(attendance).sort(); // sorted YYYY-MM-DD
      if (attendanceDates.length > 0) {
        const attendanceStart = new Date(attendanceDates[0]);
        const attendanceEnd = new Date(
          attendanceDates[attendanceDates.length - 1]
        );
        // Count Fridays between earliest and latest attendance dates
        let fridaysCount = 0;
        const current = new Date(attendanceStart);
        while (current <= attendanceEnd) {
          if (current.getDay() === 5) {
            // Friday = 5
            fridaysCount++;
          }
          current.setDate(current.getDate() + 1);
        }

        return fridaysCount;
      }
    }
    return 0;
  };

  const columns = [
    {
      title: 'Name',
      key: 'name',
      render: (_, record) => (
        <p className="text-blue-500 underline cursor-pointer hover:text-blue-400">
          {userDataDuringPayrollRun?.[record?._id]?.name}
        </p>
      ),
    },
    {
      title: 'Email',
      key: 'email',
      render: (_, record) => (
        <Tag color="blue">{userDataDuringPayrollRun?.[record?._id]?.email}</Tag>
      ),
    },
    {
      title: 'Fixed Salary',
      key: 'fixedSalary',
      render: (_, record) => <Tag color="green">₹ {record?.fixedSalary}</Tag>,
    },
    {
      title: 'Days Worked this Month',
      key: 'daysWorkedThisMonth',
      render: (_, record) => (
        <p>
          {
            getDaysWorkedInRange(
              userDataDuringPayrollRun?.[record?._id]?.attendance,
              payroll?.startDate,
              payroll?.endDate,
              holidays,
              userDataDuringPayrollRun?.[record?._id]?.workingHours
            )?.daysWorked
          }{' '}
          +{' '}
          {
            getDaysWorkedInRange(
              userDataDuringPayrollRun?.[record?._id]?.attendance,
              payroll?.startDate,
              payroll?.endDate,
              holidays,
              userDataDuringPayrollRun?.[record?._id]?.workingHours
            )?.holidaysCount
          }{' '}
          {'(Holidays)'}+{' '}
          {getFridaysInDateRange(
            userDataDuringPayrollRun?.[record?._id]?.attendance
          )}{' '}
          {'(Fridays)'}
        </p>
      ),
    },
    {
      title: 'Hours Worked this Month',
      key: 'hoursWorkedThisMonth',
      render: (_, record) => (
        <p>
          {
            getHoursWorkedInRange(
              userDataDuringPayrollRun?.[record?._id]?.attendance,
              payroll?.startDate,
              payroll?.endDate,
              holidays,
              userDataDuringPayrollRun?.[record?._id]?.workingHours
            )?.totalHours
          }{' '}
          +{' '}
          {
            getHoursWorkedInRange(
              userDataDuringPayrollRun?.[record?._id]?.attendance,
              payroll?.startDate,
              payroll?.endDate,
              holidays,
              userDataDuringPayrollRun?.[record?._id]?.workingHours
            )?.holidayHours
          }{' '}
          {'(Holidays)'}+{' '}
          {getFridaysInDateRange(
            userDataDuringPayrollRun?.[record?._id]?.attendance
          ) * userDataDuringPayrollRun?.[record?._id]?.workingHours}{' '}
          {'(Fridays)'}
        </p>
      ),
    },
    {
      title: 'Leaves Assigned',
      key: 'leavesAssigned',
      render: (_, record) => (
        <Tag color="yellow">
          {userDataDuringPayrollRun?.[record?._id]?.leavesAllowed || 0}
        </Tag>
      ),
    },
    {
      title: 'Leaves Taken',
      key: 'leavesTaken',
      render: (_, record) => (
        <Tag color="red">
          {getLeaveAttendanceInRange(
            userDataDuringPayrollRun?.[record?._id]?.attendance,
            payroll?.startDate,
            payroll?.endDate
          )?.temp?.length || 0}
        </Tag>
      ),
    },
    {
      title: 'Work Hours (per Day)',
      key: 'workingHours',
      render: (_, record) => (
        <p>{userDataDuringPayrollRun?.[record?._id]?.workingHours}</p>
      ),
    },
    {
      title: 'Calculated Pay',
      key: 'calculatedPay',
      render: (_, record) => (
        <Tag color="orange">
          ₹{' '}
          {Math.ceil(
            calculatePayrollStepOneSalary(
              record?._id,
              payroll?.startDate,
              payroll?.endDate,
              holidays
            )
          )}
        </Tag>
      ),
    },
    {
      title: '',
      key: 'actions',
      render: (_, record) => (
        <div className="flex items-center gap-2">
          <Button
            className="bg-purple-500 hover:bg-purple-600 text-white"
            onClick={() => {
              let attendance = getLeaveAttendanceInRange(
                userDataDuringPayrollRun?.[record?._id]?.attendance,
                payroll?.startDate,
                payroll?.endDate
              )?.result;
              setLeavesData({
                leavesAllowed: record?.leavesAllowed,
                leavesTaken: record?.leavesTaken,
                leaves: attendance,
                allAttendance:
                  userDataDuringPayrollRun?.[record?._id]?.attendance,
              });
              setOpenModal(true);
              setUser(record?._id);
            }}
          >
            Leaves
          </Button>
        </div>
      ),
    },
  ];

  const handlePaginationSizeChange = (_, pageSize) => {
    setPageSize(pageSize);
  };

  return (
    <>
      <LeavesModal
        openModal={openModal}
        setOpenModal={setOpenModal}
        leavesData={leavesData}
        userId={user}
        isRunningPayroll={true}
        payrollStep={current}
        startDate={payroll?.startDate}
        endDate={payroll?.endDate}
      />
      <div className="bg-white rounded-[10px] pr-2 py-2 mx-2 w-full flex justify-between">
        <div className="flex items-center gap-2">
          <IoArrowBackCircleSharp
            className="text-4xl text-blue-400 cursor-pointer"
            onClick={async () => {
              const confirm = await customConfirm(
                'All unsaved progress will be lost. Are you sure you want to go back?',
                'Delete'
              );
              if (!confirm) return;
              navigate('/hrms/payroll');
            }}
          />
          <h4 className="text-slate-500">Leaves and Reviews</h4>
        </div>
        <div className="flex items-center gap-2">
          <Button
            onClick={async () => {
              // const confirm = await customConfirm(
              //   'All unsaved progress will be lost. Are you sure you want to go to the previous step?',
              //   'Delete'
              // );
              // if (!confirm) return;
              setCurrent((prev) => prev - 1);
            }}
          >
            Previous
          </Button>
          <Button
            variant="solid"
            color="blue"
            onClick={async () => {
              // const confirm = await customConfirm(
              //   'Go to next step?',
              //   'Success'
              // );
              // if (!confirm) return;
              setCurrent((prev) => prev + 1);
            }}
          >
            Next
          </Button>
        </div>
      </div>
      <div className="h-[60vh]">
        <Table
          columns={columns}
          loading={payroll?.users !== undefined ? false : true}
          dataSource={payroll?.users}
          rowKey={(_, index) => index}
          pagination={{
            position: ['bottomRight'],
            pageSize: pageSize,
            showSizeChanger: true,
            onShowSizeChange: handlePaginationSizeChange,
            pageSizeOptions: [5, 10, 20],
          }}
          size="middle"
          scroll={{ x: true }}
          locale={{ emptyText: 'No Employees added yet' }}
        />
      </div>
    </>
  );
};

export default LeavesManagement;

import { CheckOutlined, CloseOutlined } from '@ant-design/icons';
import { Switch } from 'antd';

const Toggle = ({
  className = '',
  disabled = false,
  value = false,
  checked = false,
  onChange,
  name = '',
  size = 'default', // 'small' | 'default'
  loading = false,
  checkedChildren,
  unCheckedChildren,
  showIcons = false,
  ...rest
}) => {
  const val = checked || value;

  const handleChange = (checkedValue) => {
    if (onChange) {
      onChange({
        target: {
          checked: checkedValue,
          value: checkedValue,
          name,
          type: 'toggle',
        },
      });
    }
  };

  return (
    <Switch
      checked={val}
      onChange={handleChange}
      disabled={disabled || loading}
      size={size}
      loading={loading}
      className={`
        ${className}
        ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
      `.trim()}
      checkedChildren={
        showIcons ? (
          <CheckOutlined style={{ fontSize: '12px' }} />
        ) : (
          checkedChildren
        )
      }
      unCheckedChildren={
        showIcons ? (
          <CloseOutlined style={{ fontSize: '12px' }} />
        ) : (
          unCheckedChildren
        )
      }
      {...rest}
    />
  );
};

export default Toggle;
